<?php

declare(strict_types=1);

namespace App\BookStore\Infrastructure\ApiPlatform\State\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\BookStore\Application\Command\AnonymizeBooksCommand;
use App\Shared\Application\Command\CommandBusInterface;
use Web<PERSON>zart\Assert\Assert;

/**
 * @implements ProcessorInterface<null>
 */
final readonly class AnonymizeBooksProcessor implements ProcessorInterface
{
    public function __construct(
        private CommandBusInterface $commandBus,
    ) {
    }

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): null
    {
        Assert::isInstanceOf($data, AnonymizeBooksCommand::class);

        $this->commandBus->dispatch($data);

        return null;
    }
}
