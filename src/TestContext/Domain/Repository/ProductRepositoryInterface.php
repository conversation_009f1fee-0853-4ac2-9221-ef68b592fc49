<?php

declare(strict_types=1);

namespace App\TestContext\Domain\Repository;

use App\TestContext\Domain\Model\Product;
use App\TestContext\Domain\ValueObject\ProductId;

interface ProductRepositoryInterface
{
    public function add(Product $product): void;

    public function remove(Product $product): void;

    public function ofId(ProductId $id): ?Product;

    /**
     * @return Product[]
     */
    public function findAll(): array;
}
