<?php

declare(strict_types=1);

namespace App\TestContext\Domain\Model;

use App\TestContext\Domain\ValueObject\ProductId;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
final class Product
{
    #[ORM\Embedded(columnPrefix: false)]
    private readonly ProductId $id;

    public function __construct(
        // Ajoutez vos Value Objects ici
    ) {
        $this->id = new ProductId();
    }

    public function id(): ProductId
    {
        return $this->id;
    }

    // Ajoutez vos méthodes métier ici
}
