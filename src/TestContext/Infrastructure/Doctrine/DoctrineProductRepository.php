<?php

declare(strict_types=1);

namespace App\TestContext\Infrastructure\Doctrine;

use App\Shared\Infrastructure\Doctrine\DoctrineRepository;
use App\TestContext\Domain\Model\Product;
use App\TestContext\Domain\Repository\ProductRepositoryInterface;
use App\TestContext\Domain\ValueObject\ProductId;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @extends DoctrineRepository<Product>
 */
final class DoctrineProductRepository extends DoctrineRepository implements ProductRepositoryInterface
{
    private const ENTITY_CLASS = Product::class;
    private const ALIAS = 'product';

    public function __construct(EntityManagerInterface $em)
    {
        parent::__construct($em, self::ENTITY_CLASS, self::ALIAS);
    }

    public function add(Product $product): void
    {
        $this->em->persist($product);
    }

    public function remove(Product $product): void
    {
        $this->em->remove($product);
    }

    public function ofId(ProductId $id): ?Product
    {
        return $this->em->find(self::ENTITY_CLASS, $id->value);
    }

    /**
     * @return Product[]
     */
    public function findAll(): array
    {
        return $this->em->getRepository(self::ENTITY_CLASS)->findAll();
    }
}
