<?php

declare(strict_types=1);

namespace App\Shared\Infrastructure\Maker;

use Symfony\Bundle\MakerBundle\ConsoleStyle;
use Symfony\Bundle\MakerBundle\DependencyBuilder;
use Symfony\Bundle\MakerBundle\Generator;
use Symfony\Bundle\MakerBundle\InputConfiguration;
use Symfony\Bundle\MakerBundle\Maker\AbstractMaker;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;

final class MakeDDDValueObject extends AbstractMaker
{
    public static function getCommandName(): string
    {
        return 'make:ddd:value-object';
    }

    public static function getCommandDescription(): string
    {
        return 'Creates a new DDD Value Object';
    }

    public function configureCommand(Command $command, InputConfiguration $inputConfig): void
    {
        $command
            ->addArgument('bounded-context', InputArgument::REQUIRED, 'The bounded context (e.g. BookStore)')
            ->addArgument('value-object-name', InputArgument::REQUIRED, 'The Value Object name (e.g. BookName)')
            ->addOption('type', 't', InputOption::VALUE_REQUIRED, 'Value Object type (string, int, float, bool)', 'string')
            ->addOption('embeddable', null, InputOption::VALUE_NONE, 'Make it Doctrine embeddable')
            ->setHelp('This command creates a new DDD Value Object in the correct directory structure.');
    }

    public function generate(InputInterface $input, ConsoleStyle $io, Generator $generator): void
    {
        $boundedContext = $input->getArgument('bounded-context');
        $valueObjectName = $input->getArgument('value-object-name');
        $type = $input->getOption('type');
        $embeddable = $input->getOption('embeddable');

        $this->generateValueObject($generator, $boundedContext, $valueObjectName, $type, $embeddable);

        $generator->writeChanges();

        $io->success(sprintf('DDD Value Object "%s" created successfully in %s context!', $valueObjectName, $boundedContext));
    }

    public function configureDependencies(DependencyBuilder $dependencies): void
    {
        // Pas de dépendances spéciales requises
    }

    private function generateValueObject(Generator $generator, string $boundedContext, string $valueObjectName, string $type, bool $embeddable): void
    {
        $valueObjectPath = sprintf('src/%s/Domain/ValueObject/%s.php', $boundedContext, $valueObjectName);

        $generator->generateFile(
            $valueObjectPath,
            __DIR__ . '/../../../../templates/maker/ddd/ValueObject.tpl.php',
            [
                'bounded_context' => $boundedContext,
                'value_object_name' => $valueObjectName,
                'type' => $type,
                'embeddable' => $embeddable,
                'column_name' => strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $valueObjectName)),
            ]
        );
    }
}
