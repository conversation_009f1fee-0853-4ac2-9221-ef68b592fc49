<?php

declare(strict_types=1);

namespace App\Shared\Infrastructure\Maker;

use Symfony\Bundle\MakerBundle\ConsoleStyle;
use Symfony\Bundle\MakerBundle\DependencyBuilder;
use Symfony\Bundle\MakerBundle\Generator;
use Symfony\Bundle\MakerBundle\InputConfiguration;
use Symfony\Bundle\MakerBundle\Maker\AbstractMaker;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;

final class MakeDDDEntity extends AbstractMaker
{
    public static function getCommandName(): string
    {
        return 'make:ddd:entity';
    }

    public static function getCommandDescription(): string
    {
        return 'Creates a new DDD entity with repository interface and implementation';
    }

    public function configureCommand(Command $command, InputConfiguration $inputConfig): void
    {
        $command
            ->addArgument('bounded-context', InputArgument::REQUIRED, 'The bounded context (e.g. BookStore)')
            ->addArgument('entity-name', InputArgument::REQUIRED, 'The entity name (e.g. Book)')
            ->addOption('with-repository', null, InputOption::VALUE_NONE, 'Generate repository interface and implementation')
            ->setHelp('This command creates a new DDD entity in the correct directory structure.');
    }

    public function generate(InputInterface $input, ConsoleStyle $io, Generator $generator): void
    {
        $boundedContext = $input->getArgument('bounded-context');
        $entityName = $input->getArgument('entity-name');
        $withRepository = $input->getOption('with-repository');

        // Générer le Value Object ID
        $this->generateValueObjectId($generator, $boundedContext, $entityName);

        // Générer l'entité
        $this->generateEntity($generator, $boundedContext, $entityName);

        if ($withRepository) {
            // Générer l'interface du repository
            $this->generateRepositoryInterface($generator, $boundedContext, $entityName);

            // Générer l'implémentation Doctrine
            $this->generateDoctrineRepository($generator, $boundedContext, $entityName);
        }

        $generator->writeChanges();

        $io->success(sprintf('DDD Entity "%s" created successfully in %s context!', $entityName, $boundedContext));

        $io->note([
            'Generated files:',
            sprintf('- %sId Value Object', $entityName),
            sprintf('- %s Entity', $entityName),
        ]);

        if ($withRepository) {
            $io->note([
                'Repository files:',
                sprintf('- %sRepositoryInterface', $entityName),
                sprintf('- Doctrine%sRepository', $entityName),
                '',
                'Don\'t forget to configure the repository binding in config/services/' . strtolower($boundedContext) . '.php'
            ]);
        }
    }

    public function configureDependencies(DependencyBuilder $dependencies): void
    {
        $dependencies->addClassDependency('Doctrine\\ORM\\Mapping\\Entity', 'orm');
    }

    private function generateEntity(Generator $generator, string $boundedContext, string $entityName): void
    {
        $entityPath = sprintf('src/%s/Domain/Model/%s.php', $boundedContext, $entityName);

        $generator->generateFile(
            $entityPath,
            __DIR__ . '/../../../../templates/maker/ddd/Entity.tpl.php',
            [
                'bounded_context' => $boundedContext,
                'entity_name' => $entityName,
                'entity_id_name' => $entityName . 'Id',
            ]
        );
    }

    private function generateRepositoryInterface(Generator $generator, string $boundedContext, string $entityName): void
    {
        $repositoryPath = sprintf('src/%s/Domain/Repository/%sRepositoryInterface.php', $boundedContext, $entityName);

        $generator->generateFile(
            $repositoryPath,
            __DIR__ . '/../../../../templates/maker/ddd/RepositoryInterface.tpl.php',
            [
                'bounded_context' => $boundedContext,
                'entity_name' => $entityName,
                'entity_id_name' => $entityName . 'Id',
                'repository_alias' => strtolower($entityName),
            ]
        );
    }

    private function generateDoctrineRepository(Generator $generator, string $boundedContext, string $entityName): void
    {
        $repositoryPath = sprintf('src/%s/Infrastructure/Doctrine/Doctrine%sRepository.php', $boundedContext, $entityName);

        $generator->generateFile(
            $repositoryPath,
            __DIR__ . '/../../../../templates/maker/ddd/DoctrineRepository.tpl.php',
            [
                'bounded_context' => $boundedContext,
                'entity_name' => $entityName,
                'entity_id_name' => $entityName . 'Id',
                'repository_alias' => strtolower($entityName),
            ]
        );
    }

    private function generateValueObjectId(Generator $generator, string $boundedContext, string $entityName): void
    {
        $valueObjectPath = sprintf('src/%s/Domain/ValueObject/%sId.php', $boundedContext, $entityName);

        $generator->generateFile(
            $valueObjectPath,
            __DIR__ . '/../../../../templates/maker/ddd/ValueObjectId.tpl.php',
            [
                'bounded_context' => $boundedContext,
                'entity_name' => $entityName,
                'entity_id_name' => $entityName . 'Id',
            ]
        );
    }
}
