# EditorConfig helps developers define and maintain consistent
# coding styles between different editors and IDEs
# editorconfig.org

root = true

[*]
# Change these settings to your own preference
indent_style = space
indent_size = 4

# We recommend you to keep these unchanged
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

[*.{js,html}]
indent_style = space
indent_size = 2

[*.json]
indent_style = space
indent_size = 2

[*.md]
trim_trailing_whitespace = false

[*.php]
indent_style = space
indent_size = 4

[*.sh]
indent_style = tab
indent_size = 4

[*.xml{,.dist}]
indent_style = space
indent_size = 4

[*.{yaml,yml}]
indent_style = space
indent_size = 4
trim_trailing_whitespace = false

[api/helm/api/**.yaml]
indent_style = space
indent_size = 2

[.github/workflows/*.yml]
indent_style = space
indent_size = 2

[.gitmodules]
indent_style = tab
indent_size = 4

[.php_cs{,.dist}]
indent_style = space
indent_size = 4

[.travis.yml]
indent_style = space
indent_size = 2

[composer.json]
indent_style = space
indent_size = 4

[docker-compose{,.*}.{yaml,yml}]
indent_style = space
indent_size = 2

[Dockerfile]
indent_style = tab
indent_size = 4
