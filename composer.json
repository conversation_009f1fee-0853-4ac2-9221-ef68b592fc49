{"type": "project", "license": "MIT", "prefer-stable": true, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "api-platform/core": "^3.2", "doctrine/doctrine-bundle": "^2.9", "doctrine/orm": "^2.15", "nelmio/cors-bundle": "^2.3", "phpdocumentor/reflection-docblock": "^5.3", "phpstan/phpdoc-parser": "^1.2", "symfony/asset": "7.0.*", "symfony/console": "7.0.*", "symfony/dotenv": "7.0.*", "symfony/expression-language": "7.0.*", "symfony/flex": "^2", "symfony/framework-bundle": "7.0.*", "symfony/messenger": "7.0.*", "symfony/monolog-bundle": "^3.8", "symfony/property-access": "7.0.*", "symfony/property-info": "7.0.*", "symfony/runtime": "7.0.*", "symfony/security-bundle": "7.0.*", "symfony/serializer": "7.0.*", "symfony/string": "*", "symfony/twig-bundle": "7.0.*", "symfony/uid": "7.0.*", "symfony/validator": "7.0.*", "symfony/yaml": "7.0.*", "webmozart/assert": "^1.11"}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "symfony/runtime": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.0.*", "docker": true}}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.17", "justinrainbow/json-schema": "^5.2", "phpunit/phpunit": "^9.6", "qossmic/deptrac-shim": "^1.0", "symfony/browser-kit": "7.0.*", "symfony/css-selector": "7.0.*", "symfony/debug-bundle": "7.0.*", "symfony/http-client": "7.0.*", "symfony/maker-bundle": "^1.62", "symfony/phpunit-bridge": "7.0.*", "symfony/stopwatch": "7.0.*", "symfony/web-profiler-bundle": "7.0.*", "vimeo/psalm": "^5.12"}}