# 9. Configuration Symfony pour DDD

## 🎯 Objectifs d'apprentissage

À la fin de ce chapitre, vous serez capable de :
- Configurer l'injection de dépendances pour DDD
- Organiser les services par Bounded Context
- Configurer Doctrine pour les Value Objects
- Mettre en place Symfony Messenger pour CQRS
- Optimiser l'auto-configuration des services

## 🏗️ Architecture de Configuration

### Organisation des Fichiers

```
config/
├── packages/                    ← Configuration des bundles
│   ├── doctrine.php
│   ├── messenger.php
│   └── api_platform.php
├── routes/                      ← Routes par environnement
│   ├── dev/
│   └── api_platform.php
├── services/                    ← Services par contexte
│   ├── shared.php               ← Services partagés
│   ├── book_store.php           ← Services BookStore
│   ├── subscription.php         ← Services Subscription
│   └── test/                    ← Configuration test
│       ├── book_store.php
│       └── subscription.php
└── services.php                 ← Configuration globale
```

## ⚙️ Configuration des Services

### Configuration Globale

```php
<?php
// config/services.php

use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services();

    // Configuration par défaut
    $services->defaults()
        ->autowire()      // Injection automatique
        ->autoconfigure() // Tags automatiques
        ->private();      // Services privés par défaut

    // Auto-registration des services partagés
    $services->load('App\\Shared\\', '../src/Shared')
        ->exclude([
            '../src/Shared/Infrastructure/Symfony/Kernel.php',
            '../src/Shared/Domain/ValueObject/',
        ]);

    // Interfaces publiques pour les tests
    $services->alias('test.command_bus', 'App\\Shared\\Application\\Command\\CommandBusInterface')
        ->public();
    
    $services->alias('test.query_bus', 'App\\Shared\\Application\\Query\\QueryBusInterface')
        ->public();
};
```

### Configuration par Bounded Context

```php
<?php
// config/services/book_store.php

use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Infrastructure\ApiPlatform\State\Processor\CreateBookProcessor;
use App\BookStore\Infrastructure\ApiPlatform\State\Provider\BookItemProvider;
use App\BookStore\Infrastructure\Doctrine\DoctrineBookRepository;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services();

    $services->defaults()
        ->autowire()
        ->autoconfigure();

    // Auto-registration de tous les services du contexte
    $services->load('App\\BookStore\\', dirname(__DIR__, 2).'/src/BookStore')
        ->exclude([
            dirname(__DIR__, 2).'/src/BookStore/Domain/ValueObject/',
            dirname(__DIR__, 2).'/src/BookStore/Domain/Exception/',
        ]);

    // Configuration spécifique des repositories
    $services->set(BookRepositoryInterface::class)
        ->class(DoctrineBookRepository::class);

    // Configuration des API Platform State Providers/Processors
    $services->set(BookItemProvider::class)
        ->autoconfigure(false)
        ->tag('api_platform.state_provider', ['priority' => 0]);

    $services->set(CreateBookProcessor::class)
        ->autoconfigure(false)
        ->tag('api_platform.state_processor', ['priority' => 0]);

    // Services publics pour les tests
    $services->set('test.book_repository', BookRepositoryInterface::class)
        ->public();
};
```

### Configuration pour les Tests

```php
<?php
// config/services/test/book_store.php

use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Infrastructure\InMemory\InMemoryBookRepository;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services();

    $services->defaults()
        ->autowire()
        ->autoconfigure();

    // Remplacer le repository par la version en mémoire
    $services->set(BookRepositoryInterface::class)
        ->class(InMemoryBookRepository::class);

    // Rendre le repository accessible dans les tests
    $services->set(InMemoryBookRepository::class)
        ->public();

    // Alias pour faciliter l'accès
    $services->alias('test.book_repository', InMemoryBookRepository::class)
        ->public();
};
```

## 🗃️ Configuration Doctrine

### Mapping des Entités

```php
<?php
// config/packages/doctrine.php

use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $containerConfigurator->extension('doctrine', [
        'dbal' => [
            'url' => '%env(resolve:DATABASE_URL)%',
            'profiling_collect_backtrace' => '%kernel.debug%',
            'use_savepoints' => true,
        ],
        'orm' => [
            'auto_generate_proxy_classes' => true,
            'enable_lazy_ghost_objects' => true,
            'naming_strategy' => 'doctrine.orm.naming_strategy.underscore_number_aware',
            'auto_mapping' => true,
            'mappings' => [
                // Mapping par Bounded Context
                'BookStore' => [
                    'is_bundle' => false,
                    'type' => 'attribute',
                    'dir' => '%kernel.project_dir%/src/BookStore/Domain',
                    'prefix' => 'App\BookStore\Domain',
                ],
                'Subscription' => [
                    'is_bundle' => false,
                    'type' => 'attribute',
                    'dir' => '%kernel.project_dir%/src/Subscription/Entity',
                    'prefix' => 'App\Subscription\Entity',
                ],
                'Shared' => [
                    'is_bundle' => false,
                    'type' => 'attribute',
                    'dir' => '%kernel.project_dir%/src/Shared/Domain',
                    'prefix' => 'App\Shared\Domain',
                ],
            ],
        ],
    ]);
};
```

### Types Doctrine Personnalisés

```php
<?php
// config/packages/doctrine_types.php

use App\BookStore\Infrastructure\Doctrine\Type\BookIdType;
use App\BookStore\Infrastructure\Doctrine\Type\PriceType;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $containerConfigurator->extension('doctrine', [
        'dbal' => [
            'types' => [
                'book_id' => BookIdType::class,
                'price' => PriceType::class,
            ],
        ],
    ]);
};
```

## 📨 Configuration Messenger (CQRS)

### Configuration des Bus

```php
<?php
// config/packages/messenger.php

use App\Shared\Application\Command\CommandInterface;
use App\Shared\Application\Query\QueryInterface;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $containerConfigurator->extension('framework', [
        'messenger' => [
            'default_bus' => 'command.bus',
            'buses' => [
                'command.bus' => [
                    'middleware' => [
                        'validation',           // Validation des données
                        'doctrine_transaction', // Transaction automatique
                    ],
                ],
                'query.bus' => [
                    'middleware' => [
                        'validation', // Validation des queries
                    ],
                ],
            ],
            'routing' => [
                // Router les commands vers le command bus
                CommandInterface::class => 'command.bus',
                // Router les queries vers le query bus
                QueryInterface::class => 'query.bus',
            ],
        ],
    ]);
};
```

### Configuration des Services Messenger

```php
<?php
// config/services/messenger.php

use App\Shared\Application\Command\CommandBusInterface;
use App\Shared\Application\Query\QueryBusInterface;
use App\Shared\Infrastructure\Symfony\Messenger\MessengerCommandBus;
use App\Shared\Infrastructure\Symfony\Messenger\MessengerQueryBus;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;
use Symfony\Component\DependencyInjection\Reference;

return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services();

    // Command Bus
    $services->set(CommandBusInterface::class)
        ->class(MessengerCommandBus::class)
        ->args([new Reference('command.bus')]);

    // Query Bus
    $services->set(QueryBusInterface::class)
        ->class(MessengerQueryBus::class)
        ->args([new Reference('query.bus')]);
};
```

## 🏷️ Auto-configuration des Handlers

### Configuration dans le Kernel

```php
<?php
// src/Shared/Infrastructure/Symfony/Kernel.php

namespace App\Shared\Infrastructure\Symfony;

use App\Shared\Application\Command\AsCommandHandler;
use App\Shared\Application\Query\AsQueryHandler;
use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
use Symfony\Component\DependencyInjection\ChildDefinition;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\HttpKernel\Kernel as BaseKernel;

class Kernel extends BaseKernel
{
    use MicroKernelTrait;

    protected function build(ContainerBuilder $container): void
    {
        // Auto-configuration des Query Handlers
        $container->registerAttributeForAutoconfiguration(
            AsQueryHandler::class,
            static function (ChildDefinition $definition): void {
                $definition->addTag('messenger.message_handler', ['bus' => 'query.bus']);
            }
        );

        // Auto-configuration des Command Handlers
        $container->registerAttributeForAutoconfiguration(
            AsCommandHandler::class,
            static function (ChildDefinition $definition): void {
                $definition->addTag('messenger.message_handler', ['bus' => 'command.bus']);
            }
        );
    }
}
```

### Attributs pour les Handlers

```php
<?php
// src/Shared/Application/Command/AsCommandHandler.php

namespace App\Shared\Application\Command;

#[\Attribute(\Attribute::TARGET_CLASS)]
final class AsCommandHandler
{
    // Attribut marker pour l'auto-configuration
}
```

```php
<?php
// src/Shared/Application/Query/AsQueryHandler.php

namespace App\Shared\Application\Query;

#[\Attribute(\Attribute::TARGET_CLASS)]
final class AsQueryHandler
{
    // Attribut marker pour l'auto-configuration
}
```

## 🔧 Configuration API Platform

### Configuration Globale

```php
<?php
// config/packages/api_platform.php

use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $containerConfigurator->extension('api_platform', [
        'title' => 'BookStore API',
        'version' => '1.0.0',
        'description' => 'DDD BookStore API with CQRS',
        
        'formats' => [
            'jsonld' => ['application/ld+json'],
            'json' => ['application/json'],
        ],
        
        'docs_formats' => [
            'jsonld' => ['application/ld+json'],
            'json' => ['application/json'],
            'html' => ['text/html'],
        ],
        
        'defaults' => [
            'stateless' => true,
            'cache_headers' => [
                'vary' => ['Content-Type', 'Authorization', 'Origin'],
            ],
        ],
        
        'exception_to_status' => [
            'App\\BookStore\\Domain\\Exception\\MissingBookException' => 404,
            'App\\Shared\\Domain\\Exception\\ValidationException' => 400,
        ],
    ]);
};
```

### Configuration des Resources

```php
<?php
// Dans vos Resources API Platform

namespace App\BookStore\Infrastructure\ApiPlatform\Resource;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Post;
use ApiPlatform\Metadata\Patch;
use App\BookStore\Infrastructure\ApiPlatform\State\Provider\BookItemProvider;
use App\BookStore\Infrastructure\ApiPlatform\State\Processor\CreateBookProcessor;

#[ApiResource(
    operations: [
        new Get(provider: BookItemProvider::class),
        new GetCollection(),
        new Post(processor: CreateBookProcessor::class),
        new Patch(
            uriTemplate: '/books/{id}/discount',
            processor: DiscountBookProcessor::class
        ),
    ],
    normalizationContext: ['groups' => ['book:read']],
    denormalizationContext: ['groups' => ['book:write']],
)]
class BookResource
{
    // Propriétés et méthodes...
}
```

## 🌍 Configuration par Environnement

### Environnement de Développement

```php
<?php
// config/packages/dev/doctrine.php

use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $containerConfigurator->extension('doctrine', [
        'orm' => [
            'auto_generate_proxy_classes' => true,
            'enable_lazy_ghost_objects' => true,
        ],
        'dbal' => [
            'logging' => true,
            'profiling_collect_backtrace' => true,
        ],
    ]);
};
```

### Environnement de Test

```php
<?php
// config/packages/test/doctrine.php

use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $containerConfigurator->extension('doctrine', [
        'dbal' => [
            'url' => 'sqlite:///:memory:',
            'logging' => false,
            'profiling_collect_backtrace' => false,
        ],
    ]);
};
```

## 🔍 Debug et Profiling

### Configuration du Profiler

```php
<?php
// config/packages/dev/web_profiler.php

use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $containerConfigurator->extension('web_profiler', [
        'toolbar' => true,
        'intercept_redirects' => false,
    ]);

    $containerConfigurator->extension('framework', [
        'profiler' => [
            'only_exceptions' => false,
            'collect_serializer_data' => true,
        ],
    ]);
};
```

### Commandes de Debug

```bash
# Lister tous les services
bin/console debug:container

# Voir la configuration d'un service
bin/console debug:container BookRepositoryInterface

# Voir les routes API Platform
bin/console debug:router

# Voir la configuration Doctrine
bin/console debug:doctrine:mapping

# Voir les handlers Messenger
bin/console debug:messenger
```

## 🎨 Bonnes Pratiques

### ✅ À Faire
- **Séparer** la configuration par Bounded Context
- **Utiliser** l'auto-configuration quand possible
- **Configurer** différemment selon l'environnement
- **Rendre publics** les services nécessaires aux tests
- **Documenter** les configurations complexes

### ❌ À Éviter
- Configuration monolithique dans un seul fichier
- Services publics inutiles
- Configuration dupliquée entre environnements
- Couplage fort dans la configuration
- Configuration en dur sans variables d'environnement

## 🚀 Prochaines Étapes

Maintenant que vous maîtrisez la configuration :
1. Mise en pratique avec un exercice complet
2. Optimisation des performances
3. Déploiement et production
4. Monitoring et observabilité

## 💡 Points Clés à Retenir

- **Organisez** la configuration par Bounded Context
- **Utilisez** l'injection de dépendances pour l'inversion de contrôle
- **Configurez** Doctrine pour les Value Objects
- **Mettez en place** Messenger pour CQRS
- **Adaptez** la configuration selon l'environnement
- **Rendez** les services testables

---

**Exercice pratique** : Configurez un nouveau Bounded Context "Library" avec ses propres services, repositories, et handlers, en suivant les patterns établis.
