# Guide Complet des Services Symfony

## Table des Matières
1. [Introduction aux Services](#introduction-aux-services)
2. [Configuration Automatique vs Services Personnalisés](#configuration-automatique-vs-services-personnalisés)
3. [Autowiring et Autoconfiguration](#autowiring-et-autoconfiguration)
4. [Services Personnalisés](#services-personnalisés)
5. [Tags et Priorités](#tags-et-priorités)
6. [Exemples Pratiques](#exemples-pratiques)
7. [Bonnes Pratiques](#bonnes-pratiques)

## Introduction aux Services

### Qu'est-ce qu'un Service ?

Un **service** dans Symfony est un objet PHP qui effectue une tâche spécifique dans votre application. Le conteneur de services (Service Container) de Symfony gère la création, la configuration et l'injection de ces services.

**Exemples de services :**
- Repository pour accéder aux données
- Service d'envoi d'emails
- Logger
- Validator
- Processeur API Platform

### Le Conteneur de Services (DI Container)

Le conteneur de services est le cœur de Symfony. Il :
- **Instancie** les services automatiquement
- **Injecte** les dépendances
- **Gère** le cycle de vie des objets
- **Configure** les services selon vos besoins

## Configuration Automatique vs Services Personnalisés

### 1. Configuration Automatique (services.config)

Symfony 4+ utilise l'**autowiring** et l'**autoconfiguration** par défaut :

```php
// config/services/shared.php (exemple de votre projet)
$services->defaults()
    ->autowire()      // Injection automatique des dépendances
    ->autoconfigure(); // Configuration automatique des tags

$services->load('App\\Shared\\', dirname(__DIR__, 2).'/src/Shared');
```

**Avantages :**
- ✅ Configuration minimale
- ✅ Convention over configuration
- ✅ Détection automatique des services

**Inconvénients :**
- ❌ Moins de contrôle fin
- ❌ Peut masquer la complexité

### 2. Services Personnalisés (Custom Services)

Pour un contrôle précis, vous définissez manuellement vos services :

```php
// config/services/book_store.php (votre exemple)
$services->set(BookRepositoryInterface::class)
    ->class(DoctrineBookRepository::class);

$services->set(CheapestBooksProvider::class)
    ->autoconfigure(false)
    ->tag('api_platform.state_provider', ['priority' => 1]);
```

**Avantages :**
- ✅ Contrôle total de la configuration
- ✅ Gestion fine des priorités et tags
- ✅ Interface binding explicite

**Inconvénients :**
- ❌ Plus de configuration manuelle
- ❌ Maintenance plus complexe

## Autowiring et Autoconfiguration

### Autowiring

L'**autowiring** résout automatiquement les dépendances en analysant les type-hints :

```php
class BookService
{
    public function __construct(
        private BookRepositoryInterface $bookRepository,  // Injecté automatiquement
        private LoggerInterface $logger                   // Injecté automatiquement
    ) {}
}
```

### Autoconfiguration

L'**autoconfiguration** applique automatiquement les tags basés sur les interfaces :

```php
// Cette classe sera automatiquement taguée comme 'console.command'
class CreateBookCommand extends Command
{
    // ...
}
```

### Désactiver l'Autoconfiguration

Parfois, vous devez désactiver l'autoconfiguration pour un contrôle manuel :

```php
$services->set(CheapestBooksProvider::class)
    ->autoconfigure(false)  // Désactive l'autoconfiguration
    ->tag('api_platform.state_provider', ['priority' => 1]);
```

## Services Personnalisés

### 1. Binding d'Interface

Lier une interface à une implémentation concrète :

```php
// Votre exemple : BookRepositoryInterface → DoctrineBookRepository
$services->set(BookRepositoryInterface::class)
    ->class(DoctrineBookRepository::class);
```

### 2. Configuration avec Arguments

```php
$services->set(EmailService::class)
    ->arg('$smtpHost', '%env(SMTP_HOST)%')
    ->arg('$smtpPort', '%env(int:SMTP_PORT)%');
```

### 3. Services avec Factory

```php
$services->set(ComplexService::class)
    ->factory([ComplexServiceFactory::class, 'create'])
    ->arg('$config', '%app.complex_config%');
```

## Tags et Priorités

### Qu'est-ce qu'un Tag ?

Un **tag** est un label qui identifie un service pour un usage spécifique. Symfony utilise les tags pour :
- Collecter des services similaires
- Appliquer des comportements spécifiques
- Définir des priorités d'exécution

### Exemples de Tags dans votre Projet

```php
// State Providers avec priorités
$services->set(CheapestBooksProvider::class)
    ->tag('api_platform.state_provider', ['priority' => 1]);  // Priorité haute

$services->set(BookItemProvider::class)
    ->tag('api_platform.state_provider', ['priority' => 0]);  // Priorité normale
```

### Tags Courants

| Tag | Usage |
|-----|-------|
| `api_platform.state_provider` | Fournisseur de données API Platform |
| `api_platform.state_processor` | Processeur de données API Platform |
| `console.command` | Commande console |
| `doctrine.event_listener` | Écouteur d'événements Doctrine |
| `kernel.event_listener` | Écouteur d'événements Kernel |

## Exemples Pratiques

### Exemple 1 : Service Simple avec Autowiring

```php
// src/BookStore/Application/Service/BookPriceCalculator.php
class BookPriceCalculator
{
    public function __construct(
        private TaxCalculatorInterface $taxCalculator
    ) {}

    public function calculateFinalPrice(Book $book): Money
    {
        return $this->taxCalculator->addTax($book->getPrice());
    }
}
```

**Configuration automatique :** Aucune configuration nécessaire !

### Exemple 2 : Service avec Configuration Personnalisée

```php
// config/services/book_store.php
$services->set(BookNotificationService::class)
    ->arg('$adminEmail', '%env(ADMIN_EMAIL)%')
    ->arg('$enableNotifications', '%env(bool:ENABLE_NOTIFICATIONS)%')
    ->tag('app.notification_service');

### Exemple 3 : Multiple Providers avec Priorités (votre cas d'usage)

```php
// Providers avec priorités différentes
$services->set(CheapestBooksProvider::class)
    ->autoconfigure(false)
    ->tag('api_platform.state_provider', ['priority' => 1]);  // Exécuté en premier

$services->set(BookCollectionProvider::class)
    ->autoconfigure(false)
    ->tag('api_platform.state_provider', ['priority' => 0]);  // Exécuté après
```

**Logique :** API Platform choisit le provider avec la priorité la plus élevée qui peut traiter la requête.

## Bonnes Pratiques

### 1. Préférez l'Autowiring pour les Services Simples

```php
// ✅ Bon : Service simple avec autowiring
class BookService
{
    public function __construct(
        private BookRepositoryInterface $repository,
        private EventDispatcherInterface $eventDispatcher
    ) {}
}
```

### 2. Utilisez la Configuration Manuelle pour les Cas Complexes

```php
// ✅ Bon : Configuration manuelle pour contrôle fin
$services->set(ComplexApiProvider::class)
    ->autoconfigure(false)
    ->tag('api_platform.state_provider', [
        'priority' => 10,
        'key' => 'complex_books'
    ]);
```

### 3. Organisez vos Services par Domaine

```php
// Structure de votre projet (excellente pratique)
config/services/
├── book_store.php      // Services du domaine BookStore
├── subscription.php    // Services du domaine Subscription
└── shared.php         // Services partagés
```

### 4. Utilisez des Interfaces pour l'Injection de Dépendances

```php
// ✅ Bon : Injection d'interface
public function __construct(private BookRepositoryInterface $repository) {}

// ❌ Évitez : Injection de classe concrète
public function __construct(private DoctrineBookRepository $repository) {}
```

### 5. Gérez les Priorités de Manière Logique

```php
// Priorités logiques pour vos providers
$services->set(CheapestBooksProvider::class)      // priority: 1 (spécialisé)
$services->set(BookItemProvider::class)           // priority: 0 (générique)
$services->set(BookCollectionProvider::class)     // priority: 0 (générique)
```

## Debugging des Services

### Commandes Utiles

```bash
# Lister tous les services
php bin/console debug:container

# Chercher un service spécifique
php bin/console debug:container book

# Voir la configuration d'un service
php bin/console debug:container App\BookStore\Infrastructure\Doctrine\DoctrineBookRepository

# Voir tous les services avec un tag
php bin/console debug:container --tag=api_platform.state_provider
```

### Vérifier l'Autowiring

```bash
# Voir quels services peuvent être injectés
php bin/console debug:autowiring

# Chercher un type spécifique
php bin/console debug:autowiring BookRepositoryInterface
```

## Comparaison : Votre Configuration vs Configuration Standard

### Votre Approche (Recommandée pour DDD)

```php
// config/services/book_store.php
$services->load('App\\BookStore\\', dirname(__DIR__, 2).'/src/BookStore');

// Configuration explicite des interfaces
$services->set(BookRepositoryInterface::class)
    ->class(DoctrineBookRepository::class);

// Contrôle fin des providers API Platform
$services->set(CheapestBooksProvider::class)
    ->autoconfigure(false)
    ->tag('api_platform.state_provider', ['priority' => 1]);
```

**Avantages de votre approche :**
- ✅ Séparation claire par domaine métier
- ✅ Contrôle explicite des priorités API Platform
- ✅ Binding d'interfaces explicite (bon pour DDD)
- ✅ Configuration modulaire et maintenable

### Configuration Standard Symfony

```yaml
# config/services.yaml (approche classique)
services:
    _defaults:
        autowire: true
        autoconfigure: true

    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'
```

**Votre approche est plus adaptée pour :**
- Applications DDD complexes
- Projets avec API Platform
- Besoins de configuration fine
- Équipes expérimentées

## Conclusion

Votre configuration actuelle suit les meilleures pratiques pour une architecture DDD avec API Platform :

1. **Séparation par domaine** : Chaque bounded context a sa propre configuration
2. **Contrôle explicite** : Priorités et tags définis manuellement
3. **Interface binding** : Liaison explicite interface → implémentation
4. **Modularité** : Configuration organisée et maintenable

Cette approche offre plus de contrôle que l'autowiring pur, ce qui est essentiel pour des applications complexes avec des besoins spécifiques comme les vôtres.
```
