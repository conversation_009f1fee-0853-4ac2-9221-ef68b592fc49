# 10. Mise en Pratique - Créer un Bounded Context "Library"

## 🎯 Objectifs de l'Exercice

Dans ce chapitre pratique, vous allez créer de A à Z un nouveau Bounded Context "Library" qui gère les emprunts de livres. Cet exercice vous permettra de :

- Appliquer tous les concepts DDD appris
- Créer des Value Objects, Entités et Agrégats
- Implémenter CQRS avec Commands et Queries
- Écrire des tests unitaires et d'intégration
- Configurer les services Symfony

## 📋 Cahier des Charges

### Fonctionnalités à Implémenter

1. **Gestion des Membres** : Inscription et gestion des membres de la bibliothèque
2. **Emprunts de Livres** : Un membre peut emprunter jusqu'à 3 livres simultanément
3. **Retours** : Gestion des retours avec calcul d'amendes pour retard
4. **Réservations** : Possibilité de réserver un livre déjà emprunté

### Règles Métier

- ✅ Un membre peut emprunter maximum **3 livres** simultanément
- ✅ Durée d'emprunt : **14 jours** maximum
- ✅ Amende : **0,50€ par jour** de retard
- ✅ Un livre en retard bloque les nouveaux emprunts
- ✅ Un livre peut être réservé par **maximum 3 personnes**

## 🏗️ Étape 1 : Structure du Bounded Context

### Créer la Structure de Dossiers

```
src/Library/
├── Domain/
│   ├── Model/
│   │   ├── Member.php
│   │   ├── Loan.php
│   │   └── Reservation.php
│   ├── ValueObject/
│   │   ├── MemberId.php
│   │   ├── LoanId.php
│   │   ├── MemberEmail.php
│   │   ├── LoanPeriod.php
│   │   ├── Fine.php
│   │   └── ReservationId.php
│   ├── Repository/
│   │   ├── MemberRepositoryInterface.php
│   │   ├── LoanRepositoryInterface.php
│   │   └── ReservationRepositoryInterface.php
│   ├── Service/
│   │   └── LoanService.php
│   └── Exception/
│       ├── MemberNotFoundException.php
│       ├── LoanLimitExceededException.php
│       └── BookNotAvailableException.php
├── Application/
│   ├── Command/
│   │   ├── RegisterMemberCommand.php
│   │   ├── RegisterMemberCommandHandler.php
│   │   ├── BorrowBookCommand.php
│   │   ├── BorrowBookCommandHandler.php
│   │   ├── ReturnBookCommand.php
│   │   └── ReturnBookCommandHandler.php
│   ├── Query/
│   │   ├── FindMemberQuery.php
│   │   ├── FindMemberQueryHandler.php
│   │   ├── FindMemberLoansQuery.php
│   │   └── FindMemberLoansQueryHandler.php
│   └── DTO/
│       ├── MemberDTO.php
│       └── LoanDTO.php
└── Infrastructure/
    ├── Doctrine/
    │   ├── DoctrineMemberRepository.php
    │   └── DoctrineLoanRepository.php
    ├── InMemory/
    │   ├── InMemoryMemberRepository.php
    │   └── InMemoryLoanRepository.php
    └── ApiPlatform/
        └── Resource/
            ├── MemberResource.php
            └── LoanResource.php
```

## 🎯 Étape 2 : Value Objects

### MemberId

```php
<?php

namespace App\Library\Domain\ValueObject;

use App\Shared\Domain\ValueObject\AggregateRootId;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
final readonly class MemberId
{
    use AggregateRootId;
}
```

### MemberEmail

```php
<?php

namespace App\Library\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
final readonly class MemberEmail
{
    #[ORM\Column(name: 'email', type: 'string', length: 320)]
    public string $value;

    public function __construct(string $value)
    {
        $this->validate($value);
        $this->value = strtolower(trim($value));
    }

    private function validate(string $value): void
    {
        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Invalid email format');
        }
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

### LoanPeriod

```php
<?php

namespace App\Library\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
final readonly class LoanPeriod
{
    #[ORM\Column(name: 'borrowed_at', type: 'datetime_immutable')]
    public \DateTimeImmutable $borrowedAt;

    #[ORM\Column(name: 'due_date', type: 'datetime_immutable')]
    public \DateTimeImmutable $dueDate;

    public function __construct(
        ?\DateTimeImmutable $borrowedAt = null,
        int $durationInDays = 14
    ) {
        $this->borrowedAt = $borrowedAt ?? new \DateTimeImmutable();
        $this->dueDate = $this->borrowedAt->modify("+{$durationInDays} days");
    }

    public function isOverdue(\DateTimeImmutable $currentDate = null): bool
    {
        $currentDate ??= new \DateTimeImmutable();
        return $currentDate > $this->dueDate;
    }

    public function getDaysOverdue(\DateTimeImmutable $currentDate = null): int
    {
        $currentDate ??= new \DateTimeImmutable();
        
        if (!$this->isOverdue($currentDate)) {
            return 0;
        }

        return $currentDate->diff($this->dueDate)->days;
    }
}
```

### Fine

```php
<?php

namespace App\Library\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
final readonly class Fine
{
    private const DAILY_FINE_AMOUNT = 50; // 0.50€ en centimes

    #[ORM\Column(name: 'fine_amount', type: 'integer')]
    public int $amount; // En centimes

    public function __construct(int $amount)
    {
        if ($amount < 0) {
            throw new \InvalidArgumentException('Fine amount cannot be negative');
        }
        $this->amount = $amount;
    }

    public static function calculateForDaysOverdue(int $daysOverdue): self
    {
        return new self($daysOverdue * self::DAILY_FINE_AMOUNT);
    }

    public function toFloat(): float
    {
        return $this->amount / 100;
    }

    public function __toString(): string
    {
        return sprintf('%.2f€', $this->toFloat());
    }
}
```

## 🏛️ Étape 3 : Entités et Agrégats

### Member (Agrégat Root)

```php
<?php

namespace App\Library\Domain\Model;

use App\Library\Domain\ValueObject\MemberId;
use App\Library\Domain\ValueObject\MemberEmail;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class Member
{
    #[ORM\Embedded(columnPrefix: false)]
    private readonly MemberId $id;

    #[ORM\Column(name: 'name', type: 'string', length: 255)]
    private string $name;

    #[ORM\Embedded(columnPrefix: false)]
    private MemberEmail $email;

    #[ORM\Column(name: 'registered_at', type: 'datetime_immutable')]
    private \DateTimeImmutable $registeredAt;

    #[ORM\Column(name: 'is_active', type: 'boolean')]
    private bool $isActive;

    public function __construct(string $name, MemberEmail $email)
    {
        $this->id = new MemberId();
        $this->name = $name;
        $this->email = $email;
        $this->registeredAt = new \DateTimeImmutable();
        $this->isActive = true;
    }

    public function deactivate(): void
    {
        $this->isActive = false;
    }

    public function reactivate(): void
    {
        $this->isActive = true;
    }

    public function updateEmail(MemberEmail $newEmail): void
    {
        $this->email = $newEmail;
    }

    // Getters
    public function getId(): MemberId { return $this->id; }
    public function getName(): string { return $this->name; }
    public function getEmail(): MemberEmail { return $this->email; }
    public function getRegisteredAt(): \DateTimeImmutable { return $this->registeredAt; }
    public function isActive(): bool { return $this->isActive; }
}
```

### Loan (Entité)

```php
<?php

namespace App\Library\Domain\Model;

use App\BookStore\Domain\ValueObject\BookId;
use App\Library\Domain\ValueObject\LoanId;
use App\Library\Domain\ValueObject\MemberId;
use App\Library\Domain\ValueObject\LoanPeriod;
use App\Library\Domain\ValueObject\Fine;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class Loan
{
    #[ORM\Embedded(columnPrefix: false)]
    private readonly LoanId $id;

    #[ORM\Embedded(columnPrefix: false)]
    private readonly MemberId $memberId;

    #[ORM\Embedded(columnPrefix: false)]
    private readonly BookId $bookId;

    #[ORM\Embedded(columnPrefix: false)]
    private LoanPeriod $period;

    #[ORM\Column(name: 'returned_at', type: 'datetime_immutable', nullable: true)]
    private ?\DateTimeImmutable $returnedAt = null;

    #[ORM\Embedded(columnPrefix: false)]
    private ?Fine $fine = null;

    public function __construct(MemberId $memberId, BookId $bookId)
    {
        $this->id = new LoanId();
        $this->memberId = $memberId;
        $this->bookId = $bookId;
        $this->period = new LoanPeriod();
    }

    public function returnBook(\DateTimeImmutable $returnDate = null): Fine
    {
        if ($this->isReturned()) {
            throw new \DomainException('Book is already returned');
        }

        $returnDate ??= new \DateTimeImmutable();
        $this->returnedAt = $returnDate;

        // Calculer l'amende si en retard
        if ($this->period->isOverdue($returnDate)) {
            $daysOverdue = $this->period->getDaysOverdue($returnDate);
            $this->fine = Fine::calculateForDaysOverdue($daysOverdue);
        } else {
            $this->fine = new Fine(0);
        }

        return $this->fine;
    }

    public function isReturned(): bool
    {
        return $this->returnedAt !== null;
    }

    public function isOverdue(\DateTimeImmutable $currentDate = null): bool
    {
        return !$this->isReturned() && $this->period->isOverdue($currentDate);
    }

    // Getters
    public function getId(): LoanId { return $this->id; }
    public function getMemberId(): MemberId { return $this->memberId; }
    public function getBookId(): BookId { return $this->bookId; }
    public function getPeriod(): LoanPeriod { return $this->period; }
    public function getReturnedAt(): ?\DateTimeImmutable { return $this->returnedAt; }
    public function getFine(): ?Fine { return $this->fine; }
}
```

## 🎯 Étape 4 : Service de Domaine

```php
<?php

namespace App\Library\Domain\Service;

use App\BookStore\Domain\ValueObject\BookId;
use App\Library\Domain\Exception\LoanLimitExceededException;
use App\Library\Domain\Exception\BookNotAvailableException;
use App\Library\Domain\Model\Loan;
use App\Library\Domain\Model\Member;
use App\Library\Domain\Repository\LoanRepositoryInterface;

final readonly class LoanService
{
    private const MAX_LOANS_PER_MEMBER = 3;

    public function __construct(
        private LoanRepositoryInterface $loanRepository
    ) {}

    public function canMemberBorrowBook(Member $member, BookId $bookId): void
    {
        // Vérifier si le membre est actif
        if (!$member->isActive()) {
            throw new \DomainException('Inactive member cannot borrow books');
        }

        // Vérifier la limite d'emprunts
        $activeLoans = $this->loanRepository->findActiveLoansByMember($member->getId());
        if (count($activeLoans) >= self::MAX_LOANS_PER_MEMBER) {
            throw new LoanLimitExceededException($member->getId());
        }

        // Vérifier si le membre a des livres en retard
        foreach ($activeLoans as $loan) {
            if ($loan->isOverdue()) {
                throw new \DomainException('Member has overdue books');
            }
        }

        // Vérifier si le livre est disponible
        if ($this->loanRepository->isBookCurrentlyBorrowed($bookId)) {
            throw new BookNotAvailableException($bookId);
        }
    }

    public function borrowBook(Member $member, BookId $bookId): Loan
    {
        $this->canMemberBorrowBook($member, $bookId);

        return new Loan($member->getId(), $bookId);
    }
}
```

## 📝 Étape 5 : Commands et Handlers

### BorrowBookCommand

```php
<?php

namespace App\Library\Application\Command;

use App\BookStore\Domain\ValueObject\BookId;
use App\Library\Domain\ValueObject\MemberId;
use App\Shared\Application\Command\CommandInterface;

/**
 * @implements CommandInterface<Loan>
 */
final readonly class BorrowBookCommand implements CommandInterface
{
    public function __construct(
        public MemberId $memberId,
        public BookId $bookId,
    ) {}
}
```

### BorrowBookCommandHandler

```php
<?php

namespace App\Library\Application\Command;

use App\Library\Domain\Exception\MemberNotFoundException;
use App\Library\Domain\Model\Loan;
use App\Library\Domain\Repository\LoanRepositoryInterface;
use App\Library\Domain\Repository\MemberRepositoryInterface;
use App\Library\Domain\Service\LoanService;
use App\Shared\Application\Command\AsCommandHandler;

#[AsCommandHandler]
final readonly class BorrowBookCommandHandler
{
    public function __construct(
        private MemberRepositoryInterface $memberRepository,
        private LoanRepositoryInterface $loanRepository,
        private LoanService $loanService,
    ) {}

    public function __invoke(BorrowBookCommand $command): Loan
    {
        $member = $this->memberRepository->ofId($command->memberId);
        if (!$member) {
            throw new MemberNotFoundException($command->memberId);
        }

        $loan = $this->loanService->borrowBook($member, $command->bookId);
        $this->loanRepository->add($loan);

        return $loan;
    }
}
```

## 📖 Étape 6 : Queries et DTOs

### FindMemberLoansQuery

```php
<?php

namespace App\Library\Application\Query;

use App\Library\Domain\ValueObject\MemberId;
use App\Shared\Application\Query\QueryInterface;

/**
 * @implements QueryInterface<LoanDTO[]>
 */
final readonly class FindMemberLoansQuery implements QueryInterface
{
    public function __construct(
        public MemberId $memberId,
        public bool $activeOnly = false,
    ) {}
}
```

### LoanDTO

```php
<?php

namespace App\Library\Application\DTO;

use App\Library\Domain\Model\Loan;

final readonly class LoanDTO
{
    public function __construct(
        public string $id,
        public string $memberId,
        public string $bookId,
        public \DateTimeImmutable $borrowedAt,
        public \DateTimeImmutable $dueDate,
        public ?\DateTimeImmutable $returnedAt,
        public ?float $fine,
        public bool $isOverdue,
    ) {}

    public static function fromLoan(Loan $loan): self
    {
        return new self(
            id: (string) $loan->getId(),
            memberId: (string) $loan->getMemberId(),
            bookId: (string) $loan->getBookId(),
            borrowedAt: $loan->getPeriod()->borrowedAt,
            dueDate: $loan->getPeriod()->dueDate,
            returnedAt: $loan->getReturnedAt(),
            fine: $loan->getFine()?->toFloat(),
            isOverdue: $loan->isOverdue(),
        );
    }
}
```

## ✅ Étape 7 : Tests

### Test du Value Object LoanPeriod

```php
<?php

namespace App\Tests\Library\Unit\Domain\ValueObject;

use App\Library\Domain\ValueObject\LoanPeriod;
use PHPUnit\Framework\TestCase;

class LoanPeriodTest extends TestCase
{
    public function testCreateLoanPeriod(): void
    {
        $borrowedAt = new \DateTimeImmutable('2024-01-01');
        $period = new LoanPeriod($borrowedAt, 14);

        $this->assertEquals($borrowedAt, $period->borrowedAt);
        $this->assertEquals(
            new \DateTimeImmutable('2024-01-15'),
            $period->dueDate
        );
    }

    public function testIsOverdue(): void
    {
        $borrowedAt = new \DateTimeImmutable('2024-01-01');
        $period = new LoanPeriod($borrowedAt, 14);

        $this->assertFalse($period->isOverdue(new \DateTimeImmutable('2024-01-10')));
        $this->assertFalse($period->isOverdue(new \DateTimeImmutable('2024-01-15')));
        $this->assertTrue($period->isOverdue(new \DateTimeImmutable('2024-01-20')));
    }

    public function testGetDaysOverdue(): void
    {
        $borrowedAt = new \DateTimeImmutable('2024-01-01');
        $period = new LoanPeriod($borrowedAt, 14);

        $this->assertSame(0, $period->getDaysOverdue(new \DateTimeImmutable('2024-01-10')));
        $this->assertSame(5, $period->getDaysOverdue(new \DateTimeImmutable('2024-01-20')));
    }
}
```

### Test du Command Handler

```php
<?php

namespace App\Tests\Library\Unit\Application\Command;

use App\Library\Application\Command\BorrowBookCommand;
use App\Library\Application\Command\BorrowBookCommandHandler;
use App\Library\Domain\Model\Loan;
use App\Library\Domain\Repository\LoanRepositoryInterface;
use App\Library\Domain\Repository\MemberRepositoryInterface;
use App\Library\Domain\Service\LoanService;
use App\Tests\Library\DummyFactory\DummyMemberFactory;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;
use PHPUnit\Framework\TestCase;

class BorrowBookCommandHandlerTest extends TestCase
{
    public function testBorrowBookSuccessfully(): void
    {
        // Arrange
        $member = DummyMemberFactory::createMember();
        $bookId = DummyBookFactory::createBookId();
        
        $memberRepository = $this->createMock(MemberRepositoryInterface::class);
        $loanRepository = $this->createMock(LoanRepositoryInterface::class);
        $loanService = $this->createMock(LoanService::class);

        $memberRepository->method('ofId')->willReturn($member);
        $loanService->method('borrowBook')->willReturn(
            new Loan($member->getId(), $bookId)
        );
        $loanRepository->expects($this->once())->method('add');

        $handler = new BorrowBookCommandHandler(
            $memberRepository,
            $loanRepository,
            $loanService
        );

        $command = new BorrowBookCommand($member->getId(), $bookId);

        // Act
        $loan = $handler($command);

        // Assert
        $this->assertInstanceOf(Loan::class, $loan);
        $this->assertTrue($member->getId()->equals($loan->getMemberId()));
    }
}
```

## 🔧 Étape 8 : Configuration

### Services Library

```php
<?php
// config/services/library.php

use App\Library\Domain\Repository\MemberRepositoryInterface;
use App\Library\Domain\Repository\LoanRepositoryInterface;
use App\Library\Infrastructure\Doctrine\DoctrineMemberRepository;
use App\Library\Infrastructure\Doctrine\DoctrineLoanRepository;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services();

    $services->defaults()
        ->autowire()
        ->autoconfigure();

    $services->load('App\\Library\\', dirname(__DIR__, 2).'/src/Library');

    $services->set(MemberRepositoryInterface::class)
        ->class(DoctrineMemberRepository::class);

    $services->set(LoanRepositoryInterface::class)
        ->class(DoctrineLoanRepository::class);
};
```

## 🎯 Exercices Supplémentaires

### Niveau Débutant
1. Ajoutez une méthode `extendLoan()` qui prolonge un emprunt de 7 jours
2. Créez un Value Object `MembershipType` (Standard, Premium)
3. Implémentez la query `FindOverdueLoansQuery`

### Niveau Intermédiaire
4. Ajoutez le système de réservations avec l'entité `Reservation`
5. Implémentez les notifications par email (Domain Events)
6. Créez des tests d'acceptance pour l'API

### Niveau Avancé
7. Ajoutez la gestion des amendes avec paiement
8. Implémentez un système de recommandations
9. Créez des rapports statistiques avec des projections

## 💡 Points Clés à Retenir

- **Commencez** toujours par modéliser le domaine
- **Respectez** les règles métier dans les entités
- **Utilisez** les services de domaine pour la logique complexe
- **Testez** chaque couche indépendamment
- **Configurez** proprement l'injection de dépendances

---

**Félicitations !** Vous avez maintenant créé un Bounded Context complet en suivant les principes DDD. Cette base solide vous permettra d'ajouter facilement de nouvelles fonctionnalités tout en maintenant la qualité du code.
