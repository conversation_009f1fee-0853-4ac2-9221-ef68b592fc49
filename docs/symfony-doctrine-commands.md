# Commandes Symfony Doctrine - Guide Complet

## 📋 Table des matières
- [Entités](#entités)
- [Migrations](#migrations)
- [Base de données](#base-de-données)
- [Fixtures](#fixtures)
- [Cache](#cache)
- [Validation et Debug](#validation-et-debug)

---

## 🏗️ Entités

### Création d'entités
```bash
# Créer une nouvelle entité (mode interactif)
php bin/console make:entity

# Créer une entité avec nom spécifique
php bin/console make:entity Product

# Créer une entité dans un namespace spécifique
php bin/console make:entity "App\Entity\Product"
```

### Modification d'entités
```bash
# Ajouter des propriétés à une entité existante
php bin/console make:entity Product

# Générer les getters/setters manquants
php bin/console make:entity --regenerate
```

### Relations entre entités
```bash
# Créer une relation (OneToMany, ManyToOne, ManyToMany, OneToOne)
php bin/console make:entity
# Puis suivre les instructions pour définir la relation
```

---

## 🔄 Migrations

### Génération de migrations
```bash
# Générer une migration basée sur les changements d'entités
php bin/console make:migration

# Générer une migration avec un nom personnalisé
php bin/console make:migration --name=AddUserTable
```

### Exécution de migrations
```bash
# Exécuter toutes les migrations en attente
php bin/console doctrine:migrations:migrate

# Exécuter jusqu'à une version spécifique
php bin/console doctrine:migrations:migrate 20231201120000

# Exécuter la migration suivante uniquement
php bin/console doctrine:migrations:migrate --up

# Revenir à la migration précédente
php bin/console doctrine:migrations:migrate --down

# Exécuter sans confirmation
php bin/console doctrine:migrations:migrate --no-interaction
```

### Gestion des migrations
```bash
# Voir le statut des migrations
php bin/console doctrine:migrations:status

# Lister toutes les migrations
php bin/console doctrine:migrations:list

# Voir les détails d'une migration
php bin/console doctrine:migrations:status --show-versions

# Marquer une migration comme exécutée (sans l'exécuter)
php bin/console doctrine:migrations:version 20231201120000 --add

# Marquer une migration comme non exécutée
php bin/console doctrine:migrations:version 20231201120000 --delete

# Synchroniser les métadonnées de migration
php bin/console doctrine:migrations:sync-metadata-storage
```

### Rollback de migrations
```bash
# Revenir à une version spécifique
php bin/console doctrine:migrations:migrate 20231201120000

# Revenir à la migration précédente
php bin/console doctrine:migrations:migrate prev

# Revenir au début (attention : destructif)
php bin/console doctrine:migrations:migrate first
```

---

## 🗄️ Base de données

### Création et suppression
```bash
# Créer la base de données
php bin/console doctrine:database:create

# Supprimer la base de données
php bin/console doctrine:database:drop --force

# Recréer la base de données (drop + create)
php bin/console doctrine:database:drop --force
php bin/console doctrine:database:create
```

### Schema
```bash
# Créer le schéma de base de données
php bin/console doctrine:schema:create

# Mettre à jour le schéma
php bin/console doctrine:schema:update --force

# Voir les changements sans les appliquer
php bin/console doctrine:schema:update --dump-sql

# Supprimer le schéma
php bin/console doctrine:schema:drop --force
```

---

## 🌱 Fixtures

### Installation
```bash
composer require --dev doctrine/doctrine-fixtures-bundle
```

### Création et exécution
```bash
# Créer une fixture
php bin/console make:fixtures

# Charger les fixtures
php bin/console doctrine:fixtures:load

# Charger sans confirmation
php bin/console doctrine:fixtures:load --no-interaction

# Ajouter des fixtures sans purger
php bin/console doctrine:fixtures:load --append

# Charger des fixtures spécifiques
php bin/console doctrine:fixtures:load --group=test
```

---

## 🧹 Cache

### Gestion du cache Doctrine
```bash
# Vider le cache des métadonnées
php bin/console doctrine:cache:clear-metadata

# Vider le cache des requêtes
php bin/console doctrine:cache:clear-query

# Vider le cache des résultats
php bin/console doctrine:cache:clear-result

# Vider tous les caches Doctrine
php bin/console doctrine:cache:clear-metadata
php bin/console doctrine:cache:clear-query
php bin/console doctrine:cache:clear-result
```

---

## 🔍 Validation et Debug

### Validation du mapping
```bash
# Valider le mapping des entités
php bin/console doctrine:mapping:info

# Valider le schéma
php bin/console doctrine:schema:validate

# Vérifier les entités
php bin/console doctrine:mapping:convert annotation ./src
```

### Debug et informations
```bash
# Lister toutes les entités
php bin/console doctrine:mapping:info

# Voir les informations d'une entité spécifique
php bin/console doctrine:mapping:info --entity=App\\Entity\\Product

# Générer les proxies
php bin/console doctrine:generate:proxies

# Voir la configuration Doctrine
php bin/console debug:config doctrine
```

---

## 🚀 Commandes utiles pour le développement

### Workflow complet de développement
```bash
# 1. Modifier une entité
php bin/console make:entity Product

# 2. Générer la migration
php bin/console make:migration

# 3. Exécuter la migration
php bin/console doctrine:migrations:migrate

# 4. (Optionnel) Charger des fixtures
php bin/console doctrine:fixtures:load
```

### Reset complet de la base de données
```bash
# Script de reset complet
php bin/console doctrine:database:drop --force --if-exists
php bin/console doctrine:database:create
php bin/console doctrine:migrations:migrate --no-interaction
php bin/console doctrine:fixtures:load --no-interaction
```

### Environnements spécifiques
```bash
# Exécuter sur un environnement spécifique
php bin/console doctrine:migrations:migrate --env=test
php bin/console doctrine:database:create --env=prod
```

---

## ⚠️ Bonnes pratiques

### Sécurité
- ⚠️ **Toujours** faire une sauvegarde avant les migrations en production
- ⚠️ **Tester** les migrations sur un environnement de staging
- ⚠️ **Vérifier** le contenu des migrations avant exécution

### Performance
- 🚀 Utiliser `--no-interaction` dans les scripts automatisés
- 🚀 Vider les caches après les changements de schéma
- 🚀 Utiliser les fixtures uniquement en développement

### Développement
- ✅ Toujours générer des migrations plutôt que `schema:update`
- ✅ Nommer les migrations de manière explicite
- ✅ Valider le schéma régulièrement
- ✅ Utiliser des fixtures pour les données de test

---

## 📚 Ressources supplémentaires

- [Documentation Doctrine](https://www.doctrine-project.org/projects/doctrine-orm/en/current/index.html)
- [Symfony Doctrine Bundle](https://symfony.com/doc/current/doctrine.html)
- [Doctrine Migrations](https://www.doctrine-project.org/projects/doctrine-migrations/en/current/index.html)
