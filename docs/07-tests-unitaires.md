# 7. Tests Unitaires - Stratégies par Couche

## 🎯 Objectifs d'apprentissage

À la fin de ce chapitre, vous serez capable de :
- Écrire des tests unitaires pour chaque couche DDD
- Utiliser les mocks et stubs efficacement
- Créer des factories pour les tests
- Appliquer les bonnes pratiques de test
- Organiser vos tests de manière cohérente

## 📚 Pyramide des Tests

```
        /\
       /  \
      / UI \     ← Tests End-to-End (peu nombreux)
     /______\
    /        \
   / Intégra- \   ← Tests d'Intégration (moyens)
  /    tion    \
 /______________\
/              \
/   Unitaires   \  ← Tests Unitaires (nombreux)
/________________\
```

### Caractéristiques des Tests Unitaires

- ✅ **Rapides** : Exécution en millisecondes
- ✅ **Isolés** : Testent une seule unité
- ✅ **Répétables** : Résultats constants
- ✅ **Indépendants** : Pas d'ordre d'exécution
- ✅ **Déterministes** : Pas d'aléatoire

## 🏗️ Organisation des Tests

### Structure des Dossiers

```
tests/
├── BookStore/
│   ├── Unit/                    ← Tests unitaires
│   │   ├── Domain/
│   │   │   ├── Model/
│   │   │   │   ├── BookTest.php
│   │   │   │   └── OrderTest.php
│   │   │   ├── ValueObject/
│   │   │   │   ├── BookNameTest.php
│   │   │   │   ├── PriceTest.php
│   │   │   │   └── EmailTest.php
│   │   │   └── Service/
│   │   │       └── BookDiscountServiceTest.php
│   │   └── Application/
│   │       ├── Command/
│   │       │   ├── CreateBookCommandHandlerTest.php
│   │       │   └── DiscountBookCommandHandlerTest.php
│   │       └── Query/
│   │           └── FindBookQueryHandlerTest.php
│   ├── Functional/              ← Tests d'intégration
│   ├── Acceptance/              ← Tests end-to-end
│   └── DummyFactory/            ← Factories pour tests
│       ├── DummyBookFactory.php
│       └── DummyOrderFactory.php
└── Shared/
    └── Unit/
        └── Domain/
            └── ValueObject/
                └── AggregateRootIdTest.php
```

## 🧪 Tests de Value Objects

### Test Complet d'un Value Object

```php
<?php

namespace App\Tests\BookStore\Unit\Domain\ValueObject;

use App\BookStore\Domain\ValueObject\Price;
use App\BookStore\Domain\ValueObject\Discount;
use PHPUnit\Framework\TestCase;

class PriceTest extends TestCase
{
    public function testCreateValidPrice(): void
    {
        // Arrange & Act
        $price = new Price(1500, 'EUR');

        // Assert
        $this->assertSame(1500, $price->amount);
        $this->assertSame('EUR', $price->currency);
        $this->assertSame(15.0, $price->toFloat());
        $this->assertSame('15.00 EUR', (string) $price);
    }

    /**
     * @dataProvider invalidPriceProvider
     */
    public function testRejectInvalidPrice(int $amount, string $currency, string $expectedMessage): void
    {
        // Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage($expectedMessage);

        // Act
        new Price($amount, $currency);
    }

    public static function invalidPriceProvider(): array
    {
        return [
            'negative amount' => [-100, 'EUR', 'Price cannot be negative'],
            'invalid currency' => [1000, 'XYZ', 'Unsupported currency'],
            'zero with invalid currency' => [0, 'ABC', 'Unsupported currency'],
        ];
    }

    public function testApplyDiscount(): void
    {
        // Arrange
        $price = new Price(1000, 'EUR');
        $discount = new Discount(20.0);

        // Act
        $discountedPrice = $price->applyDiscount($discount);

        // Assert
        $this->assertSame(800, $discountedPrice->amount);
        $this->assertSame('EUR', $discountedPrice->currency);
        // Vérifier l'immutabilité
        $this->assertSame(1000, $price->amount);
        $this->assertNotSame($price, $discountedPrice);
    }

    public function testEquality(): void
    {
        // Arrange
        $price1 = new Price(1500, 'EUR');
        $price2 = new Price(1500, 'EUR');
        $price3 = new Price(1500, 'USD');
        $price4 = new Price(1600, 'EUR');

        // Assert
        $this->assertTrue($price1->equals($price2));
        $this->assertFalse($price1->equals($price3));
        $this->assertFalse($price1->equals($price4));
    }

    public function testEdgeCases(): void
    {
        // Test avec montant zéro
        $freePrice = new Price(0, 'EUR');
        $this->assertSame(0.0, $freePrice->toFloat());

        // Test avec gros montant
        $expensivePrice = new Price(999999, 'EUR');
        $this->assertSame(9999.99, $expensivePrice->toFloat());
    }
}
```

### Test d'Email avec Validation

```php
<?php

namespace App\Tests\Shared\Unit\Domain\ValueObject;

use App\Shared\Domain\ValueObject\Email;
use PHPUnit\Framework\TestCase;

class EmailTest extends TestCase
{
    public function testCreateValidEmail(): void
    {
        $email = new Email('<EMAIL>');

        $this->assertSame('<EMAIL>', $email->value);
        $this->assertSame('john.doe', $email->getLocalPart());
        $this->assertSame('example.com', $email->getDomain());
    }

    public function testNormalizeEmail(): void
    {
        $email = new Email('  <EMAIL>  ');

        $this->assertSame('<EMAIL>', $email->value);
    }

    /**
     * @dataProvider invalidEmailProvider
     */
    public function testRejectInvalidEmail(string $invalidEmail): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid email format');

        new Email($invalidEmail);
    }

    public static function invalidEmailProvider(): array
    {
        return [
            ['invalid-email'],
            ['@example.com'],
            ['john@'],
            ['<EMAIL>'],
            ['<EMAIL>'],
            [''],
        ];
    }

    public function testRejectTooLongEmail(): void
    {
        $longEmail = str_repeat('a', 310) . '@example.com';

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Email too long');

        new Email($longEmail);
    }
}
```

## 🏛️ Tests d'Entités

### Test d'Entité avec Invariants

```php
<?php

namespace App\Tests\BookStore\Unit\Domain\Model;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookContent;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Discount;
use App\BookStore\Domain\ValueObject\Price;
use PHPUnit\Framework\TestCase;

class BookTest extends TestCase
{
    private Book $book;

    protected function setUp(): void
    {
        $this->book = $this->createValidBook();
    }

    public function testCreateBook(): void
    {
        $this->assertInstanceOf(Book::class, $this->book);
        $this->assertSame('Clean Code', $this->book->getName()->value);
        $this->assertSame('Robert C. Martin', $this->book->getAuthor()->name);
        $this->assertSame(3500, $this->book->getPrice()->amount);
    }

    public function testBookHasUniqueId(): void
    {
        $book1 = $this->createValidBook();
        $book2 = $this->createValidBook();

        $this->assertFalse($book1->equals($book2));
        $this->assertNotSame($book1->getId()->value, $book2->getId()->value);
    }

    public function testApplyValidDiscount(): void
    {
        $originalPrice = $this->book->getPrice()->amount;
        $discount = new Discount(20.0);

        $this->book->applyDiscount($discount);

        $expectedPrice = (int) ($originalPrice * 0.8);
        $this->assertSame($expectedPrice, $this->book->getPrice()->amount);
    }

    public function testRejectExcessiveDiscount(): void
    {
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Discount cannot exceed 50%');

        $excessiveDiscount = new Discount(60.0);
        $this->book->applyDiscount($excessiveDiscount);
    }

    public function testUpdateValidContent(): void
    {
        $newContent = new BookContent(str_repeat('New content. ', 20));

        $this->book->updateContent($newContent);

        // Pas d'exception = succès
        $this->assertTrue(true);
    }

    public function testRejectTooShortContent(): void
    {
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Book content must be at least 100 characters');

        $shortContent = new BookContent('Too short');
        $this->book->updateContent($shortContent);
    }

    public function testChangeAuthor(): void
    {
        $newAuthor = new Author('Martin Fowler');

        $this->book->changeAuthor($newAuthor);

        $this->assertSame('Martin Fowler', $this->book->getAuthor()->name);
    }

    public function testRenameBook(): void
    {
        $newName = new BookName('Refactoring');

        $this->book->rename($newName);

        $this->assertSame('Refactoring', $this->book->getName()->value);
    }

    public function testIsSameBook(): void
    {
        $sameBook = new Book(
            $this->book->getName(),
            new BookDescription('Different description'),
            $this->book->getAuthor(),
            new BookContent(str_repeat('Different content. ', 20)),
            new Price(5000, 'EUR')
        );

        $this->assertTrue($this->book->isSameBook($sameBook));
    }

    private function createValidBook(): Book
    {
        return new Book(
            new BookName('Clean Code'),
            new BookDescription('A handbook of agile software craftsmanship'),
            new Author('Robert C. Martin'),
            new BookContent(str_repeat('Quality content. ', 20)),
            new Price(3500, 'EUR')
        );
    }
}
```

## 🎯 Tests de Command Handlers

### Test avec Mock Repository

```php
<?php

namespace App\Tests\BookStore\Unit\Application\Command;

use App\BookStore\Application\Command\CreateBookCommand;
use App\BookStore\Application\Command\CreateBookCommandHandler;
use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;
use PHPUnit\Framework\TestCase;

class CreateBookCommandHandlerTest extends TestCase
{
    private BookRepositoryInterface $repository;
    private CreateBookCommandHandler $handler;

    protected function setUp(): void
    {
        $this->repository = $this->createMock(BookRepositoryInterface::class);
        $this->handler = new CreateBookCommandHandler($this->repository);
    }

    public function testCreateBookSuccessfully(): void
    {
        // Arrange
        $command = DummyBookFactory::createBookCommand();

        // Assert - Vérifier que add() est appelé avec un Book
        $this->repository
            ->expects($this->once())
            ->method('add')
            ->with($this->isInstanceOf(Book::class));

        // Act
        $book = ($this->handler)($command);

        // Assert
        $this->assertInstanceOf(Book::class, $book);
        $this->assertSame($command->name->value, $book->getName()->value);
        $this->assertSame($command->author->name, $book->getAuthor()->name);
        $this->assertSame($command->price->amount, $book->getPrice()->amount);
    }

    public function testCreateBookWithDifferentData(): void
    {
        // Arrange
        $command = DummyBookFactory::createBookCommand(
            name: 'Refactoring',
            author: 'Martin Fowler',
            price: 4500
        );

        $this->repository->expects($this->once())->method('add');

        // Act
        $book = ($this->handler)($command);

        // Assert
        $this->assertSame('Refactoring', $book->getName()->value);
        $this->assertSame('Martin Fowler', $book->getAuthor()->name);
        $this->assertSame(4500, $book->getPrice()->amount);
    }
}
```

### Test avec Exception

```php
<?php

namespace App\Tests\BookStore\Unit\Application\Command;

use App\BookStore\Application\Command\DiscountBookCommand;
use App\BookStore\Application\Command\DiscountBookCommandHandler;
use App\BookStore\Domain\Exception\MissingBookException;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Domain\ValueObject\Discount;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;
use PHPUnit\Framework\TestCase;

class DiscountBookCommandHandlerTest extends TestCase
{
    private BookRepositoryInterface $repository;
    private DiscountBookCommandHandler $handler;

    protected function setUp(): void
    {
        $this->repository = $this->createMock(BookRepositoryInterface::class);
        $this->handler = new DiscountBookCommandHandler($this->repository);
    }

    public function testApplyDiscountSuccessfully(): void
    {
        // Arrange
        $book = DummyBookFactory::createBook();
        $command = new DiscountBookCommand($book->getId(), new Discount(20.0));

        $this->repository
            ->method('ofId')
            ->with($book->getId())
            ->willReturn($book);

        $originalPrice = $book->getPrice()->amount;

        // Act
        ($this->handler)($command);

        // Assert
        $expectedPrice = (int) ($originalPrice * 0.8);
        $this->assertSame($expectedPrice, $book->getPrice()->amount);
    }

    public function testThrowExceptionWhenBookNotFound(): void
    {
        // Arrange
        $bookId = DummyBookFactory::createBookId();
        $command = new DiscountBookCommand($bookId, new Discount(20.0));

        $this->repository
            ->method('ofId')
            ->with($bookId)
            ->willReturn(null);

        // Assert
        $this->expectException(MissingBookException::class);

        // Act
        ($this->handler)($command);
    }
}
```

## 📖 Tests de Query Handlers

```php
<?php

namespace App\Tests\BookStore\Unit\Application\Query;

use App\BookStore\Application\DTO\BookDTO;
use App\BookStore\Application\Query\FindBookQuery;
use App\BookStore\Application\Query\FindBookQueryHandler;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;
use PHPUnit\Framework\TestCase;

class FindBookQueryHandlerTest extends TestCase
{
    private BookRepositoryInterface $repository;
    private FindBookQueryHandler $handler;

    protected function setUp(): void
    {
        $this->repository = $this->createMock(BookRepositoryInterface::class);
        $this->handler = new FindBookQueryHandler($this->repository);
    }

    public function testFindExistingBook(): void
    {
        // Arrange
        $book = DummyBookFactory::createBook();
        $query = new FindBookQuery($book->getId());

        $this->repository
            ->method('ofId')
            ->with($book->getId())
            ->willReturn($book);

        // Act
        $result = ($this->handler)($query);

        // Assert
        $this->assertInstanceOf(BookDTO::class, $result);
        $this->assertSame((string) $book->getId(), $result->id);
        $this->assertSame($book->getName()->value, $result->name);
        $this->assertSame($book->getAuthor()->name, $result->author);
    }

    public function testReturnNullWhenBookNotFound(): void
    {
        // Arrange
        $bookId = DummyBookFactory::createBookId();
        $query = new FindBookQuery($bookId);

        $this->repository
            ->method('ofId')
            ->with($bookId)
            ->willReturn(null);

        // Act
        $result = ($this->handler)($query);

        // Assert
        $this->assertNull($result);
    }
}
```

## 🏭 Dummy Factories

### Factory pour Book

```php
<?php

namespace App\Tests\BookStore\DummyFactory;

use App\BookStore\Application\Command\CreateBookCommand;
use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookContent;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookId;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Price;

final class DummyBookFactory
{
    private function __construct() {}

    public static function createBook(
        string $name = 'Clean Code',
        string $description = 'A handbook of agile software craftsmanship',
        string $author = 'Robert C. Martin',
        string $content = null,
        int $price = 3500,
        string $currency = 'EUR'
    ): Book {
        return new Book(
            new BookName($name),
            new BookDescription($description),
            new Author($author),
            new BookContent($content ?? str_repeat('Quality content. ', 20)),
            new Price($price, $currency)
        );
    }

    public static function createBookCommand(
        string $name = 'Clean Code',
        string $description = 'A handbook of agile software craftsmanship',
        string $author = 'Robert C. Martin',
        string $content = null,
        int $price = 3500,
        string $currency = 'EUR'
    ): CreateBookCommand {
        return new CreateBookCommand(
            new BookName($name),
            new BookDescription($description),
            new Author($author),
            new BookContent($content ?? str_repeat('Quality content. ', 20)),
            new Price($price, $currency)
        );
    }

    public static function createBookId(): BookId
    {
        return new BookId();
    }

    public static function createPrice(int $amount = 3500, string $currency = 'EUR'): Price
    {
        return new Price($amount, $currency);
    }

    public static function createAuthor(string $name = 'Robert C. Martin'): Author
    {
        return new Author($name);
    }
}
```

## 🎨 Bonnes Pratiques

### ✅ À Faire
- **Arrange-Act-Assert** : Structure claire des tests
- **Noms explicites** : `testRejectNegativePrice()`
- **Un concept par test** : Tester une seule chose
- **Data providers** : Pour les cas multiples
- **Factories** : Pour créer les objets de test
- **setUp/tearDown** : Pour l'initialisation commune

### ❌ À Éviter
- Tests trop longs ou complexes
- Logique conditionnelle dans les tests
- Tests dépendants entre eux
- Assertions multiples non liées
- Mocks trop complexes
- Tests qui testent l'implémentation

## 🚀 Prochaines Étapes

Maintenant que vous maîtrisez les tests unitaires :
1. Tests d'intégration et fonctionnels
2. Tests d'acceptance end-to-end
3. Configuration des environnements de test
4. Optimisation des performances de test

## 💡 Points Clés à Retenir

- Les **tests unitaires** sont la base de la pyramide
- **Isolez** chaque unité avec des mocks
- **Organisez** les tests comme le code source
- **Utilisez des factories** pour créer les données de test
- **Testez les cas limites** et les erreurs
- **Maintenez** les tests simples et lisibles

---

**Exercice pratique** : Écrivez les tests unitaires complets pour un agrégat `Order` avec ses méthodes `addItem()`, `removeItem()`, `confirm()` et `cancel()`, en testant tous les invariants.
