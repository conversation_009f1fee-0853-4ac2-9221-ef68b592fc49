# 2. Structure de Projet DDD

## 🎯 Objectifs d'apprentissage

À la fin de ce chapitre, vous serez capable de :
- Organiser un projet DDD avec une structure claire
- Séparer correctement les couches Domain, Application et Infrastructure
- Configurer les namespaces et autoloading
- Mettre en place les Bounded Contexts

## 📁 Structure Générale du Projet

Voici la structure recommandée pour un projet DDD avec Symfony :

```
project-root/
├── config/                      ← Configuration Symfony
│   ├── packages/                ← Configuration des bundles
│   ├── routes/                  ← Routes par environnement
│   └── services/                ← Services par bounded context
├── docs/                        ← Documentation (ces guides !)
├── src/                         ← Code source
│   ├── BoundedContext1/         ← Premier contexte métier
│   ├── BoundedContext2/         ← Deuxième contexte métier
│   └── Shared/                  ← Code partagé
├── tests/                       ← Tests organisés par contexte
│   ├── BoundedContext1/
│   ├── BoundedContext2/
│   └── Shared/
├── var/                         ← Cache, logs, sessions
├── vendor/                      ← Dépendances Composer
├── composer.json                ← Configuration Composer
├── phpunit.xml.dist             ← Configuration PHPUnit
└── symfony.lock                 ← Lock file Symfony
```

## 🏗️ Structure d'un Bounded Context

Chaque Bounded Context suit cette organisation :

```
src/BookStore/                   ← Nom du contexte métier
├── Domain/                      ← Couche Domain (cœur métier)
│   ├── Model/                   ← Entités et Agrégats
│   │   ├── Book.php             ← Entité principale
│   │   └── BookCollection.php   ← Collection d'entités
│   ├── ValueObject/             ← Value Objects
│   │   ├── BookId.php           ← Identifiant
│   │   ├── BookName.php         ← Nom du livre
│   │   ├── Author.php           ← Auteur
│   │   ├── Price.php            ← Prix
│   │   └── Discount.php         ← Remise
│   ├── Repository/              ← Interfaces des repositories
│   │   └── BookRepositoryInterface.php
│   ├── Service/                 ← Services de domaine
│   │   └── BookDiscountService.php
│   └── Exception/               ← Exceptions métier
│       ├── MissingBookException.php
│       └── InvalidDiscountException.php
├── Application/                 ← Couche Application (cas d'usage)
│   ├── Command/                 ← Commands (écriture)
│   │   ├── CreateBookCommand.php
│   │   ├── CreateBookCommandHandler.php
│   │   ├── DiscountBookCommand.php
│   │   └── DiscountBookCommandHandler.php
│   ├── Query/                   ← Queries (lecture)
│   │   ├── FindBookQuery.php
│   │   ├── FindBookQueryHandler.php
│   │   ├── FindCheapestBooksQuery.php
│   │   └── FindCheapestBooksQueryHandler.php
│   └── DTO/                     ← Data Transfer Objects
│       ├── BookDTO.php
│       └── BookCollectionDTO.php
└── Infrastructure/              ← Couche Infrastructure (détails techniques)
    ├── Doctrine/                ← Persistance Doctrine
    │   ├── DoctrineBookRepository.php
    │   └── Type/                ← Types Doctrine personnalisés
    │       ├── BookIdType.php
    │       └── PriceType.php
    ├── InMemory/                ← Implémentation en mémoire (tests)
    │   └── InMemoryBookRepository.php
    ├── ApiPlatform/             ← API REST
    │   ├── Resource/            ← Resources API Platform
    │   │   └── BookResource.php
    │   └── State/               ← Providers et Processors
    │       ├── Provider/
    │       │   ├── BookItemProvider.php
    │       │   └── BookCollectionProvider.php
    │       └── Processor/
    │           ├── CreateBookProcessor.php
    │           └── DiscountBookProcessor.php
    └── Symfony/                 ← Intégrations Symfony
        ├── Form/
        └── Validator/
```

## 🔧 Configuration des Services

### Configuration par Bounded Context

Créez un fichier de configuration pour chaque contexte :

**config/services/book_store.php**
```php
<?php

use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Infrastructure\Doctrine\DoctrineBookRepository;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services();

    $services->defaults()
        ->autowire()
        ->autoconfigure();

    // Auto-registration des services du contexte
    $services->load('App\\BookStore\\', dirname(__DIR__, 2).'/src/BookStore');

    // Configuration spécifique des repositories
    $services->set(BookRepositoryInterface::class)
        ->class(DoctrineBookRepository::class);
};
```

### Configuration des Tests

**config/services/test/book_store.php**
```php
<?php

use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Infrastructure\InMemory\InMemoryBookRepository;

return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services();

    // Utilisation du repository en mémoire pour les tests
    $services->set(BookRepositoryInterface::class)
        ->class(InMemoryBookRepository::class);
};
```

## 📦 Configuration Composer

**composer.json**
```json
{
    "autoload": {
        "psr-4": {
            "App\\": "src/"
        }
    },
    "autoload-dev": {
        "psr-4": {
            "App\\Tests\\": "tests/"
        }
    }
}
```

## 🗂️ Organisation des Tests

Les tests suivent la même structure que le code source :

```
tests/
├── BookStore/
│   ├── Unit/                    ← Tests unitaires
│   │   ├── Domain/
│   │   │   ├── Model/
│   │   │   │   └── BookTest.php
│   │   │   └── ValueObject/
│   │   │       ├── BookNameTest.php
│   │   │       └── PriceTest.php
│   │   └── Application/
│   │       └── Command/
│   │           └── CreateBookCommandHandlerTest.php
│   ├── Functional/              ← Tests d'intégration
│   │   ├── CreateBookTest.php
│   │   └── DiscountBookTest.php
│   ├── Acceptance/              ← Tests end-to-end
│   │   └── BookCrudTest.php
│   └── DummyFactory/            ← Factories pour les tests
│       └── DummyBookFactory.php
└── Shared/
    └── Unit/
        └── Domain/
            └── ValueObject/
                └── AggregateRootIdTest.php
```

## 🎨 Conventions de Nommage

### Entités et Value Objects
- **Entités** : Nom métier simple (`Book`, `Author`)
- **Value Objects** : Nom descriptif (`BookName`, `Price`, `EmailAddress`)
- **Identifiants** : Suffixe `Id` (`BookId`, `AuthorId`)

### Commands et Queries
- **Commands** : Verbe + Nom (`CreateBook`, `DiscountBook`)
- **Queries** : `Find` + Critère (`FindBook`, `FindCheapestBooks`)
- **Handlers** : Nom + `Handler` (`CreateBookCommandHandler`)

### Repositories
- **Interface** : Nom + `RepositoryInterface` (`BookRepositoryInterface`)
- **Implémentation** : Technologie + Nom + `Repository` (`DoctrineBookRepository`)

### Exceptions
- **Métier** : Descriptif + `Exception` (`MissingBookException`)
- **Technique** : Type + `Exception` (`ValidationException`)

## 🔄 Flux de Dépendances

```
Infrastructure → Application → Domain
     ↑              ↑           ↑
   Adapte        Orchestre   Contient
     ↓              ↓           ↓
  Frameworks    Use Cases   Business Rules
```

**Règles importantes :**
- ❌ Domain ne dépend de rien d'autre
- ❌ Application ne dépend que de Domain
- ✅ Infrastructure peut dépendre de tout

## 📋 Checklist de Structure

Avant de commencer le développement, vérifiez :

- [ ] Bounded Contexts identifiés et séparés
- [ ] Structure des dossiers respectée
- [ ] Configuration des services par contexte
- [ ] Namespaces PSR-4 configurés
- [ ] Tests organisés par couche
- [ ] Conventions de nommage appliquées
- [ ] Dépendances respectent le sens Domain ← Application ← Infrastructure

## 🚀 Prochaines Étapes

Maintenant que la structure est claire, nous allons voir :
1. Comment créer des Value Objects robustes
2. Modéliser des Entités avec leurs invariants
3. Implémenter les Repositories
4. Mettre en place CQRS

## 💡 Points Clés à Retenir

- **Séparez clairement** les Bounded Contexts
- **Respectez la hiérarchie** des couches
- **Configurez les services** par contexte
- **Organisez les tests** comme le code source
- **Suivez les conventions** de nommage
- **Respectez le sens** des dépendances

---

**Exercice pratique** : Créez la structure de dossiers pour un nouveau Bounded Context "Library" qui gérerait les emprunts de livres.
