# 🚀 Exemples d'utilisation des Makers DDD

## 📋 Commandes disponibles

### 1. Générer une entité complète avec repository

```bash
# Génère Product + ProductId + ProductRepositoryInterface + DoctrineProductRepository
php bin/console make:ddd:entity BookStore Product --with-repository
```

### 2. Générer seulement une entité

```bash
# Génère Category + CategoryId
php bin/console make:ddd:entity BookStore Category
```

### 3. Générer un Value Object

```bash
# Value Object string embeddable
php bin/console make:ddd:value-object BookStore ProductName --type=string --embeddable

# Value Object int embeddable
php bin/console make:ddd:value-object BookStore ProductPrice --type=int --embeddable

# Value Object simple (non embeddable)
php bin/console make:ddd:value-object BookStore ProductStatus --type=string
```

## 🎯 Exemple complet : Créer un contexte Product

### Étape 1: Générer l'entité Product avec repository

```bash
php bin/console make:ddd:entity BookStore Product --with-repository
```

**Fichiers générés :**
- `src/BookStore/Domain/ValueObject/ProductId.php`
- `src/BookStore/Domain/Model/Product.php`
- `src/BookStore/Domain/Repository/ProductRepositoryInterface.php`
- `src/BookStore/Infrastructure/Doctrine/DoctrineProductRepository.php`

### Étape 2: Générer les Value Objects nécessaires

```bash
# Nom du produit
php bin/console make:ddd:value-object BookStore ProductName --type=string --embeddable

# Prix du produit
php bin/console make:ddd:value-object BookStore ProductPrice --type=int --embeddable

# Description du produit
php bin/console make:ddd:value-object BookStore ProductDescription --type=string --embeddable
```

### Étape 3: Configurer le repository dans les services

Ajoutez dans `config/services/book_store.php` :

```php
// repositories
$services->set(ProductRepositoryInterface::class)
    ->class(DoctrineProductRepository::class);
```

### Étape 4: Mettre à jour l'entité Product

Modifiez `src/BookStore/Domain/Model/Product.php` pour utiliser les Value Objects :

```php
<?php

declare(strict_types=1);

namespace App\BookStore\Domain\Model;

use App\BookStore\Domain\ValueObject\ProductId;
use App\BookStore\Domain\ValueObject\ProductName;
use App\BookStore\Domain\ValueObject\ProductPrice;
use App\BookStore\Domain\ValueObject\ProductDescription;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
final class Product
{
    #[ORM\Embedded(columnPrefix: false)]
    private readonly ProductId $id;

    public function __construct(
        #[ORM\Embedded(columnPrefix: false)]
        private ProductName $name,

        #[ORM\Embedded(columnPrefix: false)]
        private ProductDescription $description,

        #[ORM\Embedded(columnPrefix: false)]
        private ProductPrice $price,
    ) {
        $this->id = new ProductId();
    }

    public function id(): ProductId
    {
        return $this->id;
    }

    public function name(): ProductName
    {
        return $this->name;
    }

    public function description(): ProductDescription
    {
        return $this->description;
    }

    public function price(): ProductPrice
    {
        return $this->price;
    }

    public function updatePrice(ProductPrice $newPrice): void
    {
        $this->price = $newPrice;
    }

    public function updateDetails(ProductName $name, ProductDescription $description): void
    {
        $this->name = $name;
        $this->description = $description;
    }
}
```

## 🔧 Configuration Doctrine

Ajoutez le mapping dans `config/packages/doctrine.php` si nécessaire :

```php
'mappings' => [
    'BookStore' => [
        'is_bundle' => false,
        'type' => 'attribute',
        'dir' => '%kernel.project_dir%/src/BookStore/Domain',
        'prefix' => 'App\BookStore\Domain',
    ],
    // ... autres mappings
],
```

## ✅ Vérification

### Lister les commandes disponibles

```bash
php bin/console list make
```

Vous devriez voir :
- `make:ddd:entity`
- `make:ddd:value-object`

### Tester la génération

```bash
# Test simple
php bin/console make:ddd:entity TestContext TestEntity

# Test avec repository
php bin/console make:ddd:entity TestContext TestEntity --with-repository

# Test Value Object
php bin/console make:ddd:value-object TestContext TestName --type=string --embeddable
```

## 🎨 Personnalisation

### Modifier les templates

Les templates se trouvent dans `templates/maker/ddd/` :
- `Entity.tpl.php` - Template pour les entités
- `RepositoryInterface.tpl.php` - Template pour les interfaces de repository
- `DoctrineRepository.tpl.php` - Template pour les implémentations Doctrine
- `ValueObjectId.tpl.php` - Template pour les ID Value Objects
- `ValueObject.tpl.php` - Template pour les Value Objects génériques

### Ajouter de nouveaux types de Value Objects

Modifiez `templates/maker/ddd/ValueObject.tpl.php` pour supporter d'autres types comme `DateTime`, `Email`, etc.

## 🚨 Points d'attention

1. **Namespace** : Assurez-vous que le bounded context existe dans `src/`
2. **Services** : N'oubliez pas de configurer les repositories dans les services
3. **Tests** : Générez les tests après avoir créé vos entités
4. **Migrations** : Lancez les migrations Doctrine après avoir créé vos entités

```bash
# Générer une migration
php bin/console doctrine:migrations:diff

# Appliquer la migration
php bin/console doctrine:migrations:migrate
```
