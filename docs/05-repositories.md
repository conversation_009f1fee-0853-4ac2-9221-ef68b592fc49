# 5. Pattern Repository - Persistance et Récupération

## 🎯 Objectifs d'apprentissage

À la fin de ce chapitre, vous serez capable de :
- Comprendre le pattern Repository et ses avantages
- Créer des interfaces de repository dans le domaine
- Implémenter des repositories avec Doctrine
- Utiliser des repositories en mémoire pour les tests
- Appliquer les bonnes pratiques de persistance

## 📚 Qu'est-ce que le Pattern Repository ?

Le **Pattern Repository** encapsule la logique d'accès aux données et centralise les requêtes communes. Il :

- ✅ **Isole** le domaine de la persistance
- ✅ **Centralise** les requêtes de données
- ✅ **Facilite** les tests avec des implémentations en mémoire
- ✅ **Respecte** l'inversion de dépendance
- ✅ **Améliore** la maintenabilité

## 🏗️ Architecture Repository

```
Domain Layer
├── Repository/
│   └── BookRepositoryInterface.php    ← Interface (contrat)
│
Infrastructure Layer
├── Doctrine/
│   └── DoctrineBookRepository.php     ← Implémentation Doctrine
└── InMemory/
    └── InMemoryBookRepository.php     ← Implémentation test
```

## 🔌 Interface Repository

### Contrat de Base

```php
<?php

namespace App\BookStore\Domain\Repository;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\ValueObject\BookId;
use App\BookStore\Domain\ValueObject\Author;

interface BookRepositoryInterface
{
    /**
     * Persiste un livre (création ou mise à jour)
     */
    public function add(Book $book): void;

    /**
     * Supprime un livre
     */
    public function remove(Book $book): void;

    /**
     * Trouve un livre par son ID
     */
    public function ofId(BookId $id): ?Book;

    /**
     * Trouve tous les livres
     * @return Book[]
     */
    public function findAll(): array;

    /**
     * Trouve les livres d'un auteur
     * @return Book[]
     */
    public function findByAuthor(Author $author): array;

    /**
     * Trouve les livres les moins chers
     * @return Book[]
     */
    public function findCheapest(int $limit = 10): array;

    /**
     * Compte le nombre total de livres
     */
    public function count(): int;

    /**
     * Vérifie si un livre existe
     */
    public function exists(BookId $id): bool;

    /**
     * Trouve les livres avec pagination
     * @return Book[]
     */
    public function findWithPagination(int $page, int $limit): array;
}
```

### Repository Spécialisé

```php
<?php

namespace App\BookStore\Domain\Repository;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\ValueObject\Price;

interface BookSearchRepositoryInterface
{
    /**
     * Recherche textuelle dans les livres
     * @return Book[]
     */
    public function searchByText(string $query): array;

    /**
     * Trouve les livres dans une fourchette de prix
     * @return Book[]
     */
    public function findByPriceRange(Price $minPrice, Price $maxPrice): array;

    /**
     * Trouve les livres populaires
     * @return Book[]
     */
    public function findPopular(int $limit = 10): array;

    /**
     * Trouve les livres similaires
     * @return Book[]
     */
    public function findSimilar(Book $book, int $limit = 5): array;
}
```

## 🗃️ Implémentation Doctrine

### Repository Principal

```php
<?php

namespace App\BookStore\Infrastructure\Doctrine;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookId;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class DoctrineBookRepository extends ServiceEntityRepository implements BookRepositoryInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Book::class);
    }

    public function add(Book $book): void
    {
        $this->getEntityManager()->persist($book);
        $this->getEntityManager()->flush();
    }

    public function remove(Book $book): void
    {
        $this->getEntityManager()->remove($book);
        $this->getEntityManager()->flush();
    }

    public function ofId(BookId $id): ?Book
    {
        return $this->find($id->value);
    }

    public function findAll(): array
    {
        return $this->findBy([], ['name.value' => 'ASC']);
    }

    public function findByAuthor(Author $author): array
    {
        return $this->createQueryBuilder('b')
            ->where('b.author.name = :authorName')
            ->setParameter('authorName', $author->name)
            ->orderBy('b.name.value', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findCheapest(int $limit = 10): array
    {
        return $this->createQueryBuilder('b')
            ->orderBy('b.price.amount', 'ASC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function count(): int
    {
        return $this->createQueryBuilder('b')
            ->select('COUNT(b.id.value)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function exists(BookId $id): bool
    {
        return $this->createQueryBuilder('b')
            ->select('COUNT(b.id.value)')
            ->where('b.id.value = :id')
            ->setParameter('id', $id->value)
            ->getQuery()
            ->getSingleScalarResult() > 0;
    }

    public function findWithPagination(int $page, int $limit): array
    {
        $offset = ($page - 1) * $limit;

        return $this->createQueryBuilder('b')
            ->orderBy('b.name.value', 'ASC')
            ->setFirstResult($offset)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }
}
```

### Repository de Recherche

```php
<?php

namespace App\BookStore\Infrastructure\Doctrine;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\Repository\BookSearchRepositoryInterface;
use App\BookStore\Domain\ValueObject\Price;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class DoctrineBookSearchRepository extends ServiceEntityRepository implements BookSearchRepositoryInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Book::class);
    }

    public function searchByText(string $query): array
    {
        $searchTerm = '%' . strtolower($query) . '%';

        return $this->createQueryBuilder('b')
            ->where('LOWER(b.name.value) LIKE :search')
            ->orWhere('LOWER(b.description.value) LIKE :search')
            ->orWhere('LOWER(b.author.name) LIKE :search')
            ->setParameter('search', $searchTerm)
            ->orderBy('b.name.value', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findByPriceRange(Price $minPrice, Price $maxPrice): array
    {
        return $this->createQueryBuilder('b')
            ->where('b.price.amount >= :minPrice')
            ->andWhere('b.price.amount <= :maxPrice')
            ->andWhere('b.price.currency = :currency')
            ->setParameter('minPrice', $minPrice->amount)
            ->setParameter('maxPrice', $maxPrice->amount)
            ->setParameter('currency', $minPrice->currency)
            ->orderBy('b.price.amount', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findPopular(int $limit = 10): array
    {
        // Exemple avec une table de statistiques
        return $this->createQueryBuilder('b')
            ->leftJoin('App\BookStore\Domain\Model\BookStats', 's', 'WITH', 's.bookId = b.id')
            ->orderBy('s.viewCount', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function findSimilar(Book $book, int $limit = 5): array
    {
        return $this->createQueryBuilder('b')
            ->where('b.author.name = :author')
            ->andWhere('b.id.value != :currentBookId')
            ->setParameter('author', $book->getAuthor()->name)
            ->setParameter('currentBookId', $book->getId()->value)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }
}
```

## 🧪 Repository en Mémoire pour Tests

```php
<?php

namespace App\BookStore\Infrastructure\InMemory;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookId;

class InMemoryBookRepository implements BookRepositoryInterface
{
    /** @var Book[] */
    private array $books = [];

    public function add(Book $book): void
    {
        $this->books[(string) $book->getId()] = $book;
    }

    public function remove(Book $book): void
    {
        unset($this->books[(string) $book->getId()]);
    }

    public function ofId(BookId $id): ?Book
    {
        return $this->books[(string) $id] ?? null;
    }

    public function findAll(): array
    {
        $books = array_values($this->books);
        usort($books, fn(Book $a, Book $b) => 
            strcmp($a->getName()->value, $b->getName()->value)
        );
        return $books;
    }

    public function findByAuthor(Author $author): array
    {
        return array_filter(
            $this->books,
            fn(Book $book) => $book->getAuthor()->name === $author->name
        );
    }

    public function findCheapest(int $limit = 10): array
    {
        $books = array_values($this->books);
        usort($books, fn(Book $a, Book $b) => 
            $a->getPrice()->amount <=> $b->getPrice()->amount
        );
        return array_slice($books, 0, $limit);
    }

    public function count(): int
    {
        return count($this->books);
    }

    public function exists(BookId $id): bool
    {
        return isset($this->books[(string) $id]);
    }

    public function findWithPagination(int $page, int $limit): array
    {
        $books = $this->findAll();
        $offset = ($page - 1) * $limit;
        return array_slice($books, $offset, $limit);
    }

    /**
     * Méthode utilitaire pour les tests
     */
    public function clear(): void
    {
        $this->books = [];
    }

    /**
     * Méthode utilitaire pour les tests
     */
    public function addMany(Book ...$books): void
    {
        foreach ($books as $book) {
            $this->add($book);
        }
    }
}
```

## ⚙️ Configuration des Services

### Configuration Production

```php
// config/services/book_store.php
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Infrastructure\Doctrine\DoctrineBookRepository;

return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services();

    $services->set(BookRepositoryInterface::class)
        ->class(DoctrineBookRepository::class);
};
```

### Configuration Test

```php
// config/services/test/book_store.php
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Infrastructure\InMemory\InMemoryBookRepository;

return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services();

    $services->set(BookRepositoryInterface::class)
        ->class(InMemoryBookRepository::class);

    $services->set(InMemoryBookRepository::class)
        ->public(); // Pour accès direct dans les tests
};
```

## 🔄 Utilisation dans les Handlers

```php
<?php

namespace App\BookStore\Application\Command;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\Shared\Application\Command\AsCommandHandler;

#[AsCommandHandler]
final readonly class CreateBookCommandHandler
{
    public function __construct(
        private BookRepositoryInterface $bookRepository
    ) {}

    public function __invoke(CreateBookCommand $command): Book
    {
        $book = new Book(
            $command->name,
            $command->description,
            $command->author,
            $command->content,
            $command->price,
        );

        $this->bookRepository->add($book);

        return $book;
    }
}
```

## ✅ Tests avec Repository

### Test Unitaire avec Mock

```php
<?php

namespace App\Tests\BookStore\Unit\Application\Command;

use App\BookStore\Application\Command\CreateBookCommand;
use App\BookStore\Application\Command\CreateBookCommandHandler;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookContent;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Price;
use PHPUnit\Framework\TestCase;

class CreateBookCommandHandlerTest extends TestCase
{
    public function testCreateBook(): void
    {
        // Arrange
        $repository = $this->createMock(BookRepositoryInterface::class);
        $handler = new CreateBookCommandHandler($repository);

        $command = new CreateBookCommand(
            new BookName('Clean Code'),
            new BookDescription('A handbook of agile software craftsmanship'),
            new Author('Robert C. Martin'),
            new BookContent(str_repeat('Content ', 50)),
            new Price(3500, 'EUR')
        );

        // Assert
        $repository->expects($this->once())
            ->method('add')
            ->with($this->isInstanceOf(Book::class));

        // Act
        $book = $handler($command);

        // Assert
        $this->assertInstanceOf(Book::class, $book);
        $this->assertSame('Clean Code', $book->getName()->value);
    }
}
```

### Test d'Intégration avec Repository en Mémoire

```php
<?php

namespace App\Tests\BookStore\Functional;

use App\BookStore\Application\Command\CreateBookCommand;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Infrastructure\InMemory\InMemoryBookRepository;
use App\Shared\Application\Command\CommandBusInterface;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CreateBookTest extends KernelTestCase
{
    private CommandBusInterface $commandBus;
    private InMemoryBookRepository $bookRepository;

    protected function setUp(): void
    {
        self::bootKernel();
        
        $this->commandBus = self::getContainer()->get(CommandBusInterface::class);
        $this->bookRepository = self::getContainer()->get(InMemoryBookRepository::class);
        $this->bookRepository->clear(); // Nettoyer avant chaque test
    }

    public function testCreateBookSuccessfully(): void
    {
        // Arrange
        $command = DummyBookFactory::createBookCommand();

        // Act
        $book = $this->commandBus->dispatch($command);

        // Assert
        $this->assertSame(1, $this->bookRepository->count());
        $this->assertTrue($this->bookRepository->exists($book->getId()));
    }
}
```

## 🎨 Bonnes Pratiques

### ✅ À Faire
- Définir l'interface dans le domaine
- Implémenter dans l'infrastructure
- Utiliser des méthodes expressives (`ofId()` vs `findById()`)
- Créer des repositories spécialisés si nécessaire
- Tester avec des implémentations en mémoire

### ❌ À Éviter
- Exposer les détails de persistance
- Méthodes trop génériques (`find($criteria)`)
- Logique métier dans les repositories
- Dépendances vers le domaine depuis l'infrastructure
- Repositories trop larges

## 🚀 Prochaines Étapes

Maintenant que vous maîtrisez les repositories :
1. Implémenter CQRS avec Command/Query Bus
2. Orchestrer les cas d'usage
3. Écrire des tests complets
4. Optimiser les performances

## 💡 Points Clés à Retenir

- Les **repositories** isolent la persistance du domaine
- L'**interface** appartient au domaine, l'**implémentation** à l'infrastructure
- Les **repositories en mémoire** facilitent les tests
- Les **méthodes** doivent être expressives et métier
- La **configuration** permet de changer d'implémentation facilement

---

**Exercice pratique** : Créez un `OrderRepositoryInterface` avec les méthodes `findPendingOrders()`, `findByCustomer()`, et `findExpiredOrders()`, puis implémentez-le avec Doctrine et en mémoire.
