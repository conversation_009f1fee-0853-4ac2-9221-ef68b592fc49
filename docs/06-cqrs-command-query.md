# 6. CQRS - Command Query Responsibility Segregation

## 🎯 Objectifs d'apprentissage

À la fin de ce chapitre, vous serez capable de :
- Comprendre les principes de CQRS
- Séparer les commandes des requêtes
- Implémenter Command Bus et Query Bus
- Créer des handlers pour orchestrer les cas d'usage
- Utiliser Symfony Messenger pour le routing

## 📚 Qu'est-ce que CQRS ?

**CQRS** (Command Query Responsibility Segregation) sépare les opérations de lecture et d'écriture :

- **Commands** : Modifient l'état du système (écriture)
- **Queries** : Lisent les données sans les modifier (lecture)

### Avantages de CQRS

✅ **Séparation claire** des responsabilités
✅ **Optimisation** différente pour lecture/écriture
✅ **Scalabilité** indépendante
✅ **Testabilité** améliorée
✅ **Évolutivité** facilitée

## 🏗️ Architecture CQRS

```
Controller
    ↓
Command/Query Bus
    ↓
Handler
    ↓
Domain Services + Repositories
    ↓
Domain Model
```

## 🔧 Infrastructure CQRS

### Interfaces de Base

```php
<?php

namespace App\Shared\Application\Command;

/**
 * @template T
 */
interface CommandInterface
{
    // Marker interface pour les commandes
}
```

```php
<?php

namespace App\Shared\Application\Query;

/**
 * @template T
 */
interface QueryInterface
{
    // Marker interface pour les requêtes
}
```

### Command Bus

```php
<?php

namespace App\Shared\Application\Command;

interface CommandBusInterface
{
    /**
     * @template T
     * @param CommandInterface<T> $command
     * @return T
     */
    public function dispatch(CommandInterface $command): mixed;
}
```

### Query Bus

```php
<?php

namespace App\Shared\Application\Query;

interface QueryBusInterface
{
    /**
     * @template T
     * @param QueryInterface<T> $query
     * @return T
     */
    public function ask(QueryInterface $query): mixed;
}
```

## 📝 Commands - Écriture

### Command Simple

```php
<?php

namespace App\BookStore\Application\Command;

use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookContent;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Price;
use App\Shared\Application\Command\CommandInterface;

/**
 * @implements CommandInterface<Book>
 */
final readonly class CreateBookCommand implements CommandInterface
{
    public function __construct(
        public BookName $name,
        public BookDescription $description,
        public Author $author,
        public BookContent $content,
        public Price $price,
    ) {}
}
```

### Command Handler

```php
<?php

namespace App\BookStore\Application\Command;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\Shared\Application\Command\AsCommandHandler;

#[AsCommandHandler]
final readonly class CreateBookCommandHandler
{
    public function __construct(
        private BookRepositoryInterface $bookRepository
    ) {}

    public function __invoke(CreateBookCommand $command): Book
    {
        $book = new Book(
            $command->name,
            $command->description,
            $command->author,
            $command->content,
            $command->price,
        );

        $this->bookRepository->add($book);

        return $book;
    }
}
```

### Command avec Validation

```php
<?php

namespace App\BookStore\Application\Command;

use App\BookStore\Domain\ValueObject\BookId;
use App\BookStore\Domain\ValueObject\Discount;
use App\Shared\Application\Command\CommandInterface;

/**
 * @implements CommandInterface<void>
 */
final readonly class DiscountBookCommand implements CommandInterface
{
    public function __construct(
        public BookId $id,
        public Discount $discount,
    ) {}
}
```

```php
<?php

namespace App\BookStore\Application\Command;

use App\BookStore\Domain\Exception\MissingBookException;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\Shared\Application\Command\AsCommandHandler;

#[AsCommandHandler]
final readonly class DiscountBookCommandHandler
{
    public function __construct(
        private BookRepositoryInterface $bookRepository
    ) {}

    public function __invoke(DiscountBookCommand $command): void
    {
        $book = $this->bookRepository->ofId($command->id);
        if (null === $book) {
            throw new MissingBookException($command->id);
        }

        $book->applyDiscount($command->discount);
    }
}
```

## 📖 Queries - Lecture

### Query Simple

```php
<?php

namespace App\BookStore\Application\Query;

use App\BookStore\Domain\ValueObject\BookId;
use App\Shared\Application\Query\QueryInterface;

/**
 * @implements QueryInterface<BookDTO|null>
 */
final readonly class FindBookQuery implements QueryInterface
{
    public function __construct(
        public BookId $id
    ) {}
}
```

### Query Handler

```php
<?php

namespace App\BookStore\Application\Query;

use App\BookStore\Application\DTO\BookDTO;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\Shared\Application\Query\AsQueryHandler;

#[AsQueryHandler]
final readonly class FindBookQueryHandler
{
    public function __construct(
        private BookRepositoryInterface $bookRepository
    ) {}

    public function __invoke(FindBookQuery $query): ?BookDTO
    {
        $book = $this->bookRepository->ofId($query->id);
        
        return $book ? BookDTO::fromBook($book) : null;
    }
}
```

### Query avec Critères

```php
<?php

namespace App\BookStore\Application\Query;

use App\Shared\Application\Query\QueryInterface;

/**
 * @implements QueryInterface<BookDTO[]>
 */
final readonly class FindCheapestBooksQuery implements QueryInterface
{
    public function __construct(
        public int $limit = 10,
        public ?string $currency = null,
    ) {}
}
```

```php
<?php

namespace App\BookStore\Application\Query;

use App\BookStore\Application\DTO\BookDTO;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\Shared\Application\Query\AsQueryHandler;

#[AsQueryHandler]
final readonly class FindCheapestBooksQueryHandler
{
    public function __construct(
        private BookRepositoryInterface $bookRepository
    ) {}

    public function __invoke(FindCheapestBooksQuery $query): array
    {
        $books = $this->bookRepository->findCheapest($query->limit);
        
        return array_map(
            fn($book) => BookDTO::fromBook($book),
            $books
        );
    }
}
```

## 📦 Data Transfer Objects (DTOs)

### DTO Simple

```php
<?php

namespace App\BookStore\Application\DTO;

use App\BookStore\Domain\Model\Book;

final readonly class BookDTO
{
    public function __construct(
        public string $id,
        public string $name,
        public string $description,
        public string $author,
        public float $price,
        public string $currency,
    ) {}

    public static function fromBook(Book $book): self
    {
        return new self(
            id: (string) $book->getId(),
            name: $book->getName()->value,
            description: $book->getDescription()->value,
            author: $book->getAuthor()->name,
            price: $book->getPrice()->toFloat(),
            currency: $book->getPrice()->currency,
        );
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'author' => $this->author,
            'price' => $this->price,
            'currency' => $this->currency,
        ];
    }
}
```

### DTO Collection

```php
<?php

namespace App\BookStore\Application\DTO;

final readonly class BookCollectionDTO
{
    /** @param BookDTO[] $books */
    public function __construct(
        public array $books,
        public int $total,
        public int $page,
        public int $limit,
    ) {}

    public function toArray(): array
    {
        return [
            'books' => array_map(fn(BookDTO $book) => $book->toArray(), $this->books),
            'pagination' => [
                'total' => $this->total,
                'page' => $this->page,
                'limit' => $this->limit,
                'pages' => (int) ceil($this->total / $this->limit),
            ],
        ];
    }
}
```

## ⚙️ Implémentation avec Symfony Messenger

### Command Bus

```php
<?php

namespace App\Shared\Infrastructure\Symfony\Messenger;

use App\Shared\Application\Command\CommandBusInterface;
use App\Shared\Application\Command\CommandInterface;
use Symfony\Component\Messenger\Exception\HandlerFailedException;
use Symfony\Component\Messenger\HandleTrait;
use Symfony\Component\Messenger\MessageBusInterface;

final class MessengerCommandBus implements CommandBusInterface
{
    use HandleTrait;

    public function __construct(MessageBusInterface $commandBus)
    {
        $this->messageBus = $commandBus;
    }

    public function dispatch(CommandInterface $command): mixed
    {
        try {
            return $this->handle($command);
        } catch (HandlerFailedException $e) {
            if ($exception = current($e->getWrappedExceptions())) {
                throw $exception;
            }
            throw $e;
        }
    }
}
```

### Query Bus

```php
<?php

namespace App\Shared\Infrastructure\Symfony\Messenger;

use App\Shared\Application\Query\QueryBusInterface;
use App\Shared\Application\Query\QueryInterface;
use Symfony\Component\Messenger\Exception\HandlerFailedException;
use Symfony\Component\Messenger\HandleTrait;
use Symfony\Component\Messenger\MessageBusInterface;

final class MessengerQueryBus implements QueryBusInterface
{
    use HandleTrait;

    public function __construct(MessageBusInterface $queryBus)
    {
        $this->messageBus = $queryBus;
    }

    public function ask(QueryInterface $query): mixed
    {
        try {
            return $this->handle($query);
        } catch (HandlerFailedException $e) {
            if ($exception = current($e->getWrappedExceptions())) {
                throw $exception;
            }
            throw $e;
        }
    }
}
```

## 🔧 Configuration Symfony

### Configuration Messenger

```php
// config/packages/messenger.php
use App\Shared\Application\Command\CommandInterface;
use App\Shared\Application\Query\QueryInterface;

return static function (ContainerConfigurator $containerConfigurator): void {
    $containerConfigurator->extension('framework', [
        'messenger' => [
            'default_bus' => 'command.bus',
            'buses' => [
                'command.bus' => [
                    'middleware' => [
                        'validation',
                        'doctrine_transaction',
                    ],
                ],
                'query.bus' => [
                    'middleware' => [
                        'validation',
                    ],
                ],
            ],
            'routing' => [
                CommandInterface::class => 'command.bus',
                QueryInterface::class => 'query.bus',
            ],
        ],
    ]);
};
```

### Auto-configuration des Handlers

```php
// Dans Kernel.php
protected function build(ContainerBuilder $container): void
{
    $container->registerAttributeForAutoconfiguration(
        AsQueryHandler::class,
        static function (ChildDefinition $definition): void {
            $definition->addTag('messenger.message_handler', ['bus' => 'query.bus']);
        }
    );

    $container->registerAttributeForAutoconfiguration(
        AsCommandHandler::class,
        static function (ChildDefinition $definition): void {
            $definition->addTag('messenger.message_handler', ['bus' => 'command.bus']);
        }
    );
}
```

## 🎮 Utilisation dans les Controllers

### Controller avec CQRS

```php
<?php

namespace App\BookStore\Infrastructure\ApiPlatform\State\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\BookStore\Application\Command\CreateBookCommand;
use App\BookStore\Infrastructure\ApiPlatform\Resource\BookResource;
use App\Shared\Application\Command\CommandBusInterface;

final readonly class CreateBookProcessor implements ProcessorInterface
{
    public function __construct(
        private CommandBusInterface $commandBus
    ) {}

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): BookResource
    {
        assert($data instanceof BookResource);

        $command = new CreateBookCommand(
            $data->name,
            $data->description,
            $data->author,
            $data->content,
            $data->price,
        );

        $book = $this->commandBus->dispatch($command);

        return BookResource::fromBook($book);
    }
}
```

## ✅ Tests CQRS

### Test Command Handler

```php
<?php

namespace App\Tests\BookStore\Unit\Application\Command;

use App\BookStore\Application\Command\CreateBookCommand;
use App\BookStore\Application\Command\CreateBookCommandHandler;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;
use PHPUnit\Framework\TestCase;

class CreateBookCommandHandlerTest extends TestCase
{
    public function testCreateBookSuccessfully(): void
    {
        // Arrange
        $repository = $this->createMock(BookRepositoryInterface::class);
        $handler = new CreateBookCommandHandler($repository);
        $command = DummyBookFactory::createBookCommand();

        // Assert
        $repository->expects($this->once())->method('add');

        // Act
        $book = $handler($command);

        // Assert
        $this->assertSame($command->name->value, $book->getName()->value);
    }
}
```

### Test Query Handler

```php
<?php

namespace App\Tests\BookStore\Unit\Application\Query;

use App\BookStore\Application\DTO\BookDTO;
use App\BookStore\Application\Query\FindBookQuery;
use App\BookStore\Application\Query\FindBookQueryHandler;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;
use PHPUnit\Framework\TestCase;

class FindBookQueryHandlerTest extends TestCase
{
    public function testFindExistingBook(): void
    {
        // Arrange
        $book = DummyBookFactory::createBook();
        $repository = $this->createMock(BookRepositoryInterface::class);
        $repository->method('ofId')->willReturn($book);
        
        $handler = new FindBookQueryHandler($repository);
        $query = new FindBookQuery($book->getId());

        // Act
        $result = $handler($query);

        // Assert
        $this->assertInstanceOf(BookDTO::class, $result);
        $this->assertSame($book->getName()->value, $result->name);
    }

    public function testFindNonExistingBook(): void
    {
        // Arrange
        $repository = $this->createMock(BookRepositoryInterface::class);
        $repository->method('ofId')->willReturn(null);
        
        $handler = new FindBookQueryHandler($repository);
        $query = new FindBookQuery(DummyBookFactory::createBookId());

        // Act
        $result = $handler($query);

        // Assert
        $this->assertNull($result);
    }
}
```

## 🎨 Bonnes Pratiques

### ✅ À Faire
- Séparer clairement commands et queries
- Utiliser des DTOs pour les réponses
- Valider dans les handlers
- Tester chaque handler unitairement
- Nommer explicitement (`CreateBook` vs `SaveBook`)

### ❌ À Éviter
- Queries qui modifient l'état
- Commands qui retournent des données complexes
- Logique métier dans les commands/queries
- Handlers trop complexes
- Couplage entre commands et queries

## 🚀 Prochaines Étapes

Maintenant que vous maîtrisez CQRS :
1. Écrire des tests unitaires complets
2. Implémenter des tests d'intégration
3. Configurer l'injection de dépendances
4. Optimiser les performances

## 💡 Points Clés à Retenir

- **CQRS** sépare lecture et écriture pour plus de clarté
- Les **Commands** modifient, les **Queries** lisent
- Les **Handlers** orchestrent les cas d'usage
- Les **DTOs** transportent les données
- **Symfony Messenger** facilite l'implémentation

---

**Exercice pratique** : Créez une command `UpdateBookCommand` avec son handler, et une query `SearchBooksQuery` qui recherche par titre et auteur avec pagination.
