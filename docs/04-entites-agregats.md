# 4. Entités et Agrégats - Le Cœur du Métier

## 🎯 Objectifs d'apprentissage

À la fin de ce chapitre, vous serez capable de :
- Distinguer Entités, Agrégats et Value Objects
- Créer des entités avec identité et cycle de vie
- Modéliser des agrégats avec leurs invariants
- Implémenter les règles métier dans le domaine
- Gérer la cohérence des données

## 📚 Concepts Fondamentaux

### Entité vs Value Object vs Agrégat

| Concept | Identité | Mutabilité | Responsabilité |
|---------|----------|------------|----------------|
| **Value Object** | ❌ Non | ❌ Immuable | Validation, Calculs |
| **Entité** | ✅ Oui | ✅ Mutable | Cycle de vie, État |
| **Agrégat** | ✅ Oui | ✅ Mutable | Invariants, Cohérence |

### Caractéristiques d'une Entité
- ✅ **Identité unique** : distinguée par son ID
- ✅ **Cycle de vie** : création, modification, suppression
- ✅ **État mutable** : peut changer au cours du temps
- ✅ **Règles métier** : encapsule la logique business
- ✅ **Invariants** : maintient sa cohérence interne

## 🏗️ Anatomie d'une Entité

Analysons l'entité `Book` de votre projet :

```php
<?php

namespace App\BookStore\Domain\Model;

use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookContent;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookId;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Discount;
use App\BookStore\Domain\ValueObject\Price;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class Book
{
    #[ORM\Embedded(columnPrefix: false)]
    private readonly BookId $id;

    public function __construct(
        #[ORM\Embedded(columnPrefix: false)]
        private BookName $name,

        #[ORM\Embedded(columnPrefix: false)]
        private BookDescription $description,

        #[ORM\Embedded(columnPrefix: false)]
        private Author $author,

        #[ORM\Embedded(columnPrefix: false)]
        private BookContent $content,

        #[ORM\Embedded(columnPrefix: false)]
        private Price $price,
    ) {
        $this->id = new BookId();
    }

    // Getters pour accéder aux propriétés
    public function getId(): BookId
    {
        return $this->id;
    }

    public function getName(): BookName
    {
        return $this->name;
    }

    public function getPrice(): Price
    {
        return $this->price;
    }

    public function getAuthor(): Author
    {
        return $this->author;
    }

    // Méthodes métier qui encapsulent les règles business
    public function applyDiscount(Discount $discount): void
    {
        $this->guardAgainstExcessiveDiscount($discount);
        $this->price = $this->price->applyDiscount($discount);
    }

    public function updateContent(BookContent $newContent): void
    {
        $this->guardAgainstEmptyContent($newContent);
        $this->content = $newContent;
    }

    public function changeAuthor(Author $newAuthor): void
    {
        $this->author = $newAuthor;
    }

    public function rename(BookName $newName): void
    {
        $this->name = $newName;
    }

    // Invariants - règles qui doivent toujours être respectées
    private function guardAgainstExcessiveDiscount(Discount $discount): void
    {
        if ($discount->percentage > 50) {
            throw new \DomainException('Discount cannot exceed 50%');
        }
    }

    private function guardAgainstEmptyContent(BookContent $content): void
    {
        if (strlen($content->value) < 100) {
            throw new \DomainException('Book content must be at least 100 characters');
        }
    }

    // Méthodes de comparaison
    public function equals(Book $other): bool
    {
        return $this->id->equals($other->id);
    }

    public function isSameBook(Book $other): bool
    {
        return $this->name->value === $other->name->value 
            && $this->author->equals($other->author);
    }
}
```

## 🎯 Agrégats et Racines d'Agrégat

### Qu'est-ce qu'un Agrégat ?

Un **Agrégat** est un groupe d'entités et de value objects qui :
- ✅ Forment une **unité de cohérence**
- ✅ Ont une **racine d'agrégat** (Aggregate Root)
- ✅ Maintiennent des **invariants** entre leurs composants
- ✅ Sont la **frontière transactionnelle**

### Exemple : Agrégat Order

```php
<?php

namespace App\BookStore\Domain\Model;

use App\BookStore\Domain\ValueObject\OrderId;
use App\BookStore\Domain\ValueObject\CustomerId;
use App\BookStore\Domain\ValueObject\OrderStatus;
use App\BookStore\Domain\ValueObject\Price;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class Order // Racine d'agrégat
{
    #[ORM\Embedded(columnPrefix: false)]
    private readonly OrderId $id;

    #[ORM\Embedded(columnPrefix: false)]
    private OrderStatus $status;

    /** @var OrderItem[] */
    #[ORM\OneToMany(mappedBy: 'order', targetEntity: OrderItem::class, cascade: ['persist', 'remove'])]
    private array $items = [];

    public function __construct(
        #[ORM\Embedded(columnPrefix: false)]
        private readonly CustomerId $customerId,
    ) {
        $this->id = new OrderId();
        $this->status = OrderStatus::pending();
    }

    public function addBook(Book $book, int $quantity = 1): void
    {
        $this->guardAgainstModificationWhenNotPending();
        $this->guardAgainstInvalidQuantity($quantity);

        $existingItem = $this->findItemForBook($book);
        
        if ($existingItem) {
            $existingItem->increaseQuantity($quantity);
        } else {
            $this->items[] = new OrderItem($this, $book, $quantity);
        }
    }

    public function removeBook(Book $book): void
    {
        $this->guardAgainstModificationWhenNotPending();

        $this->items = array_filter(
            $this->items,
            fn(OrderItem $item) => !$item->isForBook($book)
        );
    }

    public function confirm(): void
    {
        $this->guardAgainstEmptyOrder();
        $this->guardAgainstAlreadyConfirmed();

        $this->status = OrderStatus::confirmed();
    }

    public function cancel(): void
    {
        $this->guardAgainstCancellationWhenShipped();
        $this->status = OrderStatus::cancelled();
    }

    public function getTotalPrice(): Price
    {
        $total = 0;
        foreach ($this->items as $item) {
            $total += $item->getTotalPrice()->amount;
        }

        return new Price($total);
    }

    // Invariants
    private function guardAgainstModificationWhenNotPending(): void
    {
        if (!$this->status->isPending()) {
            throw new \DomainException('Cannot modify a non-pending order');
        }
    }

    private function guardAgainstEmptyOrder(): void
    {
        if (empty($this->items)) {
            throw new \DomainException('Cannot confirm an empty order');
        }
    }

    private function guardAgainstInvalidQuantity(int $quantity): void
    {
        if ($quantity <= 0) {
            throw new \DomainException('Quantity must be positive');
        }
    }

    private function findItemForBook(Book $book): ?OrderItem
    {
        foreach ($this->items as $item) {
            if ($item->isForBook($book)) {
                return $item;
            }
        }
        return null;
    }

    // Getters
    public function getId(): OrderId { return $this->id; }
    public function getCustomerId(): CustomerId { return $this->customerId; }
    public function getStatus(): OrderStatus { return $this->status; }
    public function getItems(): array { return $this->items; }
}
```

### Entité Enfant dans l'Agrégat

```php
<?php

namespace App\BookStore\Domain\Model;

use App\BookStore\Domain\ValueObject\Price;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class OrderItem // Entité enfant
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Order::class, inversedBy: 'items')]
    private Order $order;

    #[ORM\ManyToOne(targetEntity: Book::class)]
    private Book $book;

    #[ORM\Column(type: 'integer')]
    private int $quantity;

    #[ORM\Embedded(columnPrefix: false)]
    private Price $unitPrice;

    public function __construct(Order $order, Book $book, int $quantity)
    {
        $this->order = $order;
        $this->book = $book;
        $this->quantity = $quantity;
        $this->unitPrice = $book->getPrice(); // Prix au moment de l'ajout
    }

    public function increaseQuantity(int $amount): void
    {
        if ($amount <= 0) {
            throw new \DomainException('Amount must be positive');
        }
        $this->quantity += $amount;
    }

    public function getTotalPrice(): Price
    {
        return new Price($this->unitPrice->amount * $this->quantity);
    }

    public function isForBook(Book $book): bool
    {
        return $this->book->equals($book);
    }

    // Getters
    public function getBook(): Book { return $this->book; }
    public function getQuantity(): int { return $this->quantity; }
    public function getUnitPrice(): Price { return $this->unitPrice; }
}
```

## 🔒 Services de Domaine

Quand la logique métier ne trouve pas sa place dans une entité :

```php
<?php

namespace App\BookStore\Domain\Service;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\ValueObject\Discount;
use App\BookStore\Domain\ValueObject\Price;

final class BookDiscountService
{
    public function calculateBulkDiscount(array $books): Discount
    {
        $totalValue = $this->calculateTotalValue($books);
        
        return match (true) {
            $totalValue->amount >= 10000 => new Discount(15.0), // 15% pour 100€+
            $totalValue->amount >= 5000 => new Discount(10.0),  // 10% pour 50€+
            $totalValue->amount >= 2000 => new Discount(5.0),   // 5% pour 20€+
            default => new Discount(0.0)
        };
    }

    public function canApplyAuthorDiscount(Book $book, string $customerEmail): bool
    {
        // Règle métier : remise auteur si email contient le nom de l'auteur
        $authorName = strtolower($book->getAuthor()->name);
        $emailLocalPart = strtolower(substr($customerEmail, 0, strpos($customerEmail, '@')));
        
        return str_contains($emailLocalPart, $authorName);
    }

    private function calculateTotalValue(array $books): Price
    {
        $total = 0;
        foreach ($books as $book) {
            $total += $book->getPrice()->amount;
        }
        
        return new Price($total);
    }
}
```

## 🎭 Events de Domaine

Pour découpler les effets de bord :

```php
<?php

namespace App\BookStore\Domain\Event;

final readonly class BookDiscountApplied
{
    public function __construct(
        public BookId $bookId,
        public Discount $discount,
        public Price $oldPrice,
        public Price $newPrice,
        public \DateTimeImmutable $occurredAt = new \DateTimeImmutable(),
    ) {}
}
```

```php
// Dans l'entité Book
public function applyDiscount(Discount $discount): void
{
    $this->guardAgainstExcessiveDiscount($discount);
    
    $oldPrice = $this->price;
    $this->price = $this->price->applyDiscount($discount);
    
    // Enregistrer l'événement
    $this->recordEvent(new BookDiscountApplied(
        $this->id,
        $discount,
        $oldPrice,
        $this->price
    ));
}
```

## ✅ Tests d'Entités et d'Agrégats

```php
<?php

namespace App\Tests\BookStore\Unit\Domain\Model;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookContent;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Discount;
use App\BookStore\Domain\ValueObject\Price;
use PHPUnit\Framework\TestCase;

class BookTest extends TestCase
{
    private Book $book;

    protected function setUp(): void
    {
        $this->book = new Book(
            new BookName('Clean Code'),
            new BookDescription('A handbook of agile software craftsmanship'),
            new Author('Robert C. Martin'),
            new BookContent(str_repeat('Content ', 50)), // 400 chars
            new Price(3500, 'EUR') // 35.00 EUR
        );
    }

    public function testCreateBook(): void
    {
        $this->assertInstanceOf(Book::class, $this->book);
        $this->assertSame('Clean Code', $this->book->getName()->value);
        $this->assertSame(3500, $this->book->getPrice()->amount);
    }

    public function testApplyValidDiscount(): void
    {
        $discount = new Discount(20.0);
        
        $this->book->applyDiscount($discount);
        
        $this->assertSame(2800, $this->book->getPrice()->amount); // 28.00 EUR
    }

    public function testRejectExcessiveDiscount(): void
    {
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Discount cannot exceed 50%');
        
        $excessiveDiscount = new Discount(60.0);
        $this->book->applyDiscount($excessiveDiscount);
    }

    public function testUpdateValidContent(): void
    {
        $newContent = new BookContent(str_repeat('New content ', 50));
        
        $this->book->updateContent($newContent);
        
        // Pas d'exception = succès
        $this->assertTrue(true);
    }

    public function testRejectTooShortContent(): void
    {
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Book content must be at least 100 characters');
        
        $shortContent = new BookContent('Too short');
        $this->book->updateContent($shortContent);
    }

    public function testBookEquality(): void
    {
        $sameBook = new Book(
            new BookName('Different Name'),
            new BookDescription('Different Description'),
            new Author('Different Author'),
            new BookContent(str_repeat('Different ', 50)),
            new Price(1000, 'EUR')
        );

        // Même ID = même livre
        $this->assertTrue($this->book->equals($this->book));
        // ID différent = livre différent
        $this->assertFalse($this->book->equals($sameBook));
    }
}
```

## 🎨 Bonnes Pratiques

### ✅ À Faire
- Encapsuler les règles métier dans les entités
- Utiliser des méthodes expressives (`applyDiscount()` vs `setPrice()`)
- Valider les invariants avec des méthodes `guard`
- Préférer l'immutabilité quand possible
- Tester tous les invariants

### ❌ À Éviter
- Entités anémiques (que des getters/setters)
- Logique métier dans les services applicatifs
- Agrégats trop larges
- Modification directe des propriétés
- Dépendances vers l'infrastructure

## 🚀 Prochaines Étapes

Maintenant que vous maîtrisez les entités et agrégats :
1. Implémenter les Repositories pour la persistance
2. Orchestrer avec CQRS et les Command/Query
3. Écrire des tests complets
4. Configurer l'injection de dépendances

## 💡 Points Clés à Retenir

- Les **entités** ont une identité et encapsulent les règles métier
- Les **agrégats** maintiennent la cohérence entre entités liées
- Les **invariants** protègent l'intégrité des données
- Les **services de domaine** gèrent la logique inter-entités
- Les **tests** valident les règles business

---

**Exercice pratique** : Modélisez un agrégat `Library` qui gère les emprunts de livres avec les règles : maximum 3 livres par utilisateur, durée d'emprunt limitée, amendes pour retard.
