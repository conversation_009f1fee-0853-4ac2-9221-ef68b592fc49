# 8. Tests d'Intégration et d'Acceptance

## 🎯 Objectifs d'apprentissage

À la fin de ce chapitre, vous serez capable de :
- Distinguer les différents types de tests d'intégration
- Écrire des tests fonctionnels avec Symfony
- Créer des tests d'acceptance avec API Platform
- Configurer les environnements de test
- Gérer les données de test et les fixtures

## 📚 Types de Tests d'Intégration

### Hiérarchie des Tests

```
Tests d'Acceptance (End-to-End)
    ↓ Testent l'application complète via HTTP
Tests Fonctionnels (Integration)
    ↓ Testent plusieurs couches ensemble
Tests Unitaires
    ↓ Testent une seule unité isolée
```

### Caractéristiques

| Type | Scope | Vitesse | Fiabilité | Maintenance |
|------|-------|---------|-----------|-------------|
| **Unitaires** | Classe/Méthode | ⚡ Très rapide | 🟡 Moyenne | ✅ Facile |
| **Fonctionnels** | Use Case complet | 🐌 Moyen | 🟢 Bonne | 🟡 Moyenne |
| **Acceptance** | Application complète | 🐌 Lent | 🟢 Excellente | 🔴 Difficile |

## 🔧 Configuration de l'Environnement de Test

### Configuration PHPUnit

```xml
<!-- phpunit.xml.dist -->
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="tests/bootstrap.php"
         colors="true">
    
    <php>
        <ini name="display_errors" value="1" />
        <ini name="error_reporting" value="-1" />
        <server name="APP_ENV" value="test" force="true" />
        <server name="SHELL_VERBOSITY" value="-1" />
        <server name="SYMFONY_PHPUNIT_REMOVE" value="" />
        <server name="SYMFONY_PHPUNIT_VERSION" value="9.5" />
    </php>

    <testsuites>
        <testsuite name="Unit">
            <directory>tests/*/Unit</directory>
        </testsuite>
        <testsuite name="Functional">
            <directory>tests/*/Functional</directory>
        </testsuite>
        <testsuite name="Acceptance">
            <directory>tests/*/Acceptance</directory>
        </testsuite>
    </testsuites>

    <coverage processUncoveredFiles="true">
        <include>
            <directory suffix=".php">src</directory>
        </include>
        <exclude>
            <directory>src/*/Infrastructure/Symfony</directory>
        </exclude>
    </coverage>
</phpunit>
```

### Bootstrap de Test

```php
<?php
// tests/bootstrap.php

use Symfony\Component\Dotenv\Dotenv;

require dirname(__DIR__).'/vendor/autoload.php';

if (file_exists(dirname(__DIR__).'/config/bootstrap.php')) {
    require dirname(__DIR__).'/config/bootstrap.php';
} elseif (method_exists(Dotenv::class, 'bootEnv')) {
    (new Dotenv())->bootEnv(dirname(__DIR__).'/.env');
}

// Nettoyer la base de données de test avant les tests
if (isset($_ENV['DATABASE_URL'])) {
    echo "Setting up test database...\n";
}
```

## 🧪 Tests Fonctionnels

### Test de Command Handler avec Repository Réel

```php
<?php

namespace App\Tests\BookStore\Functional;

use App\BookStore\Application\Command\CreateBookCommand;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Infrastructure\InMemory\InMemoryBookRepository;
use App\Shared\Application\Command\CommandBusInterface;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CreateBookTest extends KernelTestCase
{
    private CommandBusInterface $commandBus;
    private InMemoryBookRepository $bookRepository;

    protected function setUp(): void
    {
        self::bootKernel();
        
        $this->commandBus = self::getContainer()->get(CommandBusInterface::class);
        $this->bookRepository = self::getContainer()->get(InMemoryBookRepository::class);
        
        // Nettoyer le repository avant chaque test
        $this->bookRepository->clear();
    }

    public function testCreateBookSuccessfully(): void
    {
        // Arrange
        $command = DummyBookFactory::createBookCommand(
            name: 'Domain-Driven Design',
            author: 'Eric Evans',
            price: 5500
        );

        // Act
        $book = $this->commandBus->dispatch($command);

        // Assert
        $this->assertSame(1, $this->bookRepository->count());
        $this->assertTrue($this->bookRepository->exists($book->getId()));
        
        $persistedBook = $this->bookRepository->ofId($book->getId());
        $this->assertNotNull($persistedBook);
        $this->assertSame('Domain-Driven Design', $persistedBook->getName()->value);
        $this->assertSame('Eric Evans', $persistedBook->getAuthor()->name);
        $this->assertSame(5500, $persistedBook->getPrice()->amount);
    }

    public function testCreateMultipleBooks(): void
    {
        // Arrange
        $command1 = DummyBookFactory::createBookCommand(name: 'Book 1');
        $command2 = DummyBookFactory::createBookCommand(name: 'Book 2');

        // Act
        $book1 = $this->commandBus->dispatch($command1);
        $book2 = $this->commandBus->dispatch($command2);

        // Assert
        $this->assertSame(2, $this->bookRepository->count());
        $this->assertFalse($book1->equals($book2));
        
        $allBooks = $this->bookRepository->findAll();
        $this->assertCount(2, $allBooks);
    }
}
```

### Test de Query Handler avec Données

```php
<?php

namespace App\Tests\BookStore\Functional;

use App\BookStore\Application\Query\FindCheapestBooksQuery;
use App\BookStore\Infrastructure\InMemory\InMemoryBookRepository;
use App\Shared\Application\Query\QueryBusInterface;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class FindCheapestBooksTest extends KernelTestCase
{
    private QueryBusInterface $queryBus;
    private InMemoryBookRepository $bookRepository;

    protected function setUp(): void
    {
        self::bootKernel();
        
        $this->queryBus = self::getContainer()->get(QueryBusInterface::class);
        $this->bookRepository = self::getContainer()->get(InMemoryBookRepository::class);
        $this->bookRepository->clear();
    }

    public function testFindCheapestBooks(): void
    {
        // Arrange - Créer des livres avec différents prix
        $expensiveBook = DummyBookFactory::createBook(name: 'Expensive Book', price: 5000);
        $cheapBook = DummyBookFactory::createBook(name: 'Cheap Book', price: 1000);
        $mediumBook = DummyBookFactory::createBook(name: 'Medium Book', price: 3000);

        $this->bookRepository->addMany($expensiveBook, $cheapBook, $mediumBook);

        // Act
        $query = new FindCheapestBooksQuery(limit: 2);
        $result = $this->queryBus->ask($query);

        // Assert
        $this->assertCount(2, $result);
        $this->assertSame('Cheap Book', $result[0]->name);
        $this->assertSame('Medium Book', $result[1]->name);
        $this->assertSame(10.0, $result[0]->price);
        $this->assertSame(30.0, $result[1]->price);
    }

    public function testFindCheapestBooksWithEmptyRepository(): void
    {
        // Act
        $query = new FindCheapestBooksQuery();
        $result = $this->queryBus->ask($query);

        // Assert
        $this->assertEmpty($result);
    }
}
```

### Test de Workflow Complet

```php
<?php

namespace App\Tests\BookStore\Functional;

use App\BookStore\Application\Command\CreateBookCommand;
use App\BookStore\Application\Command\DiscountBookCommand;
use App\BookStore\Application\Query\FindBookQuery;
use App\BookStore\Domain\ValueObject\Discount;
use App\BookStore\Infrastructure\InMemory\InMemoryBookRepository;
use App\Shared\Application\Command\CommandBusInterface;
use App\Shared\Application\Query\QueryBusInterface;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class BookWorkflowTest extends KernelTestCase
{
    private CommandBusInterface $commandBus;
    private QueryBusInterface $queryBus;
    private InMemoryBookRepository $bookRepository;

    protected function setUp(): void
    {
        self::bootKernel();
        
        $this->commandBus = self::getContainer()->get(CommandBusInterface::class);
        $this->queryBus = self::getContainer()->get(QueryBusInterface::class);
        $this->bookRepository = self::getContainer()->get(InMemoryBookRepository::class);
        $this->bookRepository->clear();
    }

    public function testCompleteBookLifecycle(): void
    {
        // 1. Créer un livre
        $createCommand = DummyBookFactory::createBookCommand(
            name: 'Clean Architecture',
            author: 'Robert C. Martin',
            price: 4000
        );

        $book = $this->commandBus->dispatch($createCommand);
        $this->assertSame(4000, $book->getPrice()->amount);

        // 2. Vérifier qu'il est bien persisté
        $findQuery = new FindBookQuery($book->getId());
        $bookDTO = $this->queryBus->ask($findQuery);
        
        $this->assertNotNull($bookDTO);
        $this->assertSame('Clean Architecture', $bookDTO->name);
        $this->assertSame(40.0, $bookDTO->price);

        // 3. Appliquer une remise
        $discountCommand = new DiscountBookCommand($book->getId(), new Discount(25.0));
        $this->commandBus->dispatch($discountCommand);

        // 4. Vérifier que la remise a été appliquée
        $updatedBookDTO = $this->queryBus->ask($findQuery);
        $this->assertSame(30.0, $updatedBookDTO->price); // 40 * 0.75 = 30

        // 5. Vérifier dans le repository
        $persistedBook = $this->bookRepository->ofId($book->getId());
        $this->assertSame(3000, $persistedBook->getPrice()->amount);
    }
}
```

## 🌐 Tests d'Acceptance (End-to-End)

### Test API avec ApiTestCase

```php
<?php

namespace App\Tests\BookStore\Acceptance;

use ApiPlatform\Symfony\Bundle\Test\ApiTestCase;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Infrastructure\ApiPlatform\Resource\BookResource;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;
use Symfony\Component\HttpFoundation\Response;

class BookCrudTest extends ApiTestCase
{
    private BookRepositoryInterface $bookRepository;

    protected function setUp(): void
    {
        $this->bookRepository = self::getContainer()->get(BookRepositoryInterface::class);
        
        // Nettoyer la base de données
        if (method_exists($this->bookRepository, 'clear')) {
            $this->bookRepository->clear();
        }
    }

    public function testCreateBook(): void
    {
        // Arrange
        $bookData = [
            'name' => 'Test Book',
            'description' => 'A test book description',
            'author' => 'Test Author',
            'content' => str_repeat('Test content. ', 20),
            'price' => 2500,
            'currency' => 'EUR',
        ];

        // Act
        $response = static::createClient()->request('POST', '/api/books', [
            'json' => $bookData,
            'headers' => ['Content-Type' => 'application/json'],
        ]);

        // Assert
        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        $this->assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        $responseData = $response->toArray();
        $this->assertArrayHasKey('@id', $responseData);
        $this->assertSame('Test Book', $responseData['name']);
        $this->assertSame('Test Author', $responseData['author']);
        $this->assertSame(25.0, $responseData['price']);
    }

    public function testGetBook(): void
    {
        // Arrange - Créer un livre via le repository
        $book = DummyBookFactory::createBook(
            name: 'Existing Book',
            author: 'Existing Author'
        );
        $this->bookRepository->add($book);

        // Act
        $response = static::createClient()->request('GET', '/api/books/' . $book->getId());

        // Assert
        $this->assertResponseIsSuccessful();
        
        $responseData = $response->toArray();
        $this->assertSame('Existing Book', $responseData['name']);
        $this->assertSame('Existing Author', $responseData['author']);
    }

    public function testGetBookCollection(): void
    {
        // Arrange - Créer plusieurs livres
        $book1 = DummyBookFactory::createBook(name: 'Book 1', price: 1000);
        $book2 = DummyBookFactory::createBook(name: 'Book 2', price: 2000);
        
        $this->bookRepository->addMany($book1, $book2);

        // Act
        $response = static::createClient()->request('GET', '/api/books');

        // Assert
        $this->assertResponseIsSuccessful();
        
        $responseData = $response->toArray();
        $this->assertArrayHasKey('hydra:member', $responseData);
        $this->assertCount(2, $responseData['hydra:member']);
        
        $books = $responseData['hydra:member'];
        $this->assertSame('Book 1', $books[0]['name']);
        $this->assertSame('Book 2', $books[1]['name']);
    }

    public function testApplyDiscount(): void
    {
        // Arrange
        $book = DummyBookFactory::createBook(price: 4000);
        $this->bookRepository->add($book);

        // Act
        $response = static::createClient()->request('PATCH', '/api/books/' . $book->getId() . '/discount', [
            'json' => ['discount' => 20.0],
            'headers' => ['Content-Type' => 'application/json'],
        ]);

        // Assert
        $this->assertResponseIsSuccessful();
        
        $responseData = $response->toArray();
        $this->assertSame(32.0, $responseData['price']); // 40 * 0.8 = 32
    }

    public function testCreateBookWithInvalidData(): void
    {
        // Arrange
        $invalidData = [
            'name' => '', // Nom vide
            'description' => 'Valid description',
            'author' => 'Valid Author',
            'content' => 'Too short', // Contenu trop court
            'price' => -100, // Prix négatif
        ];

        // Act
        $response = static::createClient()->request('POST', '/api/books', [
            'json' => $invalidData,
            'headers' => ['Content-Type' => 'application/json'],
        ]);

        // Assert
        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        
        $responseData = $response->toArray(false);
        $this->assertArrayHasKey('violations', $responseData);
    }

    public function testGetNonExistentBook(): void
    {
        // Act
        static::createClient()->request('GET', '/api/books/non-existent-id');

        // Assert
        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }
}
```

### Test de Recherche

```php
<?php

namespace App\Tests\BookStore\Acceptance;

use ApiPlatform\Symfony\Bundle\Test\ApiTestCase;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;

class BookSearchTest extends ApiTestCase
{
    private BookRepositoryInterface $bookRepository;

    protected function setUp(): void
    {
        $this->bookRepository = self::getContainer()->get(BookRepositoryInterface::class);
        
        if (method_exists($this->bookRepository, 'clear')) {
            $this->bookRepository->clear();
        }
    }

    public function testSearchBooksByName(): void
    {
        // Arrange
        $cleanCodeBook = DummyBookFactory::createBook(
            name: 'Clean Code',
            author: 'Robert C. Martin'
        );
        $cleanArchBook = DummyBookFactory::createBook(
            name: 'Clean Architecture',
            author: 'Robert C. Martin'
        );
        $refactoringBook = DummyBookFactory::createBook(
            name: 'Refactoring',
            author: 'Martin Fowler'
        );

        $this->bookRepository->addMany($cleanCodeBook, $cleanArchBook, $refactoringBook);

        // Act - Rechercher les livres contenant "Clean"
        $response = static::createClient()->request('GET', '/api/books', [
            'query' => ['search' => 'Clean'],
        ]);

        // Assert
        $this->assertResponseIsSuccessful();
        
        $responseData = $response->toArray();
        $books = $responseData['hydra:member'];
        
        $this->assertCount(2, $books);
        $bookNames = array_column($books, 'name');
        $this->assertContains('Clean Code', $bookNames);
        $this->assertContains('Clean Architecture', $bookNames);
        $this->assertNotContains('Refactoring', $bookNames);
    }

    public function testGetCheapestBooks(): void
    {
        // Arrange
        $expensiveBook = DummyBookFactory::createBook(name: 'Expensive', price: 5000);
        $cheapBook = DummyBookFactory::createBook(name: 'Cheap', price: 1000);
        $mediumBook = DummyBookFactory::createBook(name: 'Medium', price: 3000);

        $this->bookRepository->addMany($expensiveBook, $cheapBook, $mediumBook);

        // Act
        $response = static::createClient()->request('GET', '/api/books/cheapest', [
            'query' => ['limit' => 2],
        ]);

        // Assert
        $this->assertResponseIsSuccessful();
        
        $responseData = $response->toArray();
        $this->assertCount(2, $responseData);
        $this->assertSame('Cheap', $responseData[0]['name']);
        $this->assertSame('Medium', $responseData[1]['name']);
    }
}
```

## 🗃️ Gestion des Données de Test

### Fixtures avec Alice

```yaml
# fixtures/books.yaml
App\BookStore\Domain\Model\Book:
    book_clean_code:
        name: '<bookName()>'
        description: '<sentence(10)>'
        author: '<name()>'
        content: '<text(500)>'
        price: '<numberBetween(1000, 5000)>'
        
    book_{1..10}:
        name: '<bookTitle()>'
        description: '<sentence(15)>'
        author: '<name()>'
        content: '<text(800)>'
        price: '<numberBetween(500, 8000)>'
```

### Trait pour Nettoyer les Tests

```php
<?php

namespace App\Tests\Shared;

use Doctrine\DBAL\Connection;

trait DatabaseTestTrait
{
    protected function clearDatabase(): void
    {
        $connection = self::getContainer()->get(Connection::class);
        
        $tables = ['book', 'subscription']; // Liste des tables à nettoyer
        
        $connection->executeStatement('SET FOREIGN_KEY_CHECKS = 0');
        foreach ($tables as $table) {
            $connection->executeStatement("TRUNCATE TABLE $table");
        }
        $connection->executeStatement('SET FOREIGN_KEY_CHECKS = 1');
    }
}
```

## 🎨 Bonnes Pratiques

### ✅ À Faire
- **Isoler** chaque test (setUp/tearDown)
- **Nettoyer** les données entre les tests
- **Tester les cas d'erreur** (400, 404, 500)
- **Vérifier les headers** HTTP
- **Utiliser des factories** pour les données
- **Grouper** les tests par fonctionnalité

### ❌ À Éviter
- Tests dépendants entre eux
- Données partagées entre tests
- Tests trop longs ou complexes
- Assertions sur l'implémentation
- Mocks dans les tests d'acceptance
- Tests flaky (instables)

## 🚀 Prochaines Étapes

Maintenant que vous maîtrisez les tests d'intégration :
1. Configuration avancée de Symfony
2. Injection de dépendances et services
3. Optimisation des performances
4. Mise en production

## 💡 Points Clés à Retenir

- Les **tests fonctionnels** testent les use cases complets
- Les **tests d'acceptance** testent l'API HTTP
- **Isolez** chaque test avec des données propres
- **Utilisez** les bons outils (KernelTestCase, ApiTestCase)
- **Testez** les cas d'erreur autant que les cas de succès
- **Maintenez** un équilibre dans la pyramide des tests

---

**Exercice pratique** : Créez un test d'acceptance complet pour un workflow de commande : créer des livres, les ajouter à une commande, confirmer la commande, puis vérifier le statut via l'API.
