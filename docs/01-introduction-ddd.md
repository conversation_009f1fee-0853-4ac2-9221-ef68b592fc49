# 1. Introduction au Domain-Driven Design (DDD)

## 🎯 Objectifs d'apprentissage

À la fin de ce chapitre, vous serez capable de :
- Comprendre les concepts fondamentaux du DDD
- Identifier les avantages de l'architecture hexagonale
- Reconnaître les différentes couches d'une application DDD
- Appliquer les principes de séparation des responsabilités

## 📚 Qu'est-ce que le Domain-Driven Design ?

Le **Domain-Driven Design** est une approche de développement logiciel qui place le **domaine métier** au centre de la conception. C<PERSON><PERSON> par <PERSON>, le DDD propose une méthodologie pour créer des applications complexes en se concentrant sur :

1. **Le langage ubiquitaire** : Un vocabulaire commun entre développeurs et experts métier
2. **La modélisation du domaine** : Représentation fidèle des règles business
3. **La séparation des responsabilités** : Isolation du code métier des détails techniques

## 🏗️ Architecture Hexagonale (Ports & Adapters)

L'architecture hexagonale, aussi appelée **Ports & Adapters**, organise le code en couches concentriques :

```
┌─────────────────────────────────────┐
│           Infrastructure            │  ← Adapters (DB, API, UI)
├─────────────────────────────────────┤
│            Application              │  ← Use Cases, Commands, Queries
├─────────────────────────────────────┤
│              Domain                 │  ← Entités, Value Objects, Rules
└─────────────────────────────────────┘
```

### Avantages de cette architecture :

✅ **Testabilité** : Le domaine peut être testé sans dépendances externes
✅ **Flexibilité** : Changement facile d'infrastructure (DB, Framework)
✅ **Maintenabilité** : Code métier isolé et protégé
✅ **Évolutivité** : Ajout de nouvelles fonctionnalités sans impact sur l'existant

## 🎯 Les Couches en Détail

### 1. Domain (Cœur Métier)
- **Entités** : Objets avec identité et cycle de vie
- **Value Objects** : Objets immuables sans identité
- **Agrégats** : Groupes d'entités avec invariants
- **Services de domaine** : Logique métier complexe
- **Repositories (interfaces)** : Contrats de persistance

### 2. Application (Cas d'Usage)
- **Commands** : Actions qui modifient l'état
- **Queries** : Lectures de données
- **Handlers** : Orchestration des cas d'usage
- **DTOs** : Objets de transfert de données

### 3. Infrastructure (Détails Techniques)
- **Repositories (implémentations)** : Accès aux données
- **Controllers** : Points d'entrée HTTP
- **Adapters** : Intégrations externes
- **Configuration** : Services et dépendances

## 🔄 Flux de Données Typique

```
1. Controller (Infrastructure)
   ↓
2. Command/Query (Application)
   ↓
3. Handler (Application)
   ↓
4. Domain Service (Domain)
   ↓
5. Repository Interface (Domain)
   ↓
6. Repository Implementation (Infrastructure)
```

## 📝 Exemple Concret : BookStore

Dans votre projet, observez cette structure :

```
src/
├── BookStore/                    ← Bounded Context
│   ├── Domain/                   ← Couche Domain
│   │   ├── Model/Book.php        ← Entité
│   │   ├── ValueObject/          ← Value Objects
│   │   ├── Repository/           ← Interfaces
│   │   └── Exception/            ← Exceptions métier
│   ├── Application/              ← Couche Application
│   │   ├── Command/              ← Commands & Handlers
│   │   └── Query/                ← Queries & Handlers
│   └── Infrastructure/           ← Couche Infrastructure
│       ├── Doctrine/             ← Persistance
│       └── ApiPlatform/          ← API REST
```

## 🎨 Bounded Context

Un **Bounded Context** est une frontière logique qui délimite un modèle de domaine. Dans votre projet :

- **BookStore** : Gestion des livres et de leur catalogue
- **Subscription** : Gestion des abonnements
- **Shared** : Éléments partagés entre contextes

## 🔑 Principes Fondamentaux

### 1. Inversion de Dépendance
```php
// ❌ Mauvais : Dépendance directe
class BookService {
    private DoctrineBookRepository $repository;
}

// ✅ Bon : Dépendance vers l'interface
class BookService {
    private BookRepositoryInterface $repository;
}
```

### 2. Séparation des Responsabilités
- **Domain** : QU'EST-CE QUE fait l'application
- **Application** : COMMENT orchestrer les actions
- **Infrastructure** : OÙ et AVEC QUOI implémenter

### 3. Langage Ubiquitaire
Utilisez le même vocabulaire dans le code et les discussions métier :
- `Book` (pas `BookEntity`)
- `applyDiscount()` (pas `setPrice()`)
- `Author` (pas `string $author`)

## 🚀 Prochaines Étapes

Dans les prochains chapitres, nous verrons :
1. Comment structurer un projet DDD
2. Créer des Value Objects robustes
3. Modéliser des Entités et Agrégats
4. Implémenter le pattern Repository
5. Mettre en place CQRS
6. Écrire des tests efficaces

## 💡 Points Clés à Retenir

- Le DDD place le **métier au centre** de la conception
- L'architecture hexagonale **protège le domaine** des détails techniques
- Chaque couche a une **responsabilité claire** et définie
- Les **interfaces** permettent l'inversion de dépendance
- Le **langage ubiquitaire** facilite la communication

---

**Exercice de réflexion** : Identifiez dans votre domaine métier actuel quelles seraient les entités principales, les value objects et les règles business à modéliser.
