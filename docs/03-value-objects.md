# 3. Value Objects - Les Briques de Base du Domain

## 🎯 Objectifs d'apprentissage

À la fin de ce chapitre, vous serez capable de :
- Comprendre ce qu'est un Value Object et ses caractéristiques
- Créer des Value Objects robustes avec validation
- Implémenter l'immutabilité et l'égalité par valeur
- Utiliser les Value Objects dans vos entités
- Configurer Doctrine pour persister les Value Objects

## 📚 Qu'est-ce qu'un Value Object ?

Un **Value Object** est un objet qui :
- ✅ **N'a pas d'identité** : défini uniquement par ses attributs
- ✅ **Est immuable** : ne peut pas être modifié après création
- ✅ **Égalité par valeur** : deux instances avec les mêmes valeurs sont égales
- ✅ **Encapsule la validation** : garantit la cohérence des données
- ✅ **Exprime le métier** : porte la sémantique du domaine

## 🔍 Exemples de Value Objects

Dans votre projet BookStore, observez ces Value Objects :

### BookName - Nom d'un livre
```php
<?php

namespace App\BookStore\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
final readonly class BookName
{
    #[ORM\Column(name: 'name', type: 'string', length: 255)]
    public string $value;

    public function __construct(string $value)
    {
        $this->validate($value);
        $this->value = $value;
    }

    private function validate(string $value): void
    {
        if (empty(trim($value))) {
            throw new \InvalidArgumentException('Book name cannot be empty');
        }

        if (strlen($value) > 255) {
            throw new \InvalidArgumentException('Book name cannot exceed 255 characters');
        }
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

### Price - Prix avec devise
```php
<?php

namespace App\BookStore\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
final readonly class Price
{
    #[ORM\Column(name: 'price_amount', type: 'integer')]
    public int $amount; // En centimes pour éviter les problèmes de float

    #[ORM\Column(name: 'price_currency', type: 'string', length: 3)]
    public string $currency;

    public function __construct(int $amount, string $currency = 'EUR')
    {
        $this->validate($amount, $currency);
        $this->amount = $amount;
        $this->currency = $currency;
    }

    private function validate(int $amount, string $currency): void
    {
        if ($amount < 0) {
            throw new \InvalidArgumentException('Price cannot be negative');
        }

        if (!in_array($currency, ['EUR', 'USD', 'GBP'], true)) {
            throw new \InvalidArgumentException('Unsupported currency');
        }
    }

    public function toFloat(): float
    {
        return $this->amount / 100;
    }

    public function applyDiscount(Discount $discount): self
    {
        $discountedAmount = (int) ($this->amount * (1 - $discount->percentage / 100));
        return new self($discountedAmount, $this->currency);
    }

    public function equals(Price $other): bool
    {
        return $this->amount === $other->amount 
            && $this->currency === $other->currency;
    }

    public function __toString(): string
    {
        return sprintf('%.2f %s', $this->toFloat(), $this->currency);
    }
}
```

### Email - Adresse email validée
```php
<?php

namespace App\Shared\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
final readonly class Email
{
    #[ORM\Column(name: 'email', type: 'string', length: 320)]
    public string $value;

    public function __construct(string $value)
    {
        $this->validate($value);
        $this->value = strtolower(trim($value));
    }

    private function validate(string $value): void
    {
        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Invalid email format');
        }

        if (strlen($value) > 320) {
            throw new \InvalidArgumentException('Email too long');
        }
    }

    public function getDomain(): string
    {
        return substr($this->value, strpos($this->value, '@') + 1);
    }

    public function getLocalPart(): string
    {
        return substr($this->value, 0, strpos($this->value, '@'));
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

## 🆔 Value Objects pour les Identifiants

### Trait AggregateRootId
```php
<?php

namespace App\Shared\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\AbstractUid;
use Symfony\Component\Uid\Uuid;

trait AggregateRootId
{
    #[ORM\Id]
    #[ORM\Column(name: 'id', type: 'uuid')]
    public readonly AbstractUid $value;

    final public function __construct(?AbstractUid $value = null)
    {
        $this->value = $value ?? Uuid::v4();
    }

    public function equals(self $other): bool
    {
        return $this->value->equals($other->value);
    }

    public function __toString(): string
    {
        return (string) $this->value;
    }
}
```

### BookId - Identifiant spécifique
```php
<?php

namespace App\BookStore\Domain\ValueObject;

use App\Shared\Domain\ValueObject\AggregateRootId;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
final readonly class BookId
{
    use AggregateRootId;
}
```

## 🔧 Patterns Avancés

### Value Object avec Méthodes Métier
```php
<?php

namespace App\BookStore\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
final readonly class Discount
{
    #[ORM\Column(name: 'discount_percentage', type: 'float')]
    public float $percentage;

    public function __construct(float $percentage)
    {
        $this->validate($percentage);
        $this->percentage = $percentage;
    }

    private function validate(float $percentage): void
    {
        if ($percentage < 0 || $percentage > 100) {
            throw new \InvalidArgumentException('Discount must be between 0 and 100%');
        }
    }

    public function isSignificant(): bool
    {
        return $this->percentage >= 10;
    }

    public function combine(self $other): self
    {
        // Combine deux remises : (1 - (1-a)(1-b)) * 100
        $combinedRate = 1 - ((1 - $this->percentage / 100) * (1 - $other->percentage / 100));
        return new self($combinedRate * 100);
    }

    public function __toString(): string
    {
        return sprintf('%.1f%%', $this->percentage);
    }
}
```

### Collection de Value Objects
```php
<?php

namespace App\BookStore\Domain\ValueObject;

final readonly class TagCollection
{
    /** @var Tag[] */
    private array $tags;

    public function __construct(Tag ...$tags)
    {
        $this->tags = array_values(array_unique($tags, SORT_REGULAR));
    }

    public function add(Tag $tag): self
    {
        if ($this->contains($tag)) {
            return $this;
        }

        return new self(...$this->tags, $tag);
    }

    public function remove(Tag $tag): self
    {
        $filtered = array_filter($this->tags, fn(Tag $t) => !$t->equals($tag));
        return new self(...$filtered);
    }

    public function contains(Tag $tag): bool
    {
        foreach ($this->tags as $existingTag) {
            if ($existingTag->equals($tag)) {
                return true;
            }
        }
        return false;
    }

    public function count(): int
    {
        return count($this->tags);
    }

    public function toArray(): array
    {
        return $this->tags;
    }
}
```

## 🗃️ Configuration Doctrine

### Mapping des Value Objects
```php
// Dans votre entité Book
#[ORM\Entity]
class Book
{
    #[ORM\Embedded(columnPrefix: false)]
    private BookName $name;

    #[ORM\Embedded(columnPrefix: false)]
    private Price $price;

    #[ORM\Embedded(columnPrefix: false)]
    private Author $author;

    // ...
}
```

### Types Doctrine Personnalisés
```php
<?php

namespace App\BookStore\Infrastructure\Doctrine\Type;

use App\BookStore\Domain\ValueObject\BookId;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\Type;
use Symfony\Component\Uid\Uuid;

class BookIdType extends Type
{
    public const NAME = 'book_id';

    public function getSQLDeclaration(array $column, AbstractPlatform $platform): string
    {
        return $platform->getGuidTypeDeclarationSQL($column);
    }

    public function convertToPHPValue($value, AbstractPlatform $platform): ?BookId
    {
        return $value ? new BookId(Uuid::fromString($value)) : null;
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        return $value instanceof BookId ? (string) $value->value : null;
    }

    public function getName(): string
    {
        return self::NAME;
    }
}
```

## ✅ Tests des Value Objects

### Test Unitaire Complet
```php
<?php

namespace App\Tests\BookStore\Unit\Domain\ValueObject;

use App\BookStore\Domain\ValueObject\Price;
use App\BookStore\Domain\ValueObject\Discount;
use PHPUnit\Framework\TestCase;

class PriceTest extends TestCase
{
    public function testCreateValidPrice(): void
    {
        $price = new Price(1500, 'EUR'); // 15.00 EUR

        $this->assertSame(1500, $price->amount);
        $this->assertSame('EUR', $price->currency);
        $this->assertSame(15.0, $price->toFloat());
    }

    public function testRejectNegativeAmount(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Price cannot be negative');

        new Price(-100, 'EUR');
    }

    public function testRejectInvalidCurrency(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Unsupported currency');

        new Price(1000, 'XYZ');
    }

    public function testApplyDiscount(): void
    {
        $price = new Price(1000, 'EUR'); // 10.00 EUR
        $discount = new Discount(20.0); // 20%

        $discountedPrice = $price->applyDiscount($discount);

        $this->assertSame(800, $discountedPrice->amount); // 8.00 EUR
        $this->assertSame('EUR', $discountedPrice->currency);
    }

    public function testEquality(): void
    {
        $price1 = new Price(1500, 'EUR');
        $price2 = new Price(1500, 'EUR');
        $price3 = new Price(1500, 'USD');

        $this->assertTrue($price1->equals($price2));
        $this->assertFalse($price1->equals($price3));
    }

    public function testImmutability(): void
    {
        $originalPrice = new Price(1000, 'EUR');
        $discount = new Discount(10.0);

        $discountedPrice = $originalPrice->applyDiscount($discount);

        // L'objet original n'est pas modifié
        $this->assertSame(1000, $originalPrice->amount);
        $this->assertSame(900, $discountedPrice->amount);
        $this->assertNotSame($originalPrice, $discountedPrice);
    }
}
```

## 🎨 Bonnes Pratiques

### ✅ À Faire
- Valider dans le constructeur
- Rendre les propriétés `readonly`
- Implémenter `__toString()`
- Créer des méthodes métier expressives
- Tester tous les cas limites

### ❌ À Éviter
- Setters ou méthodes de modification
- Logique complexe dans les Value Objects
- Dépendances vers des services externes
- Validation dans les getters

## 🚀 Prochaines Étapes

Maintenant que vous maîtrisez les Value Objects, nous verrons :
1. Comment créer des Entités robustes
2. Modéliser des Agrégats avec invariants
3. Implémenter les Repositories
4. Orchestrer avec CQRS

## 💡 Points Clés à Retenir

- Les **Value Objects** encapsulent et valident les données primitives
- Ils sont **immuables** et définis par leurs valeurs
- Ils portent la **sémantique métier** et les règles de validation
- Doctrine peut les **embarquer** dans les entités
- Ils sont **faciles à tester** unitairement

---

**Exercice pratique** : Créez un Value Object `ISBN` pour représenter le numéro ISBN d'un livre avec validation du format et calcul de la clé de contrôle.
