# 🛠️ Customisation de Symfony Maker pour l'Architecture DDD

Ce guide explique comment customiser Symfony Maker pour générer automatiquement les entités et repositories dans la structure DDD de votre projet.

## 📁 Structure Cible

```
src/
├── BookStore/                    ← Bounded Context
│   ├── Domain/
│   │   ├── Model/               ← Entités générées ici
│   │   │   └── Book.php
│   │   ├── Repository/          ← Interfaces de repository
│   │   │   └── BookRepositoryInterface.php
│   │   └── ValueObject/
│   └── Infrastructure/
│       └── Doctrine/            ← Implémentations Doctrine
│           └── DoctrineBookRepository.php
```

## 🎯 Solution 1: Configuration Maker Bundle

### Étape 1: Vérifier Symfony Maker Bundle

Le Maker Bundle devrait déjà être installé. Vérifiez avec :

```bash
composer show symfony/maker-bundle
```

Si pas installé :
```bash
composer require --dev symfony/maker-bundle
```

### Étape 2: Créer la configuration Maker

Créez le fichier `config/packages/dev/maker.php` :

```php
<?php

declare(strict_types=1);

use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $containerConfigurator->extension('maker', [
        'root_namespace' => 'App',
        'generate_final_classes' => true,
        'generate_final_entities' => true,
    ]);
};
```

### Étape 3: Créer un Maker personnalisé

Créez `src/Shared/Infrastructure/Maker/MakeDDDEntity.php` :

```php
<?php

declare(strict_types=1);

namespace App\Shared\Infrastructure\Maker;

use Symfony\Bundle\MakerBundle\ConsoleStyle;
use Symfony\Bundle\MakerBundle\DependencyBuilder;
use Symfony\Bundle\MakerBundle\Generator;
use Symfony\Bundle\MakerBundle\InputConfiguration;
use Symfony\Bundle\MakerBundle\Maker\AbstractMaker;
use Symfony\Bundle\MakerBundle\Str;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;

final class MakeDDDEntity extends AbstractMaker
{
    public static function getCommandName(): string
    {
        return 'make:ddd:entity';
    }

    public static function getCommandDescription(): string
    {
        return 'Creates a new DDD entity with repository interface and implementation';
    }

    public function configureCommand(Command $command, InputConfiguration $inputConfig): void
    {
        $command
            ->addArgument('bounded-context', InputArgument::REQUIRED, 'The bounded context (e.g. BookStore)')
            ->addArgument('entity-name', InputArgument::REQUIRED, 'The entity name (e.g. Book)')
            ->addOption('with-repository', null, InputOption::VALUE_NONE, 'Generate repository interface and implementation')
            ->setHelp('This command creates a new DDD entity in the correct directory structure.');
    }

    public function generate(InputInterface $input, ConsoleStyle $io, Generator $generator): void
    {
        $boundedContext = $input->getArgument('bounded-context');
        $entityName = $input->getArgument('entity-name');
        $withRepository = $input->getOption('with-repository');

        // Générer l'entité
        $this->generateEntity($generator, $boundedContext, $entityName);

        if ($withRepository) {
            // Générer l'interface du repository
            $this->generateRepositoryInterface($generator, $boundedContext, $entityName);
            
            // Générer l'implémentation Doctrine
            $this->generateDoctrineRepository($generator, $boundedContext, $entityName);
        }

        $generator->writeChanges();

        $io->success(sprintf('DDD Entity "%s" created successfully in %s context!', $entityName, $boundedContext));
        
        if ($withRepository) {
            $io->note([
                'Repository interface and Doctrine implementation created.',
                'Don\'t forget to configure the repository binding in config/services/' . strtolower($boundedContext) . '.php'
            ]);
        }
    }

    public function configureDependencies(DependencyBuilder $dependencies): void
    {
        $dependencies->addClassDependency('Doctrine\\ORM\\Mapping\\Entity', 'orm');
    }

    private function generateEntity(Generator $generator, string $boundedContext, string $entityName): void
    {
        $entityPath = sprintf('src/%s/Domain/Model/%s.php', $boundedContext, $entityName);
        
        $generator->generateClass(
            $entityPath,
            'maker/ddd/Entity.tpl.php',
            [
                'bounded_context' => $boundedContext,
                'entity_name' => $entityName,
                'entity_id_name' => $entityName . 'Id',
            ]
        );
    }

    private function generateRepositoryInterface(Generator $generator, string $boundedContext, string $entityName): void
    {
        $repositoryPath = sprintf('src/%s/Domain/Repository/%sRepositoryInterface.php', $boundedContext, $entityName);
        
        $generator->generateClass(
            $repositoryPath,
            'maker/ddd/RepositoryInterface.tpl.php',
            [
                'bounded_context' => $boundedContext,
                'entity_name' => $entityName,
                'entity_id_name' => $entityName . 'Id',
            ]
        );
    }

    private function generateDoctrineRepository(Generator $generator, string $boundedContext, string $entityName): void
    {
        $repositoryPath = sprintf('src/%s/Infrastructure/Doctrine/Doctrine%sRepository.php', $boundedContext, $entityName);
        
        $generator->generateClass(
            $repositoryPath,
            'maker/ddd/DoctrineRepository.tpl.php',
            [
                'bounded_context' => $boundedContext,
                'entity_name' => $entityName,
                'entity_id_name' => $entityName . 'Id',
                'repository_alias' => strtolower($entityName),
            ]
        );
    }
}
```

## 🎨 Templates Maker

### Template Entity

Créez `templates/maker/ddd/Entity.tpl.php` :

```php
<?= "<?php\n" ?>

declare(strict_types=1);

namespace App\<?= $bounded_context ?>\Domain\Model;

use App\<?= $bounded_context ?>\Domain\ValueObject\<?= $entity_id_name ?>;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
final class <?= $entity_name ?>

{
    #[ORM\Embedded(columnPrefix: false)]
    private readonly <?= $entity_id_name ?> $id;

    public function __construct(
        // Ajoutez vos Value Objects ici
    ) {
        $this->id = new <?= $entity_id_name ?>();
    }

    public function id(): <?= $entity_id_name ?>

    {
        return $this->id;
    }

    // Ajoutez vos méthodes métier ici
}
```

### Template Repository Interface

Créez `templates/maker/ddd/RepositoryInterface.tpl.php` :

```php
<?= "<?php\n" ?>

declare(strict_types=1);

namespace App\<?= $bounded_context ?>\Domain\Repository;

use App\<?= $bounded_context ?>\Domain\Model\<?= $entity_name ?>;
use App\<?= $bounded_context ?>\Domain\ValueObject\<?= $entity_id_name ?>;

interface <?= $entity_name ?>RepositoryInterface
{
    public function add(<?= $entity_name ?> $<?= $repository_alias ?>): void;

    public function remove(<?= $entity_name ?> $<?= $repository_alias ?>): void;

    public function ofId(<?= $entity_id_name ?> $id): ?<?= $entity_name ?>;

    /**
     * @return <?= $entity_name ?>[]
     */
    public function findAll(): array;
}
```

### Template Doctrine Repository

Créez `templates/maker/ddd/DoctrineRepository.tpl.php` :

```php
<?= "<?php\n" ?>

declare(strict_types=1);

namespace App\<?= $bounded_context ?>\Infrastructure\Doctrine;

use App\<?= $bounded_context ?>\Domain\Model\<?= $entity_name ?>;
use App\<?= $bounded_context ?>\Domain\Repository\<?= $entity_name ?>RepositoryInterface;
use App\<?= $bounded_context ?>\Domain\ValueObject\<?= $entity_id_name ?>;
use App\Shared\Infrastructure\Doctrine\DoctrineRepository;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @extends DoctrineRepository<<?= $entity_name ?>>
 */
final class Doctrine<?= $entity_name ?>Repository extends DoctrineRepository implements <?= $entity_name ?>RepositoryInterface
{
    private const ENTITY_CLASS = <?= $entity_name ?>::class;
    private const ALIAS = '<?= $repository_alias ?>';

    public function __construct(EntityManagerInterface $em)
    {
        parent::__construct($em, self::ENTITY_CLASS, self::ALIAS);
    }

    public function add(<?= $entity_name ?> $<?= $repository_alias ?>): void
    {
        $this->em->persist($<?= $repository_alias ?>);
    }

    public function remove(<?= $entity_name ?> $<?= $repository_alias ?>): void
    {
        $this->em->remove($<?= $repository_alias ?>);
    }

    public function ofId(<?= $entity_id_name ?> $id): ?<?= $entity_name ?>

    {
        return $this->em->find(self::ENTITY_CLASS, $id->value);
    }

    /**
     * @return <?= $entity_name ?>[]
     */
    public function findAll(): array
    {
        return $this->em->getRepository(self::ENTITY_CLASS)->findAll();
    }
}
```

## 🚀 Utilisation

### Générer une entité avec repository

```bash
php bin/console make:ddd:entity BookStore Product --with-repository
```

Cette commande génère :
- `src/BookStore/Domain/Model/Product.php`
- `src/BookStore/Domain/Repository/ProductRepositoryInterface.php`
- `src/BookStore/Infrastructure/Doctrine/DoctrineProductRepository.php`

### Générer seulement une entité

```bash
php bin/console make:ddd:entity BookStore Category
```

## ⚙️ Configuration des Services

Après génération, ajoutez dans `config/services/book_store.php` :

```php
// repositories
$services->set(ProductRepositoryInterface::class)
    ->class(DoctrineProductRepository::class);
```

## 📝 Prochaines Étapes

1. Créer le Value Object `ProductId`
2. Ajouter les autres Value Objects nécessaires
3. Implémenter les méthodes métier
4. Écrire les tests unitaires
5. Configurer les mappings Doctrine si nécessaire
