<?= "<?php\n" ?>

declare(strict_types=1);

namespace App\<?= $bounded_context ?>\Domain\Repository;

use App\<?= $bounded_context ?>\Domain\Model\<?= $entity_name ?>;
use App\<?= $bounded_context ?>\Domain\ValueObject\<?= $entity_id_name ?>;

interface <?= $entity_name ?>RepositoryInterface
{
    public function add(<?= $entity_name ?> $<?= $repository_alias ?>): void;

    public function remove(<?= $entity_name ?> $<?= $repository_alias ?>): void;

    public function ofId(<?= $entity_id_name ?> $id): ?<?= $entity_name ?>;

    /**
     * @return <?= $entity_name ?>[]
     */
    public function findAll(): array;
}
