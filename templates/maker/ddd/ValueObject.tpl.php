<?= "<?php\n" ?>

declare(strict_types=1);

namespace App\<?= $bounded_context ?>\Domain\ValueObject;

<?php if ($embeddable): ?>
use Doctrine\ORM\Mapping as ORM;
<?php endif; ?>
use Webmozart\Assert\Assert;

<?php if ($embeddable): ?>
#[ORM\Embeddable]
<?php endif; ?>
final class <?= $value_object_name ?>

{
<?php if ($embeddable): ?>
    #[ORM\Column(name: '<?= $column_name ?>', type: '<?= $type === 'int' ? 'integer' : $type ?>'<?php if ($type === 'string'): ?>, length: 255<?php endif; ?>)]
<?php endif; ?>
    public readonly <?= $type ?> $value;

    public function __construct(<?= $type ?> $value)
    {
<?php if ($type === 'string'): ?>
        Assert::lengthBetween($value, 1, 255);
<?php elseif ($type === 'int'): ?>
        Assert::greaterThanEq($value, 0);
<?php elseif ($type === 'float'): ?>
        Assert::greaterThanEq($value, 0.0);
<?php endif; ?>

        $this->value = $value;
    }

    public function isEqualTo(self $other): bool
    {
        return $other->value === $this->value;
    }

<?php if ($type === 'string'): ?>
    public function __toString(): string
    {
        return $this->value;
    }
<?php endif; ?>
}
