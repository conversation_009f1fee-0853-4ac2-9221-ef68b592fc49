<?= "<?php\n" ?>

declare(strict_types=1);

namespace App\<?= $bounded_context ?>\Infrastructure\Doctrine;

use App\<?= $bounded_context ?>\Domain\Model\<?= $entity_name ?>;
use App\<?= $bounded_context ?>\Domain\Repository\<?= $entity_name ?>RepositoryInterface;
use App\<?= $bounded_context ?>\Domain\ValueObject\<?= $entity_id_name ?>;
use App\Shared\Infrastructure\Doctrine\DoctrineRepository;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @extends DoctrineRepository<<?= $entity_name ?>>
 */
final class Doctrine<?= $entity_name ?>Repository extends DoctrineRepository implements <?= $entity_name ?>RepositoryInterface
{
    private const ENTITY_CLASS = <?= $entity_name ?>::class;
    private const ALIAS = '<?= $repository_alias ?>';

    public function __construct(EntityManagerInterface $em)
    {
        parent::__construct($em, self::ENTITY_CLASS, self::ALIAS);
    }

    public function add(<?= $entity_name ?> $<?= $repository_alias ?>): void
    {
        $this->em->persist($<?= $repository_alias ?>);
    }

    public function remove(<?= $entity_name ?> $<?= $repository_alias ?>): void
    {
        $this->em->remove($<?= $repository_alias ?>);
    }

    public function ofId(<?= $entity_id_name ?> $id): ?<?= $entity_name ?>

    {
        return $this->em->find(self::ENTITY_CLASS, $id->value);
    }

    /**
     * @return <?= $entity_name ?>[]
     */
    public function findAll(): array
    {
        return $this->em->getRepository(self::ENTITY_CLASS)->findAll();
    }
}
