<?= "<?php\n" ?>

declare(strict_types=1);

namespace App\<?= $bounded_context ?>\Domain\Model;

use App\<?= $bounded_context ?>\Domain\ValueObject\<?= $entity_id_name ?>;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
final class <?= $entity_name ?>

{
    #[ORM\Embedded(columnPrefix: false)]
    private readonly <?= $entity_id_name ?> $id;

    public function __construct(
        // Ajoutez vos Value Objects ici
    ) {
        $this->id = new <?= $entity_id_name ?>();
    }

    public function id(): <?= $entity_id_name ?>

    {
        return $this->id;
    }

    // Ajoutez vos méthodes métier ici
}
