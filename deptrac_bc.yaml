parameters:
    paths:
        - ./src

    layers:
        - name: BookStore
          collectors:
              - type: directory
                regex: src/BookStore/.*

        - name: Subscription
          collectors:
              - type: directory
                regex: src/Subscription/.*

        - name: Shared
          collectors:
              - type: directory
                regex: src/Shared/.*

        - name: Vendors
          collectors:
              - { type: className, regex: ^ApiPlatform\\ }
              - { type: className, regex: ^Symfony\\ }
              - { type: className, regex: ^Doctrine\\ }
              - { type: className, regex: ^Webmozart\\ }

    ruleset:
        BookStore: [ Shared, Vendors ]
        Subscription: [ Shared, Vendors ]
        Shared: [ Vendors ]
