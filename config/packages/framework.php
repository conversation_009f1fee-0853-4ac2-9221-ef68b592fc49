<?php

declare(strict_types=1);

use App\BookStore\Domain\Exception\MissingBookException;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $containerConfigurator->extension(
        'framework',
        [
            'secret' => '%env(APP_SECRET)%',
            'http_method_override' => false,
            'handle_all_throwables' => true,
            'session' => [
                'handler_id' => null,
                'cookie_secure' => 'auto',
                'cookie_samesite' => 'lax',
                'storage_factory_id' => 'session.storage.factory.native',
            ],
            'php_errors' => [
                'log' => 4096,
            ],
            'exceptions' => [
                MissingBookException::class => [
                    'status_code' => 404,
                ],
            ],
        ],
    );
    if ('test' === $containerConfigurator->env()) {
        $containerConfigurator->extension('framework', [
            'test' => true,
            'session' => [
                'storage_factory_id' => 'session.storage.factory.mock_file',
            ],
        ]);
    }
};
