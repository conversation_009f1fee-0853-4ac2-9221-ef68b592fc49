{"api-platform/core": {"version": "3.1", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "3.0", "ref": "d9417a435d16dd91d7eb1eba547a9cb214cfccfe"}, "files": ["config/routes/api_platform.php", "src/ApiResource/.gitignore"]}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.9", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "2.8", "ref": "551a7bc8998418b83a2c69090782d1eb90c7fa1a"}, "files": ["config/packages/doctrine.php", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "friendsofphp/php-cs-fixer": {"version": "3.16", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "nelmio/cors-bundle": {"version": "2.3", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "1.5", "ref": "f818ca1a144e57496762ae837d9929543f042751"}, "files": ["config/packages/nelmio_cors.php"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "9.3", "ref": "a6249a6c4392e9169b87abf93225f7f9f59025e6"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "qossmic/deptrac-shim": {"version": "1.0", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php-contrib", "branch": "main", "version": "0.19", "ref": "3c3daab46226c37fc14e10faf81df737a3039d96"}}, "symfony/console": {"version": "6.2", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["bin/console"]}, "symfony/debug-bundle": {"version": "6.2", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "5.3", "ref": "31687d470bd812209d9da6b673ed2aba38521547"}, "files": ["config/packages/debug.php"]}, "symfony/flex": {"version": "2.2", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "6.2", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "6.2", "ref": "7106e360bba0b323cfdad1ee63533f75229bdddd"}, "files": ["config/packages/cache.php", "config/packages/framework.php", "config/preload.php", "config/routes/framework.php", "config/services.php", "public/index.php", "src/Controller/.gitignore", "src/Shared/Infrastructure/Symfony/Kernel.php"]}, "symfony/maker-bundle": {"version": "1.62", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "6.2", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "6.0", "ref": "495a41e4ea82968732087767c3cacf1cacd7eee8"}, "files": ["config/packages/messenger.php"]}, "symfony/monolog-bundle": {"version": "3.8", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "3.7", "ref": "96b724d9eee133f18d4845f90948c5d864cfb286"}, "files": ["config/packages/monolog.php"]}, "symfony/phpunit-bridge": {"version": "6.2", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "5.3", "ref": "819d3d2ffa4590eba0b8f4f3e5e89415ee4e45c3"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/routing": {"version": "6.2", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "6.2", "ref": "14f3dfdb1a4de4c60baeedbf320af6959b66cf65"}, "files": ["config/packages/routing.php", "config/routes.php"]}, "symfony/security-bundle": {"version": "6.2", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "6.0", "ref": "a495b223821bb3a49947b1a041720173f5a1dac6"}, "files": ["config/packages/security.php"]}, "symfony/twig-bundle": {"version": "6.2", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "5.4", "ref": "44d440f374e69400bef3e98f529851b43dfe6685"}, "files": ["config/packages/twig.php", "templates/base.html.twig"]}, "symfony/uid": {"version": "6.2", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "6.2", "ref": "3a539fccfafb8b4bc5d0be04f008b77bade77f91"}, "files": ["config/packages/uid.php"]}, "symfony/validator": {"version": "6.2", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "5.3", "ref": "f05ddc36fac6381e494125732d6822b5af2051b3"}, "files": ["config/packages/validator.php"]}, "symfony/web-profiler-bundle": {"version": "6.2", "recipe": {"repo": "github.com/schranz-php-recipes/symfony-recipes-php", "branch": "main", "version": "6.1", "ref": "65c16a9b40070dc15c389b4e54458ede4e7e5c29"}, "files": ["config/packages/web_profiler.php", "config/routes/web_profiler.php"]}}