name: CI

on:
    pull_request: ~

jobs:
    php-cs-fixer:
        runs-on: ubuntu-latest
        name: PHP-CS-Fixer
        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: '8.2'
                  tools: php-cs-fixer, cs2pr

            - name: Run PHP-CS-Fixer
              run: php-cs-fixer fix --dry-run --format checkstyle | cs2pr

    psalm:
        runs-on: ubuntu-latest
        name: Psalm
        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: '8.2'

            - name: Get composer cache directory
              id: composercache
              run: echo "dir=$(composer config cache-files-dir)" >> "$GITHUB_OUTPUT"

            - name: Cache dependencies
              uses: actions/cache@v4
              with:
                  path: ${{ steps.composercache.outputs.dir }}
                  key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.json') }}
                  restore-keys: ${{ runner.os }}-composer-

            - name: Install dependencies
              run: composer install --prefer-dist

            - name: Run Psalm
              run: vendor/bin/psalm --show-info=true --output-format=github

    deptrac_bc:
        runs-on: ubuntu-latest
        name: Deptrac bounded contexts
        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: '8.2'

            - name: Get composer cache directory
              id: composercache
              run: echo "dir=$(composer config cache-files-dir)" >> "$GITHUB_OUTPUT"

            - name: Cache dependencies
              uses: actions/cache@v4
              with:
                  path: ${{ steps.composercache.outputs.dir }}
                  key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.json') }}
                  restore-keys: ${{ runner.os }}-composer-

            - name: Install dependencies
              run: composer install --prefer-dist

            - name: Run Deptrac
              run: vendor/bin/deptrac analyze --fail-on-uncovered --report-uncovered --no-progress --config-file deptrac_bc.yaml

    deptrac_hexa:
        runs-on: ubuntu-latest
        name: Deptrac hexagonal
        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: '8.2'

            - name: Get composer cache directory
              id: composercache
              run: echo "dir=$(composer config cache-files-dir)" >> "$GITHUB_OUTPUT"

            - name: Cache dependencies
              uses: actions/cache@v4
              with:
                  path: ${{ steps.composercache.outputs.dir }}
                  key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.json') }}
                  restore-keys: ${{ runner.os }}-composer-

            - name: Install dependencies
              run: composer install --prefer-dist

            - name: Run Deptrac
              run: vendor/bin/deptrac analyze --fail-on-uncovered --report-uncovered --no-progress --config-file deptrac_hexa.yaml

    phpunit:
        name: PHPUnit
        runs-on: ubuntu-latest

        services:
            database:
                image: postgres:13-alpine
                env:
                    POSTGRES_USER: symfony
                    POSTGRES_PASSWORD: '!ChangeMe!'
                options: >-
                    --health-cmd pg_isready
                    --health-interval 10s
                    --health-timeout 5s
                    --health-retries 5
                ports:
                    - 5432:5432

        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: '8.2'

            - name: Get composer cache directory
              id: composercache
              run: echo "dir=$(composer config cache-files-dir)" >> "$GITHUB_OUTPUT"

            - name: Cache dependencies
              uses: actions/cache@v4
              with:
                  path: ${{ steps.composercache.outputs.dir }}
                  key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.json') }}
                  restore-keys: ${{ runner.os }}-composer-

            - name: Install dependencies
              run: composer install --prefer-dist

            - name: Run tests
              run: bin/phpunit
              env:
                  DATABASE_URL: 'postgresql://symfony:!ChangeMe!@localhost:5432/app_test?serverVersion=15&charset=utf8'
