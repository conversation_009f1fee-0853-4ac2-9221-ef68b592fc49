# 🏛️ Étape 1 : Architecture et Structure

## 🎯 Objectifs de cette Étape

À la fin de cette étape, vous saurez :
- ✅ Comprendre l'architecture DDD et ses couches
- ✅ Organiser votre projet avec une structure claire
- ✅ Configurer l'environnement de développement
- ✅ Créer les premiers fichiers de base

**Temps estimé** : 45 minutes

## 🏗️ Architecture DDD : Les 3 Couches

### Vue d'Ensemble

```mermaid
graph TB
    subgraph "🌐 Infrastructure Layer"
        API[API Platform]
        DB[Doctrine ORM]
        EXT[Services Externes]
    end
    
    subgraph "⚡ Application Layer"
        CMD[Commands]
        QRY[Queries]
        HDL[Handlers]
    end
    
    subgraph "💎 Domain Layer"
        ENT[Entités]
        VO[Value Objects]
        REPO[Repository Interfaces]
        SRV[Domain Services]
    end
    
    API --> CMD
    API --> QRY
    CMD --> HDL
    QRY --> HDL
    HDL --> ENT
    HDL --> REPO
    DB --> REPO
    
    style Domain fill:#e8f5e8
    style Application fill:#fff3e0
    style Infrastructure fill:#e1f5fe
```

### 💎 Domain Layer (Cœur Métier)
**Responsabilité** : Contient la logique métier pure
- **Entités** : Objets avec identité et cycle de vie
- **Value Objects** : Objets immutables sans identité
- **Repository Interfaces** : Contrats pour la persistance
- **Domain Services** : Logique métier complexe
- **Exceptions** : Erreurs métier spécifiques

### ⚡ Application Layer (Orchestration)
**Responsabilité** : Coordonne les opérations métier
- **Commands** : Actions qui modifient l'état
- **Queries** : Requêtes de lecture
- **Handlers** : Traitent les commands/queries
- **DTOs** : Objets de transfert de données

### 🌐 Infrastructure Layer (Technique)
**Responsabilité** : Implémentations techniques
- **Repositories** : Persistance avec Doctrine
- **API Platform** : Exposition REST
- **Services externes** : Email, paiement, etc.

## 📁 Structure du Projet

### Organisation Recommandée

```
src/
├── BookStore/                    ← Bounded Context
│   ├── Domain/                   ← Couche Domain
│   │   ├── Model/                ← Entités et Agrégats
│   │   │   ├── Book.php
│   │   │   └── BookCollection.php
│   │   ├── ValueObject/          ← Value Objects
│   │   │   ├── BookId.php
│   │   │   ├── BookName.php
│   │   │   ├── Author.php
│   │   │   ├── Price.php
│   │   │   └── Discount.php
│   │   ├── Repository/           ← Interfaces
│   │   │   └── BookRepositoryInterface.php
│   │   ├── Service/              ← Services de domaine
│   │   │   └── BookDiscountService.php
│   │   └── Exception/            ← Exceptions métier
│   │       └── BookNotFoundException.php
│   ├── Application/              ← Couche Application
│   │   ├── Command/              ← Commands & Handlers
│   │   │   ├── CreateBookCommand.php
│   │   │   ├── CreateBookCommandHandler.php
│   │   │   ├── UpdateBookCommand.php
│   │   │   └── UpdateBookCommandHandler.php
│   │   └── Query/                ← Queries & Handlers
│   │       ├── FindBookQuery.php
│   │       ├── FindBookQueryHandler.php
│   │       ├── FindBooksQuery.php
│   │       └── FindBooksQueryHandler.php
│   └── Infrastructure/           ← Couche Infrastructure
│       ├── Doctrine/             ← Persistance
│       │   └── DoctrineBookRepository.php
│       └── ApiPlatform/          ← API REST
│           ├── Resource/
│           │   └── BookResource.php
│           └── State/
│               ├── Processor/
│               │   └── CreateBookProcessor.php
│               └── Provider/
│                   └── BookProvider.php
├── Shared/                       ← Code partagé
│   ├── Domain/
│   │   └── ValueObject/
│   │       └── AggregateRootId.php
│   ├── Application/
│   │   └── Command/
│   │       └── AsCommandHandler.php
│   └── Infrastructure/
│       └── Maker/                ← Commandes de génération
└── Kernel.php
```

## 🚀 Configuration de l'Environnement

### Étape 1 : Créer le Projet

```bash
# Créer un nouveau projet Symfony
symfony new bookstore-api --version="6.4.*" --webapp

cd bookstore-api

# Installer les dépendances DDD
composer require api-platform/core
composer require doctrine/orm
composer require symfony/maker-bundle --dev
composer require phpunit/phpunit --dev
composer require webmozart/assert
```

### Étape 2 : Configuration de Base

**config/packages/doctrine.yaml**
```yaml
doctrine:
    dbal:
        url: '%env(resolve:DATABASE_URL)%'
        driver: 'pdo_mysql'
        charset: utf8mb4
        default_table_options:
            charset: utf8mb4
            collate: utf8mb4_unicode_ci

    orm:
        auto_generate_proxy_classes: true
        enable_lazy_ghost_objects: true
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        auto_mapping: true
        mappings:
            BookStore:
                is_bundle: false
                dir: '%kernel.project_dir%/src/BookStore/Domain/Model'
                prefix: 'App\BookStore\Domain\Model'
                type: attribute
```

**config/packages/api_platform.yaml**
```yaml
api_platform:
    title: 'BookStore API'
    version: '1.0.0'
    description: 'API DDD pour la gestion d\'une librairie'
    
    formats:
        json: ['application/json']
    
    docs_formats:
        jsonapi: ['application/vnd.api+json']
    
    defaults:
        stateless: true
        cache_headers:
            vary: ['Content-Type', 'Authorization', 'Origin']
        extra_properties:
            standard_put: true
            rfc_7807_compliant_errors: true
```

### Étape 3 : Variables d'Environnement

**.env.local**
```bash
# Base de données
DATABASE_URL="mysql://user:password@127.0.0.1:3306/bookstore_api?serverVersion=8.0.32&charset=utf8mb4"

# Environnement de développement
APP_ENV=dev
APP_DEBUG=1
```

## 🎨 Bounded Context : BookStore

### Qu'est-ce qu'un Bounded Context ?

Un **Bounded Context** est une frontière logique qui délimite un modèle de domaine. Dans notre cas, `BookStore` représente tout ce qui concerne la gestion des livres dans une librairie.

### Avantages de cette Organisation

1. **Séparation claire** : Chaque contexte a sa propre logique
2. **Évolutivité** : Facile d'ajouter de nouveaux contextes
3. **Maintenabilité** : Code organisé et prévisible
4. **Tests** : Structure claire pour les tests

## 🛠️ Premiers Fichiers de Base

### Étape 1 : Trait AggregateRootId

```php
<?php
// src/Shared/Domain/ValueObject/AggregateRootId.php

declare(strict_types=1);

namespace App\Shared\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

trait AggregateRootId
{
    #[ORM\Id]
    #[ORM\Column(type: 'string', length: 36)]
    public readonly string $value;

    public function __construct(?string $value = null)
    {
        $this->value = $value ?? Uuid::v4()->toRfc4122();
    }

    public static function generate(): static
    {
        return new static();
    }

    public static function fromString(string $value): static
    {
        return new static($value);
    }

    public function equals(self $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

### Étape 2 : Attribut AsCommandHandler

```php
<?php
// src/Shared/Application/Command/AsCommandHandler.php

declare(strict_types=1);

namespace App\Shared\Application\Command;

#[\Attribute(\Attribute::TARGET_CLASS)]
class AsCommandHandler
{
}
```

### Étape 3 : Configuration des Services

**config/services.yaml**
```yaml
services:
    _defaults:
        autowire: true
        autoconfigure: true

    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # Auto-configuration des Command Handlers
    App\BookStore\Application\Command\:
        resource: '../src/BookStore/Application/Command/*Handler.php'
        tags: ['app.command_handler']

    # Auto-configuration des Query Handlers  
    App\BookStore\Application\Query\:
        resource: '../src/BookStore/Application/Query/*Handler.php'
        tags: ['app.query_handler']
```

## ✅ Validation de l'Étape

Vérifiez que vous avez :

- [ ] **Créé** la structure de dossiers complète
- [ ] **Configuré** Doctrine et API Platform
- [ ] **Installé** toutes les dépendances nécessaires
- [ ] **Créé** les fichiers de base (AggregateRootId, AsCommandHandler)
- [ ] **Configuré** les services Symfony

### Test de Validation

```bash
# Vérifier que Symfony fonctionne
symfony console about

# Vérifier la configuration Doctrine
symfony console doctrine:schema:validate

# Vérifier API Platform
symfony console api:openapi:export
```

## 🎯 Prochaine Étape

Maintenant que l'architecture est en place, nous allons créer notre première entité `Book` et écrire nos premiers tests unitaires dans l'**Étape 2 : Tests Unitaires du Modèle**.

---

**💡 Points Clés à Retenir**
- L'architecture DDD sépare clairement les responsabilités
- La structure de dossiers reflète l'architecture
- Chaque couche a un rôle spécifique et bien défini
- La configuration de base est essentielle pour la suite
