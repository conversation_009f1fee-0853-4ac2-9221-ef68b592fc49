# 🧪 Optionnel : Repositories InMemory pour Tests

## ⚠️ Important : Fonctionnalité Optionnelle

Cette section décrit l'implémentation de repositories en mémoire **uniquement pour les tests**. Ces implémentations ne sont **PAS nécessaires** pour le fonctionnement de base de votre application DDD.

### 🎯 Quand Utiliser les Repositories InMemory

- ✅ **Tests unitaires rapides** sans base de données
- ✅ **Tests d'intégration** avec données contrôlées
- ✅ **Prototypage rapide** sans configuration DB
- ❌ **PAS pour la production** (données perdues au redémarrage)
- ❌ **PAS obligatoire** pour une application DDD fonctionnelle

## 🏗️ Architecture des Repositories InMemory

### Structure Recommandée

```
src/BookStore/Infrastructure/InMemory/
├── InMemoryBookRepository.php        ← Repository en mémoire
└── InMemoryMemberRepository.php      ← (si vous avez des membres)

src/Shared/Infrastructure/InMemory/
├── InMemoryRepository.php            ← Classe de base abstraite
└── InMemoryPaginator.php            ← Pagination en mémoire

tests/BookStore/Integration/InMemory/
└── InMemoryBookRepositoryTest.php    ← Tests spécifiques
```

## 🔧 Implémentation de Base

### 1. Repository Abstrait Partagé

```php
<?php
// src/Shared/Infrastructure/InMemory/InMemoryRepository.php

declare(strict_types=1);

namespace App\Shared\Infrastructure\InMemory;

use App\Shared\Domain\Repository\PaginatorInterface;
use App\Shared\Domain\Repository\RepositoryInterface;
use Webmozart\Assert\Assert;

/**
 * @template T of object
 * @implements RepositoryInterface<T>
 */
abstract class InMemoryRepository implements RepositoryInterface
{
    /**
     * @var array<string, T>
     */
    protected array $entities = [];

    protected ?int $page = null;
    protected ?int $itemsPerPage = null;

    public function getIterator(): \Iterator
    {
        if (null !== $paginator = $this->paginator()) {
            yield from $paginator;
            return;
        }

        yield from $this->entities;
    }

    public function withPagination(int $page, int $itemsPerPage): static
    {
        Assert::positiveInteger($page);
        Assert::positiveInteger($itemsPerPage);

        $cloned = clone $this;
        $cloned->page = $page;
        $cloned->itemsPerPage = $itemsPerPage;

        return $cloned;
    }

    public function withoutPagination(): static
    {
        $cloned = clone $this;
        $cloned->page = null;
        $cloned->itemsPerPage = null;

        return $cloned;
    }

    public function paginator(): ?PaginatorInterface
    {
        if (null === $this->page || null === $this->itemsPerPage) {
            return null;
        }

        return new InMemoryPaginator(
            new \ArrayIterator($this->entities),
            count($this->entities),
            $this->page,
            $this->itemsPerPage,
        );
    }

    public function count(): int
    {
        if (null !== $paginator = $this->paginator()) {
            return count($paginator);
        }

        return count($this->entities);
    }

    /**
     * @param callable(mixed, mixed=): bool $filter
     * @return static<T>
     */
    protected function filter(callable $filter): static
    {
        $cloned = clone $this;
        $cloned->entities = array_filter($cloned->entities, $filter);

        return $cloned;
    }

    /**
     * Méthode utilitaire pour vider le repository (tests uniquement)
     */
    public function clear(): void
    {
        $this->entities = [];
    }
}
```

### 2. Paginator InMemory

```php
<?php
// src/Shared/Infrastructure/InMemory/InMemoryPaginator.php

declare(strict_types=1);

namespace App\Shared\Infrastructure\InMemory;

use App\Shared\Domain\Repository\PaginatorInterface;
use Webmozart\Assert\Assert;

/**
 * @template T of object
 * @implements PaginatorInterface<T>
 */
final readonly class InMemoryPaginator implements PaginatorInterface
{
    private int $offset;
    private int $limit;
    private int $lastPage;

    /**
     * @param \Traversable<T> $items
     */
    public function __construct(
        private \Traversable $items,
        private int $totalItems,
        private int $currentPage,
        private int $itemsPerPage,
    ) {
        Assert::greaterThanEq($totalItems, 0);
        Assert::positiveInteger($currentPage);
        Assert::positiveInteger($itemsPerPage);

        $this->offset = ($currentPage - 1) * $itemsPerPage;
        $this->limit = $itemsPerPage;
        $this->lastPage = (int) max(1, ceil($totalItems / $itemsPerPage));
    }

    public function getItemsPerPage(): int
    {
        return $this->itemsPerPage;
    }

    public function getCurrentPage(): int
    {
        return $this->currentPage;
    }

    public function getLastPage(): int
    {
        return $this->lastPage;
    }

    public function getTotalItems(): int
    {
        return $this->totalItems;
    }

    public function count(): int
    {
        return iterator_count($this->getIterator());
    }

    public function getIterator(): \Traversable
    {
        if ($this->currentPage > $this->lastPage) {
            return new \EmptyIterator();
        }

        return new \LimitIterator(
            new \IteratorIterator($this->items), 
            $this->offset, 
            $this->limit
        );
    }
}
```

### 3. Repository Book InMemory

```php
<?php
// src/BookStore/Infrastructure/InMemory/InMemoryBookRepository.php

declare(strict_types=1);

namespace App\BookStore\Infrastructure\InMemory;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookId;
use App\Shared\Infrastructure\InMemory\InMemoryRepository;

/**
 * @extends InMemoryRepository<Book>
 */
final class InMemoryBookRepository extends InMemoryRepository implements BookRepositoryInterface
{
    public function add(Book $book): void
    {
        $this->entities[(string) $book->id()] = $book;
    }

    public function remove(Book $book): void
    {
        unset($this->entities[(string) $book->id()]);
    }

    public function ofId(BookId $id): ?Book
    {
        return $this->entities[(string) $id] ?? null;
    }

    public function withAuthor(Author $author): static
    {
        return $this->filter(fn (Book $book) => $book->author()->isEqualTo($author));
    }

    public function withCheapestsFirst(): static
    {
        $cloned = clone $this;
        uasort($cloned->entities, fn (Book $a, Book $b) => $a->price()->amount <=> $b->price()->amount);

        return $cloned;
    }

    public function findAvailable(): array
    {
        return array_filter(
            $this->entities,
            fn(Book $book) => $book->isAvailable()
        );
    }

    public function findByAuthor(Author $author): array
    {
        return array_filter(
            $this->entities,
            fn(Book $book) => $book->author()->isEqualTo($author)
        );
    }
}
```

## ⚙️ Configuration pour Tests

### Configuration des Services de Test

```php
<?php
// config/services/test/book_store.php

declare(strict_types=1);

use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Infrastructure\Doctrine\DoctrineBookRepository;
use App\BookStore\Infrastructure\InMemory\InMemoryBookRepository;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services();

    $services->defaults()
        ->autowire()
        ->autoconfigure();

    // ⚠️ OPTIONNEL : Utiliser InMemory pour les tests
    // Commentez cette ligne pour utiliser Doctrine dans les tests
    $services->set(BookRepositoryInterface::class)
        ->class(InMemoryBookRepository::class);

    $services->set(InMemoryBookRepository::class)
        ->public(); // Pour accès direct dans les tests

    $services->set(DoctrineBookRepository::class)
        ->public();
};
```

## 🧪 Tests avec Repositories InMemory

### Test d'Intégration Rapide

```php
<?php
// tests/BookStore/Integration/InMemory/InMemoryBookRepositoryTest.php

declare(strict_types=1);

namespace App\Tests\BookStore\Integration\InMemory;

use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Infrastructure\InMemory\InMemoryBookRepository;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class InMemoryBookRepositoryTest extends KernelTestCase
{
    private InMemoryBookRepository $repository;

    protected function setUp(): void
    {
        self::bootKernel();
        $this->repository = static::getContainer()->get(InMemoryBookRepository::class);
        $this->repository->clear(); // Nettoyer avant chaque test
    }

    public function testAddAndRetrieveBook(): void
    {
        // Arrange
        $book = DummyBookFactory::createBook();

        // Act
        $this->repository->add($book);
        $retrievedBook = $this->repository->ofId($book->id());

        // Assert
        $this->assertSame($book, $retrievedBook);
        $this->assertCount(1, $this->repository);
    }

    public function testFilterByAuthor(): void
    {
        // Arrange
        $author1 = new Author('Author One');
        $author2 = new Author('Author Two');
        
        $this->repository->add(DummyBookFactory::createBook(author: 'Author One'));
        $this->repository->add(DummyBookFactory::createBook(author: 'Author One'));
        $this->repository->add(DummyBookFactory::createBook(author: 'Author Two'));

        // Act
        $booksFromAuthor1 = $this->repository->withAuthor($author1);
        $booksFromAuthor2 = $this->repository->withAuthor($author2);

        // Assert
        $this->assertCount(2, $booksFromAuthor1);
        $this->assertCount(1, $booksFromAuthor2);
    }

    public function testPagination(): void
    {
        // Arrange
        for ($i = 0; $i < 5; $i++) {
            $this->repository->add(DummyBookFactory::createBook());
        }

        // Act
        $page1 = $this->repository->withPagination(1, 2);
        $page2 = $this->repository->withPagination(2, 2);

        // Assert
        $this->assertCount(2, $page1);
        $this->assertCount(2, $page2);
        $this->assertNotNull($page1->paginator());
    }
}
```

## 🔄 Alternative : Tests avec Doctrine

### Si vous préférez tester directement avec Doctrine

```php
<?php
// tests/BookStore/Integration/Doctrine/DoctrineBookRepositoryTest.php

declare(strict_types=1);

namespace App\Tests\BookStore\Integration\Doctrine;

use App\BookStore\Infrastructure\Doctrine\DoctrineBookRepository;
use App\Tests\BookStore\DummyFactory\DummyBookFactory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class DoctrineBookRepositoryTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private DoctrineBookRepository $repository;

    protected function setUp(): void
    {
        self::bootKernel();
        $this->entityManager = static::getContainer()->get(EntityManagerInterface::class);
        $this->repository = static::getContainer()->get(DoctrineBookRepository::class);

        // Nettoyer la base de données de test
        $this->entityManager->createQuery('DELETE FROM App\BookStore\Domain\Model\Book')->execute();
    }

    public function testAddAndRetrieveBook(): void
    {
        // Arrange
        $book = DummyBookFactory::createBook();

        // Act
        $this->repository->add($book);
        $this->entityManager->flush();

        $retrievedBook = $this->repository->ofId($book->id());

        // Assert
        $this->assertNotNull($retrievedBook);
        $this->assertTrue($book->id()->equals($retrievedBook->id()));
    }
}
```

## 🎯 Recommandations d'Usage

### ✅ Utilisez InMemory quand :
- Vous voulez des **tests très rapides**
- Vous testez la **logique métier** sans persistance
- Vous faites du **prototypage rapide**
- Vous voulez **isoler** les tests de la base de données

### ❌ N'utilisez PAS InMemory quand :
- Vous testez des **requêtes SQL complexes**
- Vous voulez tester les **contraintes de base de données**
- Vous testez la **performance** de persistance
- Vous êtes en **production**

## 📊 Comparaison des Approches

| Aspect | InMemory | Doctrine |
|--------|----------|----------|
| **Vitesse** | ⚡ Très rapide | 🐌 Plus lent |
| **Isolation** | ✅ Parfaite | ⚠️ Partielle |
| **Réalisme** | ❌ Limité | ✅ Complet |
| **Configuration** | ✅ Simple | ⚠️ Complexe |
| **Requêtes SQL** | ❌ Non testées | ✅ Testées |
| **Contraintes DB** | ❌ Non testées | ✅ Testées |

## 🚀 Mise en Place Optionnelle

### Étape 1 : Créer les Classes (Optionnel)

```bash
# Créer le dossier InMemory
mkdir -p src/Shared/Infrastructure/InMemory
mkdir -p src/BookStore/Infrastructure/InMemory

# Créer les fichiers (copiez le code ci-dessus)
touch src/Shared/Infrastructure/InMemory/InMemoryRepository.php
touch src/Shared/Infrastructure/InMemory/InMemoryPaginator.php
touch src/BookStore/Infrastructure/InMemory/InMemoryBookRepository.php
```

### Étape 2 : Configuration Test (Optionnel)

```bash
# Créer la configuration de test
mkdir -p config/services/test
touch config/services/test/book_store.php
```

### Étape 3 : Tests (Optionnel)

```bash
# Créer les tests InMemory
mkdir -p tests/BookStore/Integration/InMemory
touch tests/BookStore/Integration/InMemory/InMemoryBookRepositoryTest.php
```

## ✅ Validation Optionnelle

Si vous choisissez d'implémenter les repositories InMemory :

- [ ] **Classes créées** et fonctionnelles
- [ ] **Tests passent** avec InMemory
- [ ] **Configuration** de test correcte
- [ ] **Possibilité de basculer** entre InMemory et Doctrine
- [ ] **Documentation** claire sur l'usage

## 🎯 Conclusion

Les repositories InMemory sont un **outil optionnel** qui peut accélérer vos tests, mais ils ne sont **pas indispensables** pour une application DDD fonctionnelle.

**Conseil** : Commencez par implémenter uniquement les repositories Doctrine. Ajoutez les repositories InMemory plus tard si vous ressentez le besoin d'accélérer vos tests.

---

**💡 Rappel Important** : Cette fonctionnalité est **entièrement optionnelle** et ne fait pas partie du chemin critique pour apprendre le DDD.
```
