# 🔄 Le Flux Complet d'une Requête DDD

## 🎯 Vue d'Ensemble de Votre Architecture Réelle

Voici le processus complet d'une requête dans **votre architecture DDD actuelle**, sans DTOs classiques mais avec API Platform Resources et ProcessorInterface :

```mermaid
sequenceDiagram
    participant Client as 🌐 Client HTTP
    participant API as 📦 API Platform
    participant Processor as ⚙️ ProcessorInterface
    participant CommandBus as 🚌 Command Bus
    participant Handler as ⚡ Command Handler
    participant Domain as 💎 Domain Service
    participant Entity as � Book Entity
    participant Repo as 🗄️ Repository
    participant DB as 💾 Base de Données

    Client->>API: 1. POST /api/books + JSON
    API->>API: 2. Désérialise → BookResource
    API->>API: 3. Validation annotations
    API->>Processor: 4. process(BookResource)
    Processor->>Processor: 5. Validation métier
    Processor->>Processor: 6. Resource → Value Objects
    Processor->>CommandBus: 7. dispatch(CreateBookCommand)
    CommandBus->>Handler: 8. Route automatiquement
    Handler->>Domain: 9. <PERSON> métier (optionnel)
    Handler->>Entity: 10. new Book(ValueObjects)
    Entity->>Entity: 11. Applique règles métier
    Handler->>Repo: 12. repository.add(book)
    Repo->>DB: 13. Persistance + Transaction
    Handler-->>CommandBus: 14. return Book
    CommandBus-->>Processor: 15. Book entity
    Processor->>Processor: 16. Book → BookResource
    Processor-->>API: 17. BookResource
    API->>API: 18. Sérialise → JSON
    API-->>Client: 19. HTTP 201 + JSON
```

## 📋 Processus Détaillé Étape par Étape

### 1. 🌐 **Réception de la Requête HTTP**

```php
// Le client envoie une requête
POST /api/books
Content-Type: application/json

{
    "name": "Clean Code",
    "description": "A handbook of agile software craftsmanship",
    "author": "Robert C. Martin",
    "content": "This book is about...",
    "price": 2999
}
```

**Rôle** : Point d'entrée de l'application

### 2. 📦 **API Platform - Désérialisation et Validation**

```php
<?php
// src/BookStore/Infrastructure/ApiPlatform/Resource/BookResource.php

#[ApiResource(
    operations: [
        new Post(
            validationContext: ['groups' => ['create']],
            processor: CreateBookProcessor::class, // ← Votre processor personnalisé
        ),
    ]
)]
final class BookResource
{
    public function __construct(
        #[Assert\NotNull(groups: ['create'])]
        #[Assert\Length(min: 1, max: 255, groups: ['create', 'Default'])]
        public ?string $name = null,

        #[Assert\NotNull(groups: ['create'])]
        #[Assert\Length(min: 1, max: 1023, groups: ['create', 'Default'])]
        public ?string $description = null,

        #[Assert\NotNull(groups: ['create'])]
        #[Assert\Length(min: 1, max: 255, groups: ['create', 'Default'])]
        public ?string $author = null,

        #[Assert\NotNull(groups: ['create'])]
        #[Assert\PositiveOrZero(groups: ['create', 'Default'])]
        public ?int $price = null,
    ) {}
}
```

**Étapes automatiques d'API Platform** :
- ✅ **Désérialisation** JSON → BookResource
- ✅ **Validation** des annotations Symfony
- ✅ **Routage** vers le ProcessorInterface

### 3. ⚙️ **ProcessorInterface - Pont vers le Domaine**

```php
<?php
// src/BookStore/Infrastructure/ApiPlatform/State/Processor/CreateBookProcessor.php

final readonly class CreateBookProcessor implements ProcessorInterface
{
    public function __construct(
        private CommandBusInterface $commandBus,
    ) {}

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): BookResource
    {
        // 3a. Validation stricte des données
        Assert::isInstanceOf($data, BookResource::class);
        Assert::notNull($data->name);
        Assert::notNull($data->description);
        Assert::notNull($data->author);
        Assert::notNull($data->content);
        Assert::notNull($data->price);

        // 3b. Transformation Resource → Command avec Value Objects
        $command = new CreateBookCommand(
            new BookName($data->name),
            new BookDescription($data->description),
            new Author($data->author),
            new BookContent($data->content),
            new Price($data->price), // Prix en centimes
        );

        // 3c. Dispatch vers la couche Application
        $model = $this->commandBus->dispatch($command);

        // 3d. Transformation Entity → Resource pour la réponse
        return BookResource::fromModel($model);
    }
}
```

**Responsabilités du ProcessorInterface** :
- ✅ **Validation métier** supplémentaire
- ✅ **Transformation** Resource → Value Objects → Command
- ✅ **Orchestration** via Command Bus
- ✅ **Transformation retour** Entity → Resource

### 4. 🚌 **Command Bus - Routage Intelligent**

```php
<?php
// src/Shared/Infrastructure/Symfony/Messenger/MessengerCommandBus.php

final class MessengerCommandBus implements CommandBusInterface
{
    use HandleTrait;

    public function __construct(MessageBusInterface $commandBus)
    {
        $this->messageBus = $commandBus;
    }

    public function dispatch(CommandInterface $command): mixed
    {
        try {
            return $this->handle($command); // Symfony Messenger magic
        } catch (HandlerFailedException $e) {
            // Unwrap les exceptions Symfony pour exposer les vraies exceptions métier
            if ($exception = current($e->getWrappedExceptions())) {
                throw $exception;
            }
            throw $e;
        }
    }
}
```

**Configuration automatique** :
```php
// config/packages/messenger.php
'command.bus' => [
    'middleware' => [
        'messenger.middleware.doctrine_transaction', // ← Transactions automatiques !
    ],
],

// src/Shared/Infrastructure/Symfony/Kernel.php
$container->registerAttributeForAutoconfiguration(AsCommandHandler::class,
    static function (ChildDefinition $definition): void {
        $definition->addTag('messenger.message_handler', ['bus' => 'command.bus']);
    }
);
```

**Avantages du Command Bus** :
- ✅ **Routage automatique** CreateBookCommand → CreateBookCommandHandler
- ✅ **Transactions automatiques** via middleware Doctrine
- ✅ **Gestion d'erreurs** centralisée et unwrapping
- ✅ **Découplage total** Processor ↔ Handler

### 5. ⚡ **Command Handler - Orchestrateur Métier**

```php
<?php
// src/BookStore/Application/Command/CreateBookCommandHandler.php

#[AsCommandHandler] // ← Auto-configuration Symfony
final readonly class CreateBookCommandHandler
{
    public function __construct(
        private BookRepositoryInterface $bookRepository
    ) {}

    public function __invoke(CreateBookCommand $command): Book
    {
        // 5a. Création de l'entité avec Value Objects
        $book = new Book(
            $command->name,        // BookName
            $command->description, // BookDescription
            $command->author,      // Author
            $command->content,     // BookContent
            $command->price,       // Price
        );

        // 5b. Persistance via Repository
        $this->bookRepository->add($book);

        // 5c. Retour de l'entité créée
        return $book;
    }
}
```

**Command avec Value Objects** :
```php
<?php
// src/BookStore/Application/Command/CreateBookCommand.php

/**
 * @implements CommandInterface<Book>
 */
final readonly class CreateBookCommand implements CommandInterface
{
    public function __construct(
        public BookName $name,
        public BookDescription $description,
        public Author $author,
        public BookContent $content,
        public Price $price,
    ) {}
}
```

**Responsabilités du Handler** :
- ✅ **Orchestration** simple et claire
- ✅ **Création d'entités** avec Value Objects
- ✅ **Appel du repository** pour persistance
- ✅ **Retour du résultat** vers le Processor

### 6. � **Entité Book - Cœur du Domaine**

```php
<?php
// src/BookStore/Domain/Model/Book.php

#[ORM\Entity]
class Book
{
    #[ORM\Embedded(columnPrefix: false)]
    private readonly BookId $id;

    public function __construct(
        #[ORM\Embedded(columnPrefix: false)]
        private BookName $name,

        #[ORM\Embedded(columnPrefix: false)]
        private BookDescription $description,

        #[ORM\Embedded(columnPrefix: false)]
        private Author $author,

        #[ORM\Embedded(columnPrefix: false)]
        private BookContent $content,

        #[ORM\Embedded(columnPrefix: false)]
        private Price $price,
    ) {
        $this->id = new BookId(); // Génération automatique UUID
    }

    public function update(
        ?BookName $name = null,
        ?BookDescription $description = null,
        ?Author $author = null,
        ?BookContent $content = null,
        ?Price $price = null,
    ): void {
        // Règle métier : pas de changement de prix si emprunté
        if ($price !== null && $this->isBorrowed()) {
            throw new \DomainException('Cannot update price of borrowed book');
        }

        $this->name = $name ?? $this->name;
        $this->description = $description ?? $this->description;
        $this->author = $author ?? $this->author;
        $this->content = $content ?? $this->content;
        $this->price = $price ?? $this->price;
    }

    public function applyDiscount(Discount $discount): static
    {
        $this->price = $this->price->applyDiscount($discount);
        return $this;
    }

    // Getters pour exposer les Value Objects
    public function id(): BookId { return $this->id; }
    public function name(): BookName { return $this->name; }
    public function author(): Author { return $this->author; }
    public function price(): Price { return $this->price; }
    // ... autres getters
}
```

**Caractéristiques de l'Entité** :
- ✅ **Value Objects** comme propriétés
- ✅ **Règles métier** encapsulées
- ✅ **Invariants** protégés
- ✅ **Identité** unique (BookId)

### 7. �️ **Repository - Abstraction de la Persistance**

```php
<?php
// src/BookStore/Domain/Repository/BookRepositoryInterface.php

interface BookRepositoryInterface extends RepositoryInterface
{
    public function add(Book $book): void;
    public function remove(Book $book): void;
    public function ofId(BookId $id): ?Book;

    // Méthodes de recherche métier
    public function withAuthor(Author $author): static;
    public function withCheapestsFirst(): static;
}
```

**Implémentation Doctrine** :
```php
<?php
// src/BookStore/Infrastructure/Doctrine/DoctrineBookRepository.php

final class DoctrineBookRepository implements BookRepositoryInterface
{
    use RepositoryTrait;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function add(Book $book): void
    {
        $this->entityManager->persist($book);
        // Le flush est géré par le middleware de transaction
    }

    public function remove(Book $book): void
    {
        $this->entityManager->remove($book);
    }

    public function ofId(BookId $id): ?Book
    {
        return $this->entityManager->find(Book::class, $id->value);
    }

    public function withAuthor(Author $author): static
    {
        $cloned = clone $this;
        $cloned->queryBuilder = $this->createQueryBuilder()
            ->where('book.author.value = :author')
            ->setParameter('author', $author->value);

        return $cloned;
    }

    public function withCheapestsFirst(): static
    {
        $cloned = clone $this;
        $cloned->queryBuilder = $this->createQueryBuilder()
            ->orderBy('book.price.amount', 'ASC');

        return $cloned;
    }

    private function createQueryBuilder(): QueryBuilder
    {
        return $this->entityManager->createQueryBuilder()
            ->select('book')
            ->from(Book::class, 'book');
    }
}
```

**Avantages du Repository** :
- ✅ **Interface métier** expressive
- ✅ **Abstraction** de Doctrine
- ✅ **Requêtes optimisées** avec QueryBuilder
- ✅ **Fluent interface** pour les filtres

### 8. � **Flux de Retour - Transformation pour la Réponse**

```php
// Le résultat remonte la chaîne avec transformations :

// 1. Repository → Handler : Book entity
$book = $this->bookRepository->add($book);
return $book; // Entity Book

// 2. Handler → Command Bus : Book entity
return $book; // Retour du Handler

// 3. Command Bus → Processor : Book entity
$model = $this->commandBus->dispatch($command); // Book entity

// 4. Processor → API Platform : BookResource
return BookResource::fromModel($model);

// 5. API Platform → Client : JSON
{
    "id": "01234567-89ab-cdef-0123-456789abcdef",
    "name": "Clean Code",
    "description": "A handbook of agile software craftsmanship",
    "author": "Robert C. Martin",
    "content": "This book is about...",
    "price": 2999
}
```

**Transformation BookResource::fromModel()** :
```php
<?php
// src/BookStore/Infrastructure/ApiPlatform/Resource/BookResource.php

public static function fromModel(Book $book): self
{
    return new self(
        id: $book->id()->value,
        name: $book->name()->value,
        description: $book->description()?->value,
        author: $book->author()->value,
        content: $book->content()?->value,
        price: $book->price()->amount,
    );
}
```

### 9. 💾 **Base de Données - Persistance Finale**

```sql
-- Doctrine génère et exécute automatiquement :
INSERT INTO book (
    id,
    name,
    description,
    author,
    content,
    price
) VALUES (
    '01234567-89ab-cdef-0123-456789abcdef',
    'Clean Code',
    'A handbook of agile software craftsmanship',
    'Robert C. Martin',
    'This book is about...',
    2999
);

-- Transaction automatique via middleware :
BEGIN;
INSERT INTO book (...) VALUES (...);
COMMIT; -- ou ROLLBACK en cas d'exception
```

## 🎯 **Avantages de Votre Architecture DDD**

### ✅ **Séparation des Responsabilités Claire**

| Couche | Responsabilité | Exemple |
|--------|----------------|---------|
| **API Platform** | HTTP, Sérialisation | BookResource, validation annotations |
| **ProcessorInterface** | Transformation, Orchestration | CreateBookProcessor |
| **Command Bus** | Routage, Middleware | MessengerCommandBus + transactions |
| **Command Handler** | Logique applicative | CreateBookCommandHandler |
| **Entity** | Règles métier | Book avec invariants |
| **Repository** | Persistance | DoctrineBookRepository |

### ✅ **Validation Multi-Niveaux Robuste**

```php
// 1. Validation API Platform (syntaxique)
#[Assert\NotNull(groups: ['create'])]
#[Assert\Length(min: 1, max: 255)]
public ?string $name = null,

// 2. Validation Processor (métier)
Assert::isInstanceOf($data, BookResource::class);
Assert::notNull($data->name);

// 3. Validation Value Objects (domaine)
new BookName($data->name); // Lève InvalidArgumentException si invalide

// 4. Validation Entity (invariants)
if ($price !== null && $this->isBorrowed()) {
    throw new \DomainException('Cannot update price of borrowed book');
}
```

### ✅ **Gestion d'Erreurs Centralisée**

```php
// Dans MessengerCommandBus
try {
    return $this->handle($command);
} catch (HandlerFailedException $e) {
    // Unwrap automatique des exceptions Symfony
    if ($exception = current($e->getWrappedExceptions())) {
        throw $exception; // Exception métier pure
    }
    throw $e;
}

// Dans API Platform (automatique)
// DomainException → HTTP 400
// InvalidArgumentException → HTTP 400
// \Exception → HTTP 500
```

### ✅ **Extensibilité et Évolutivité**

```php
// Facile d'ajouter des fonctionnalités transversales :

// 1. Middleware Command Bus
'middleware' => [
    'messenger.middleware.doctrine_transaction',
    'messenger.middleware.audit_log',        // ← Nouveau
    'messenger.middleware.rate_limiting',    // ← Nouveau
    'messenger.middleware.validation',       // ← Nouveau
],

// 2. Nouveaux Processors
new Post(processor: CreateBookProcessor::class),
new Put(processor: UpdateBookProcessor::class),   // ← Nouveau
new Patch(processor: DiscountBookProcessor::class), // ← Nouveau

// 3. Nouveaux Commands/Handlers
class UpdateBookCommand implements CommandInterface {}
class UpdateBookCommandHandler {} // Auto-découverte

// 4. Nouveaux Value Objects
class ISBN extends StringValueObject {}
class PublicationDate extends DateValueObject {}
```

## 🚀 **Exemple Concret Complet de Votre Architecture**

### 📝 **Trace Complète d'une Requête**

```bash
# 1. Requête Client
curl -X POST http://localhost:8000/api/books \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Domain-Driven Design",
    "description": "Tackling Complexity in the Heart of Software",
    "author": "Eric Evans",
    "content": "This book provides a framework...",
    "price": 4500
  }'
```

```php
// 2. API Platform → BookResource (désérialisation automatique)
$bookResource = new BookResource(
    name: "Domain-Driven Design",
    description: "Tackling Complexity in the Heart of Software",
    author: "Eric Evans",
    content: "This book provides a framework...",
    price: 4500
);

// 3. API Platform → CreateBookProcessor
$processor->process($bookResource, $operation, [], []);

// 4. Processor → Command avec Value Objects
$command = new CreateBookCommand(
    new BookName("Domain-Driven Design"),
    new BookDescription("Tackling Complexity in the Heart of Software"),
    new Author("Eric Evans"),
    new BookContent("This book provides a framework..."),
    new Price(4500) // Validation : prix > 0
);

// 5. Command Bus → Handler (routage automatique)
$commandBus->dispatch($command); // → CreateBookCommandHandler

// 6. Handler → Entity
$book = new Book(
    $command->name,        // BookName
    $command->description, // BookDescription
    $command->author,      // Author
    $command->content,     // BookContent
    $command->price        // Price
); // Génère automatiquement BookId

// 7. Handler → Repository
$this->bookRepository->add($book);

// 8. Repository → Doctrine
$this->entityManager->persist($book);
// Transaction automatique via middleware

// 9. Doctrine → Base de données
INSERT INTO book (id, name, description, author, content, price)
VALUES ('01234567-89ab-cdef-0123-456789abcdef', 'Domain-Driven Design', ...);

// 10. Retour Handler → Processor
return $book; // Entity Book

// 11. Processor → API Platform
return BookResource::fromModel($book);

// 12. API Platform → Client (sérialisation automatique)
HTTP/1.1 201 Created
Content-Type: application/json

{
    "id": "01234567-89ab-cdef-0123-456789abcdef",
    "name": "Domain-Driven Design",
    "description": "Tackling Complexity in the Heart of Software",
    "author": "Eric Evans",
    "content": "This book provides a framework...",
    "price": 4500
}
```

## 🎯 **Comparaison : Votre Architecture vs Architecture Classique**

### ❌ **Architecture Classique (Sans DDD)**
```php
Controller → Service → Repository → Database
// Logique métier éparpillée, couplage fort
```

### ✅ **Votre Architecture DDD**
```php
API Platform → Processor → Command Bus → Handler → Entity → Repository → Database
// Séparation claire, logique métier centralisée, découplage total
```

## 🏆 **Bénéfices Concrets de Votre Approche**

### ✅ **Testabilité Maximale**
```php
// Test unitaire du Handler (isolé)
$handler = new CreateBookCommandHandler($mockRepository);
$book = $handler($command);
$this->assertInstanceOf(Book::class, $book);

// Test d'intégration du Processor
$processor = new CreateBookProcessor($mockCommandBus);
$result = $processor->process($bookResource, $operation, [], []);

// Test end-to-end de l'API
$this->client->request('POST', '/api/books', [], [], [], $json);
$this->assertResponseStatusCodeSame(201);
```

### ✅ **Réutilisabilité Totale**
```php
// Même Command utilisable partout
$command = new CreateBookCommand(/* ... */);

// Dans l'API REST
$this->commandBus->dispatch($command);

// Dans une commande CLI
$this->commandBus->dispatch($command);

// Dans un worker de queue
$this->commandBus->dispatch($command);

// Dans un test
$this->commandBus->dispatch($command);
```

### ✅ **Évolutivité Sans Limite**
- **Nouveaux endpoints** : Ajouter un Processor
- **Nouvelle logique** : Ajouter un Handler
- **Nouvelles règles** : Modifier l'Entity
- **Nouvelle persistance** : Changer le Repository
- **Nouveaux formats** : Modifier la Resource

---

**💡 Conclusion** : Votre architecture DDD avec API Platform offre le meilleur des deux mondes : la **simplicité** d'API Platform et la **robustesse** du DDD ! 🚀
