# 🧪 Étape 2 : Tests Unitaires du Modèle

## 🎯 Objectifs de cette Étape

À la fin de cette étape, vous saurez :
- ✅ Créer votre première entité `Book`
- ✅ Écrire des tests unitaires complets
- ✅ Appliquer les principes TDD (Test-Driven Development)
- ✅ Valider la logique métier avec des tests

**Temps estimé** : 60 minutes

## 🔄 Approche TDD : Red-Green-Refactor

### Le Cycle TDD

```mermaid
graph LR
    A[🔴 RED<br/>Écrire un test qui échoue] --> B[🟢 GREEN<br/>Écrire le code minimal]
    B --> C[🔵 REFACTOR<br/>Améliorer le code]
    C --> A
    
    style A fill:#ffebee
    style B fill:#e8f5e8
    style C fill:#e3f2fd
```

### Pourquoi TDD ?
1. **Spécification vivante** : Les tests documentent le comportement
2. **Confiance** : Refactoring sans peur de casser
3. **Design** : Force à penser à l'API avant l'implémentation
4. **Couverture** : 100% de couverture naturellement

## 📝 Étape 1 : Définir les Exigences Métier

### User Stories pour l'Entité Book

```gherkin
Feature: Gestion des livres
  En tant que libraire
  Je veux gérer les livres de mon inventaire
  Pour pouvoir les vendre et les prêter

Scenario: Créer un nouveau livre
  Given je veux ajouter un livre à l'inventaire
  When je crée un livre avec un titre et un prix
  Then le livre doit avoir un identifiant unique
  And le livre doit être disponible par défaut

Scenario: Emprunter un livre disponible
  Given un livre disponible
  When un client emprunte le livre
  Then le livre devient indisponible
  And la date d'emprunt est enregistrée

Scenario: Retourner un livre emprunté
  Given un livre emprunté
  When le client retourne le livre
  Then le livre redevient disponible
  And la date d'emprunt est effacée

Scenario: Changer le prix d'un livre
  Given un livre disponible
  When je change le prix du livre
  Then le nouveau prix est appliqué

Scenario: Interdire le changement de prix d'un livre emprunté
  Given un livre emprunté
  When j'essaie de changer le prix
  Then une exception est levée
```

## 🧪 Étape 2 : Créer les Tests Unitaires

### Configuration PHPUnit

**phpunit.xml.dist**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="tests/bootstrap.php"
         colors="true">
    
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory>tests/Integration</directory>
        </testsuite>
        <testsuite name="EndToEnd">
            <directory>tests/EndToEnd</directory>
        </testsuite>
    </testsuites>

    <source>
        <include>
            <directory>src</directory>
        </include>
        <exclude>
            <directory>src/Shared/Infrastructure/Maker</directory>
        </exclude>
    </source>
</phpunit>
```

### Premier Test : BookTest

```php
<?php
// tests/Unit/BookStore/Domain/Model/BookTest.php

declare(strict_types=1);

namespace App\Tests\Unit\BookStore\Domain\Model;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\ValueObject\BookId;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\Price;
use PHPUnit\Framework\TestCase;

class BookTest extends TestCase
{
    private BookId $bookId;
    private BookName $bookName;
    private Author $author;
    private Price $price;

    protected function setUp(): void
    {
        $this->bookId = BookId::generate();
        $this->bookName = new BookName('Clean Code');
        $this->author = new Author('Robert C. Martin');
        $this->price = new Price(2999); // Prix en centimes
    }

    public function testCreateBook(): void
    {
        // Act
        $book = new Book(
            $this->bookName,
            $this->author,
            $this->price
        );

        // Assert
        $this->assertInstanceOf(BookId::class, $book->id());
        $this->assertEquals($this->bookName, $book->name());
        $this->assertEquals($this->author, $book->author());
        $this->assertEquals($this->price, $book->price());
        $this->assertTrue($book->isAvailable());
        $this->assertFalse($book->isBorrowed());
        $this->assertNull($book->borrowedAt());
    }

    public function testBorrowAvailableBook(): void
    {
        // Arrange
        $book = new Book($this->bookName, $this->author, $this->price);
        $this->assertTrue($book->isAvailable());

        // Act
        $book->borrow();

        // Assert
        $this->assertFalse($book->isAvailable());
        $this->assertTrue($book->isBorrowed());
        $this->assertInstanceOf(\DateTimeImmutable::class, $book->borrowedAt());
        
        // Vérifier que la date d'emprunt est récente (moins de 1 seconde)
        $now = new \DateTimeImmutable();
        $diff = $now->getTimestamp() - $book->borrowedAt()->getTimestamp();
        $this->assertLessThan(1, $diff);
    }

    public function testCannotBorrowAlreadyBorrowedBook(): void
    {
        // Arrange
        $book = new Book($this->bookName, $this->author, $this->price);
        $book->borrow();

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Book is not available for borrowing');

        // Act
        $book->borrow();
    }

    public function testReturnBorrowedBook(): void
    {
        // Arrange
        $book = new Book($this->bookName, $this->author, $this->price);
        $book->borrow();
        $this->assertTrue($book->isBorrowed());

        // Act
        $book->return();

        // Assert
        $this->assertTrue($book->isAvailable());
        $this->assertFalse($book->isBorrowed());
        $this->assertNull($book->borrowedAt());
    }

    public function testCannotReturnAvailableBook(): void
    {
        // Arrange
        $book = new Book($this->bookName, $this->author, $this->price);

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Book is not currently borrowed');

        // Act
        $book->return();
    }

    public function testUpdateBookProperties(): void
    {
        // Arrange
        $book = new Book($this->bookName, $this->author, $this->price);
        $newName = new BookName('Clean Architecture');
        $newAuthor = new Author('Robert C. Martin');
        $newPrice = new Price(3499);

        // Act
        $book->update($newName, null, $newAuthor, null, $newPrice);

        // Assert
        $this->assertEquals($newName, $book->name());
        $this->assertEquals($newAuthor, $book->author());
        $this->assertEquals($newPrice, $book->price());
    }

    public function testCannotUpdatePriceOfBorrowedBook(): void
    {
        // Arrange
        $book = new Book($this->bookName, $this->author, $this->price);
        $book->borrow();
        $newPrice = new Price(1999);

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Cannot update price of borrowed book');

        // Act
        $book->update(null, null, null, null, $newPrice);
    }

    public function testApplyDiscount(): void
    {
        // Arrange
        $book = new Book($this->bookName, $this->author, $this->price);
        $discount = new \App\BookStore\Domain\ValueObject\Discount(10); // 10%

        // Act
        $book->applyDiscount($discount);

        // Assert
        $expectedPrice = new Price(2699); // 2999 - 10% = 2699
        $this->assertEquals($expectedPrice, $book->price());
    }

    /**
     * @dataProvider validUpdateDataProvider
     */
    public function testUpdateWithValidData(
        ?BookName $name,
        ?Author $author,
        ?Price $price,
        string $expectedName,
        string $expectedAuthor,
        int $expectedPrice
    ): void {
        // Arrange
        $book = new Book($this->bookName, $this->author, $this->price);

        // Act
        $book->update($name, null, $author, null, $price);

        // Assert
        $this->assertSame($expectedName, $book->name()->value);
        $this->assertSame($expectedAuthor, $book->author()->value);
        $this->assertSame($expectedPrice, $book->price()->amount);
    }

    public static function validUpdateDataProvider(): array
    {
        return [
            'update name only' => [
                new BookName('New Title'),
                null,
                null,
                'New Title',
                'Robert C. Martin',
                2999
            ],
            'update author only' => [
                null,
                new Author('New Author'),
                null,
                'Clean Code',
                'New Author',
                2999
            ],
            'update price only' => [
                null,
                null,
                new Price(1999),
                'Clean Code',
                'Robert C. Martin',
                1999
            ],
            'update all' => [
                new BookName('New Title'),
                new Author('New Author'),
                new Price(1999),
                'New Title',
                'New Author',
                1999
            ],
        ];
    }
}
```

## 🔴 Phase RED : Exécuter les Tests (qui échouent)

```bash
# Exécuter les tests (ils vont échouer car les classes n'existent pas encore)
./vendor/bin/phpunit tests/Unit/BookStore/Domain/Model/BookTest.php
```

**Résultat attendu** : Erreurs car les classes n'existent pas encore.

## 🟢 Phase GREEN : Implémenter le Code Minimal

### Étape 1 : Entité Book

```php
<?php
// src/BookStore/Domain/Model/Book.php

declare(strict_types=1);

namespace App\BookStore\Domain\Model;

use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookContent;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookId;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Discount;
use App\BookStore\Domain\ValueObject\Price;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class Book
{
    #[ORM\Embedded(columnPrefix: false)]
    private readonly BookId $id;

    private bool $isAvailable = true;
    private ?\DateTimeImmutable $borrowedAt = null;

    public function __construct(
        #[ORM\Embedded(columnPrefix: false)]
        private BookName $name,

        #[ORM\Embedded(columnPrefix: false)]
        private Author $author,

        #[ORM\Embedded(columnPrefix: false)]
        private Price $price,

        #[ORM\Embedded(columnPrefix: false)]
        private ?BookDescription $description = null,

        #[ORM\Embedded(columnPrefix: false)]
        private ?BookContent $content = null,
    ) {
        $this->id = new BookId();
    }

    public function borrow(): void
    {
        if (!$this->isAvailable) {
            throw new \DomainException('Book is not available for borrowing');
        }

        $this->isAvailable = false;
        $this->borrowedAt = new \DateTimeImmutable();
    }

    public function return(): void
    {
        if ($this->isAvailable) {
            throw new \DomainException('Book is not currently borrowed');
        }

        $this->isAvailable = true;
        $this->borrowedAt = null;
    }

    public function update(
        ?BookName $name = null,
        ?BookDescription $description = null,
        ?Author $author = null,
        ?BookContent $content = null,
        ?Price $price = null,
    ): void {
        if ($price !== null && !$this->isAvailable) {
            throw new \DomainException('Cannot update price of borrowed book');
        }

        $this->name = $name ?? $this->name;
        $this->description = $description ?? $this->description;
        $this->author = $author ?? $this->author;
        $this->content = $content ?? $this->content;
        $this->price = $price ?? $this->price;
    }

    public function applyDiscount(Discount $discount): static
    {
        $this->price = $this->price->applyDiscount($discount);
        return $this;
    }

    // Getters
    public function id(): BookId
    {
        return $this->id;
    }

    public function name(): BookName
    {
        return $this->name;
    }

    public function author(): Author
    {
        return $this->author;
    }

    public function price(): Price
    {
        return $this->price;
    }

    public function description(): ?BookDescription
    {
        return $this->description;
    }

    public function content(): ?BookContent
    {
        return $this->content;
    }

    public function isAvailable(): bool
    {
        return $this->isAvailable;
    }

    public function isBorrowed(): bool
    {
        return !$this->isAvailable;
    }

    public function borrowedAt(): ?\DateTimeImmutable
    {
        return $this->borrowedAt;
    }
}
```

### Étape 2 : Value Objects (voir étape 3 pour les détails)

Les Value Objects seront créés dans l'étape suivante. Pour l'instant, créons les versions minimales :

## ✅ Validation de l'Étape

```bash
# Exécuter les tests unitaires
./vendor/bin/phpunit tests/Unit/BookStore/Domain/Model/BookTest.php

# Vérifier la couverture de code
./vendor/bin/phpunit --coverage-html var/coverage tests/Unit/
```

**Résultat attendu** : Tous les tests passent au vert ! 🟢

### Checklist de Validation

- [ ] **Tous les tests passent** sans erreur
- [ ] **Couverture de code** > 95% sur l'entité Book
- [ ] **Logique métier** respectée (emprunt, retour, etc.)
- [ ] **Validation des données** avec des exceptions appropriées
- [ ] **Value Objects** immutables et validés

## 🎯 Prochaine Étape

Maintenant que notre modèle de domaine est solide avec des tests complets, nous allons approfondir les **Value Objects** dans l'**Étape 3 : Définition des Value Objects**.

---

**💡 Points Clés à Retenir**
- TDD garantit un code testé et bien conçu
- Les tests documentent le comportement attendu
- La logique métier doit être dans le domaine
- Les Value Objects apportent robustesse et expressivité
