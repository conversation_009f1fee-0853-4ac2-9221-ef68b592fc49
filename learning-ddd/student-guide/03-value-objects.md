# 💎 Étape 3 : Définition des Value Objects

## 🎯 Objectifs de cette Étape

À la fin de cette étape, vous saurez :
- ✅ Comprendre les principes des Value Objects
- ✅ Créer des Value Objects robustes et immutables
- ✅ Tester leur validation et comportement
- ✅ Utiliser les makers DDD pour la génération

**Temps estimé** : 45 minutes

## 💎 Qu'est-ce qu'un Value Object ?

### Définition

Un **Value Object** est un objet qui :
- N'a **pas d'identité** propre
- Est **immutable** (ne peut pas être modifié)
- Est défini par ses **valeurs**
- Implémente l'**égalité par valeur**

### Exemples Concrets

```mermaid
graph LR
    subgraph "🏠 Adresse"
        A1[Rue: '123 Main St']
        A2[Ville: 'Paris']
        A3[Code Postal: '75001']
    end
    
    subgraph "💰 Prix"
        P1[Montant: 2999]
        P2[Devise: 'EUR']
    end
    
    subgraph "📧 Email"
        E1[Valeur: '<EMAIL>']
        E2[Validation: Format valide]
    end
    
    style A1 fill:#e8f5e8
    style A2 fill:#e8f5e8
    style A3 fill:#e8f5e8
    style P1 fill:#fff3e0
    style P2 fill:#fff3e0
    style E1 fill:#e1f5fe
    style E2 fill:#e1f5fe
```

## 🛠️ Création avec les Makers DDD

### Utilisation du Maker

```bash
# Générer un Value Object string embeddable
php bin/console make:ddd:value-object BookStore BookName --type=string --embeddable

# Générer un Value Object int embeddable
php bin/console make:ddd:value-object BookStore Price --type=int --embeddable

# Générer un Value Object simple (non embeddable)
php bin/console make:ddd:value-object BookStore Discount --type=int
```

## 📝 Implémentation Complète des Value Objects

### 1. BookId - Identifiant Unique

```php
<?php
// src/BookStore/Domain/ValueObject/BookId.php

declare(strict_types=1);

namespace App\BookStore\Domain\ValueObject;

use App\Shared\Domain\ValueObject\AggregateRootId;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
final class BookId implements \Stringable
{
    use AggregateRootId;
}
```

### 2. BookName - Nom du Livre

```php
<?php
// src/BookStore/Domain/ValueObject/BookName.php

declare(strict_types=1);

namespace App\BookStore\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;
use Webmozart\Assert\Assert;

#[ORM\Embeddable]
final class BookName
{
    #[ORM\Column(name: 'name', length: 255)]
    public readonly string $value;

    public function __construct(string $value)
    {
        Assert::lengthBetween($value, 1, 255, 'Book name must be between 1 and 255 characters');
        Assert::notEmpty(trim($value), 'Book name cannot be empty or only whitespace');
        
        $this->value = trim($value);
    }

    public function isEqualTo(self $other): bool
    {
        return $other->value === $this->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

### 3. Author - Auteur

```php
<?php
// src/BookStore/Domain/ValueObject/Author.php

declare(strict_types=1);

namespace App\BookStore\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;
use Webmozart\Assert\Assert;

#[ORM\Embeddable]
final class Author
{
    #[ORM\Column(name: 'author', length: 255)]
    public readonly string $value;

    public function __construct(string $value)
    {
        Assert::lengthBetween($value, 1, 255, 'Author name must be between 1 and 255 characters');
        Assert::notEmpty(trim($value), 'Author name cannot be empty');
        
        $this->value = trim($value);
    }

    public function isEqualTo(self $other): bool
    {
        return $other->value === $this->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

### 4. Price - Prix en Centimes

```php
<?php
// src/BookStore/Domain/ValueObject/Price.php

declare(strict_types=1);

namespace App\BookStore\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;
use Webmozart\Assert\Assert;

#[ORM\Embeddable]
final class Price
{
    #[ORM\Column(name: 'price', type: 'integer', options: ['unsigned' => true])]
    public readonly int $amount;

    public function __construct(int $amount)
    {
        Assert::greaterThanEq($amount, 0, 'Price cannot be negative');
        Assert::lessThanEq($amount, 999999999, 'Price is too high');
        
        $this->amount = $amount;
    }

    public function applyDiscount(Discount $discount): static
    {
        $discountAmount = (int) ($this->amount * $discount->percentage / 100);
        $newAmount = $this->amount - $discountAmount;
        
        return new static(max(0, $newAmount));
    }

    public function add(Price $other): static
    {
        return new static($this->amount + $other->amount);
    }

    public function multiply(int $factor): static
    {
        Assert::greaterThanEq($factor, 0, 'Factor cannot be negative');
        
        return new static($this->amount * $factor);
    }

    public function toEuros(): float
    {
        return $this->amount / 100;
    }

    public function isEqualTo(self $other): bool
    {
        return $other->amount === $this->amount;
    }

    public function isGreaterThan(self $other): bool
    {
        return $this->amount > $other->amount;
    }

    public function isLessThan(self $other): bool
    {
        return $this->amount < $other->amount;
    }

    public function __toString(): string
    {
        return number_format($this->toEuros(), 2) . ' €';
    }
}
```

### 5. Discount - Remise

```php
<?php
// src/BookStore/Domain/ValueObject/Discount.php

declare(strict_types=1);

namespace App\BookStore\Domain\ValueObject;

use Webmozart\Assert\Assert;

final class Discount
{
    public readonly int $percentage;

    public function __construct(int $percentage)
    {
        Assert::range($percentage, 0, 100, 'Discount percentage must be between 0 and 100');
        
        $this->percentage = $percentage;
    }

    public function isEqualTo(self $other): bool
    {
        return $other->percentage === $this->percentage;
    }

    public function __toString(): string
    {
        return $this->percentage . '%';
    }
}
```

### 6. BookDescription - Description

```php
<?php
// src/BookStore/Domain/ValueObject/BookDescription.php

declare(strict_types=1);

namespace App\BookStore\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;
use Webmozart\Assert\Assert;

#[ORM\Embeddable]
final class BookDescription
{
    #[ORM\Column(name: 'description', type: 'text', nullable: true)]
    public readonly ?string $value;

    public function __construct(?string $value = null)
    {
        if ($value !== null) {
            Assert::maxLength($value, 2000, 'Description cannot exceed 2000 characters');
            $value = trim($value);
            $value = $value === '' ? null : $value;
        }
        
        $this->value = $value;
    }

    public function isEqualTo(self $other): bool
    {
        return $other->value === $this->value;
    }

    public function __toString(): string
    {
        return $this->value ?? '';
    }
}
```

### 7. BookContent - Contenu

```php
<?php
// src/BookStore/Domain/ValueObject/BookContent.php

declare(strict_types=1);

namespace App\BookStore\Domain\ValueObject;

use Doctrine\ORM\Mapping as ORM;
use Webmozart\Assert\Assert;

#[ORM\Embeddable]
final class BookContent
{
    #[ORM\Column(name: 'content', type: 'text', nullable: true)]
    public readonly ?string $value;

    public function __construct(?string $value = null)
    {
        if ($value !== null) {
            Assert::minLength($value, 10, 'Book content must be at least 10 characters');
            $value = trim($value);
        }
        
        $this->value = $value;
    }

    public function getWordCount(): int
    {
        if ($this->value === null) {
            return 0;
        }
        
        return str_word_count($this->value);
    }

    public function getExcerpt(int $length = 100): string
    {
        if ($this->value === null) {
            return '';
        }
        
        if (strlen($this->value) <= $length) {
            return $this->value;
        }
        
        return substr($this->value, 0, $length) . '...';
    }

    public function isEqualTo(self $other): bool
    {
        return $other->value === $this->value;
    }

    public function __toString(): string
    {
        return $this->value ?? '';
    }
}
```

## 🧪 Tests des Value Objects

### Test de BookName

```php
<?php
// tests/Unit/BookStore/Domain/ValueObject/BookNameTest.php

declare(strict_types=1);

namespace App\Tests\Unit\BookStore\Domain\ValueObject;

use App\BookStore\Domain\ValueObject\BookName;
use PHPUnit\Framework\TestCase;
use Webmozart\Assert\InvalidArgumentException;

class BookNameTest extends TestCase
{
    public function testCreateValidBookName(): void
    {
        $name = new BookName('Clean Code');

        $this->assertSame('Clean Code', $name->value);
        $this->assertSame('Clean Code', (string) $name);
    }

    public function testTrimWhitespace(): void
    {
        $name = new BookName('  Clean Code  ');

        $this->assertSame('Clean Code', $name->value);
    }

    public function testEqualityComparison(): void
    {
        $name1 = new BookName('Clean Code');
        $name2 = new BookName('Clean Code');
        $name3 = new BookName('Different Book');

        $this->assertTrue($name1->isEqualTo($name2));
        $this->assertFalse($name1->isEqualTo($name3));
    }

    /**
     * @dataProvider invalidNameProvider
     */
    public function testInvalidBookName(string $invalidName, string $expectedMessage): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage($expectedMessage);

        new BookName($invalidName);
    }

    public static function invalidNameProvider(): array
    {
        return [
            'empty string' => ['', 'Book name must be between 1 and 255 characters'],
            'only whitespace' => ['   ', 'Book name cannot be empty or only whitespace'],
            'too long' => [str_repeat('a', 256), 'Book name must be between 1 and 255 characters'],
        ];
    }
}
```

### Test de Price

```php
<?php
// tests/Unit/BookStore/Domain/ValueObject/PriceTest.php

declare(strict_types=1);

namespace App\Tests\Unit\BookStore\Domain\ValueObject;

use App\BookStore\Domain\ValueObject\Discount;
use App\BookStore\Domain\ValueObject\Price;
use PHPUnit\Framework\TestCase;
use Webmozart\Assert\InvalidArgumentException;

class PriceTest extends TestCase
{
    public function testCreateValidPrice(): void
    {
        $price = new Price(2999);

        $this->assertSame(2999, $price->amount);
        $this->assertSame(29.99, $price->toEuros());
        $this->assertSame('29.99 €', (string) $price);
    }

    public function testApplyDiscount(): void
    {
        $price = new Price(2000);
        $discount = new Discount(10);

        $discountedPrice = $price->applyDiscount($discount);

        $this->assertSame(1800, $discountedPrice->amount);
        $this->assertNotSame($price, $discountedPrice); // Immutabilité
    }

    public function testPriceOperations(): void
    {
        $price1 = new Price(1000);
        $price2 = new Price(500);

        // Addition
        $sum = $price1->add($price2);
        $this->assertSame(1500, $sum->amount);

        // Multiplication
        $multiplied = $price1->multiply(3);
        $this->assertSame(3000, $multiplied->amount);

        // Comparaisons
        $this->assertTrue($price1->isGreaterThan($price2));
        $this->assertFalse($price1->isLessThan($price2));
        $this->assertTrue($price1->isEqualTo(new Price(1000)));
    }

    public function testNegativePriceThrowsException(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Price cannot be negative');

        new Price(-100);
    }

    public function testTooHighPriceThrowsException(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Price is too high');

        new Price(1000000000);
    }
}
```

## ✅ Validation de l'Étape

### Commandes de Test

```bash
# Tester tous les Value Objects
./vendor/bin/phpunit tests/Unit/BookStore/Domain/ValueObject/

# Tester un Value Object spécifique
./vendor/bin/phpunit tests/Unit/BookStore/Domain/ValueObject/PriceTest.php

# Vérifier la couverture
./vendor/bin/phpunit --coverage-html var/coverage tests/Unit/BookStore/Domain/ValueObject/
```

### Checklist de Validation

- [ ] **Tous les Value Objects** sont créés et testés
- [ ] **Immutabilité** respectée (pas de setters)
- [ ] **Validation** des données à la construction
- [ ] **Égalité par valeur** implémentée
- [ ] **Tests complets** avec cas d'erreur
- [ ] **Couverture de code** > 95%

## 🎯 Prochaine Étape

Maintenant que nos Value Objects sont robustes, nous allons implémenter les **Repositories** dans l'**Étape 4 : Ajout des Repositories**.

---

**💡 Points Clés à Retenir**
- Les Value Objects encapsulent et valident les données
- L'immutabilité garantit la cohérence
- Les tests valident tous les cas d'usage
- La validation précoce évite les erreurs
```
