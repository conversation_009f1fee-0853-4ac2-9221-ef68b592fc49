# 🟡 Advanced Domain - Agrégats et Invariants

## 🎯 Objectifs de ce Guide

À la fin de ce chapitre, vous saurez :
- ✅ Concevoir des agrégats avec leurs frontières
- ✅ Implémenter des invariants complexes
- ✅ Gérer les relations entre entités
- ✅ Utiliser les domain events
- ✅ Optimiser la cohérence transactionnelle

**Temps estimé** : 60 minutes  
**Prérequis** : Avoir terminé le niveau débutant

## 📚 Qu'est-ce qu'un Agrégat ?

Un **Agrégat** est un groupe d'entités et de value objects qui :
- ✅ Forment une **unité de cohérence** métier
- ✅ Ont une **racine d'agrégat** (Aggregate Root)
- ✅ Maintiennent des **invariants** entre leurs composants
- ✅ Définissent une **frontière transactionnelle**

### Exemple : Commande de Livres

```
Order (Aggregate Root)
├── OrderId (Value Object)
├── CustomerId (Value Object)
├── OrderStatus (Value Object)
└── OrderItems[] (Entités enfants)
    ├── OrderItemId
    ├── BookId
    ├── Quantity
    └── UnitPrice
```

## 🏗️ Conception d'un Agrégat Order

### Étape 1 : Value Objects de l'Agrégat

```php
<?php
// src/Domain/ValueObject/OrderId.php

namespace App\Domain\ValueObject;

final readonly class OrderId
{
    public function __construct(public string $value)
    {
        if (empty($value)) {
            throw new \InvalidArgumentException('OrderId cannot be empty');
        }
    }

    public static function generate(): self
    {
        return new self(uniqid('order_', true));
    }

    public function equals(OrderId $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

```php
<?php
// src/Domain/ValueObject/OrderStatus.php

namespace App\Domain\ValueObject;

enum OrderStatus: string
{
    case PENDING = 'pending';
    case CONFIRMED = 'confirmed';
    case SHIPPED = 'shipped';
    case DELIVERED = 'delivered';
    case CANCELLED = 'cancelled';

    public function canBeModified(): bool
    {
        return $this === self::PENDING;
    }

    public function canBeCancelled(): bool
    {
        return in_array($this, [self::PENDING, self::CONFIRMED]);
    }

    public function canBeShipped(): bool
    {
        return $this === self::CONFIRMED;
    }
}
```

```php
<?php
// src/Domain/ValueObject/Quantity.php

namespace App\Domain\ValueObject;

final readonly class Quantity
{
    public function __construct(public int $value)
    {
        if ($value <= 0) {
            throw new \InvalidArgumentException('Quantity must be positive');
        }

        if ($value > 10) {
            throw new \InvalidArgumentException('Maximum quantity is 10 per item');
        }
    }

    public function add(Quantity $other): self
    {
        return new self($this->value + $other->value);
    }

    public function multiply(int $factor): self
    {
        return new self($this->value * $factor);
    }
}
```

### Étape 2 : Entité OrderItem

```php
<?php
// src/Domain/Model/OrderItem.php

namespace App\Domain\Model;

use App\Domain\ValueObject\BookId;
use App\Domain\ValueObject\Price;
use App\Domain\ValueObject\Quantity;

class OrderItem
{
    private string $id;

    public function __construct(
        private readonly BookId $bookId,
        private Quantity $quantity,
        private readonly Price $unitPrice
    ) {
        $this->id = uniqid('item_', true);
    }

    public function increaseQuantity(Quantity $additionalQuantity): void
    {
        $this->quantity = $this->quantity->add($additionalQuantity);
    }

    public function updateQuantity(Quantity $newQuantity): void
    {
        $this->quantity = $newQuantity;
    }

    public function getTotalPrice(): Price
    {
        return new Price(
            $this->unitPrice->amount * $this->quantity->value,
            $this->unitPrice->currency
        );
    }

    public function isForBook(BookId $bookId): bool
    {
        return $this->bookId->equals($bookId);
    }

    // Getters
    public function getId(): string { return $this->id; }
    public function getBookId(): BookId { return $this->bookId; }
    public function getQuantity(): Quantity { return $this->quantity; }
    public function getUnitPrice(): Price { return $this->unitPrice; }
}
```

### Étape 3 : Agrégat Order (Racine)

```php
<?php
// src/Domain/Model/Order.php

namespace App\Domain\Model;

use App\Domain\ValueObject\BookId;
use App\Domain\ValueObject\CustomerId;
use App\Domain\ValueObject\OrderId;
use App\Domain\ValueObject\OrderStatus;
use App\Domain\ValueObject\Price;
use App\Domain\ValueObject\Quantity;

class Order
{
    private readonly OrderId $id;
    private OrderStatus $status;
    private array $items = [];
    private \DateTimeImmutable $createdAt;

    public function __construct(
        private readonly CustomerId $customerId
    ) {
        $this->id = OrderId::generate();
        $this->status = OrderStatus::PENDING;
        $this->createdAt = new \DateTimeImmutable();
    }

    // === MÉTHODES MÉTIER ===

    public function addBook(BookId $bookId, Quantity $quantity, Price $unitPrice): void
    {
        $this->guardAgainstModificationWhenNotPending();

        $existingItem = $this->findItemForBook($bookId);
        
        if ($existingItem) {
            $existingItem->increaseQuantity($quantity);
        } else {
            $this->items[] = new OrderItem($bookId, $quantity, $unitPrice);
        }

        $this->guardAgainstTooManyItems();
    }

    public function removeBook(BookId $bookId): void
    {
        $this->guardAgainstModificationWhenNotPending();

        $this->items = array_filter(
            $this->items,
            fn(OrderItem $item) => !$item->isForBook($bookId)
        );

        $this->items = array_values($this->items); // Réindexer
    }

    public function updateBookQuantity(BookId $bookId, Quantity $newQuantity): void
    {
        $this->guardAgainstModificationWhenNotPending();

        $item = $this->findItemForBook($bookId);
        if (!$item) {
            throw new \DomainException('Book not found in order');
        }

        $item->updateQuantity($newQuantity);
    }

    public function confirm(): void
    {
        $this->guardAgainstEmptyOrder();
        $this->guardAgainstAlreadyConfirmed();

        $this->status = OrderStatus::CONFIRMED;
    }

    public function ship(): void
    {
        if (!$this->status->canBeShipped()) {
            throw new \DomainException('Order cannot be shipped in current status');
        }

        $this->status = OrderStatus::SHIPPED;
    }

    public function cancel(): void
    {
        if (!$this->status->canBeCancelled()) {
            throw new \DomainException('Order cannot be cancelled in current status');
        }

        $this->status = OrderStatus::CANCELLED;
    }

    // === CALCULS ===

    public function getTotalPrice(): Price
    {
        if (empty($this->items)) {
            return new Price(0, 'EUR');
        }

        $total = 0;
        $currency = $this->items[0]->getUnitPrice()->currency;

        foreach ($this->items as $item) {
            if ($item->getUnitPrice()->currency !== $currency) {
                throw new \DomainException('All items must have the same currency');
            }
            $total += $item->getTotalPrice()->amount;
        }

        return new Price($total, $currency);
    }

    public function getItemCount(): int
    {
        return array_sum(
            array_map(
                fn(OrderItem $item) => $item->getQuantity()->value,
                $this->items
            )
        );
    }

    // === INVARIANTS (RÈGLES MÉTIER) ===

    private function guardAgainstModificationWhenNotPending(): void
    {
        if (!$this->status->canBeModified()) {
            throw new \DomainException('Cannot modify order in status: ' . $this->status->value);
        }
    }

    private function guardAgainstEmptyOrder(): void
    {
        if (empty($this->items)) {
            throw new \DomainException('Cannot confirm empty order');
        }
    }

    private function guardAgainstAlreadyConfirmed(): void
    {
        if ($this->status !== OrderStatus::PENDING) {
            throw new \DomainException('Order is already confirmed');
        }
    }

    private function guardAgainstTooManyItems(): void
    {
        if ($this->getItemCount() > 50) {
            throw new \DomainException('Order cannot contain more than 50 items');
        }
    }

    // === MÉTHODES UTILITAIRES ===

    private function findItemForBook(BookId $bookId): ?OrderItem
    {
        foreach ($this->items as $item) {
            if ($item->isForBook($bookId)) {
                return $item;
            }
        }
        return null;
    }

    // === GETTERS ===

    public function getId(): OrderId { return $this->id; }
    public function getCustomerId(): CustomerId { return $this->customerId; }
    public function getStatus(): OrderStatus { return $this->status; }
    public function getItems(): array { return $this->items; }
    public function getCreatedAt(): \DateTimeImmutable { return $this->createdAt; }
}
```

## 🎯 Exercice Pratique 1 : Tests de l'Agrégat

Créez des tests complets pour l'agrégat `Order` qui valident :
- L'ajout et suppression d'items
- Les calculs de prix total
- Tous les invariants métier
- Les transitions d'état

<details>
<summary>💡 Structure de test</summary>

```php
<?php
// tests/Unit/Domain/Model/OrderTest.php

namespace App\Tests\Unit\Domain\Model;

use App\Domain\Model\Order;
use App\Domain\ValueObject\BookId;
use App\Domain\ValueObject\CustomerId;
use App\Domain\ValueObject\Price;
use App\Domain\ValueObject\Quantity;
use PHPUnit\Framework\TestCase;

class OrderTest extends TestCase
{
    private Order $order;
    private CustomerId $customerId;

    protected function setUp(): void
    {
        $this->customerId = new CustomerId('customer_123');
        $this->order = new Order($this->customerId);
    }

    public function testCreateOrder(): void
    {
        $this->assertSame($this->customerId, $this->order->getCustomerId());
        $this->assertSame(OrderStatus::PENDING, $this->order->getStatus());
        $this->assertEmpty($this->order->getItems());
    }

    public function testAddBookToOrder(): void
    {
        $bookId = new BookId('book_123');
        $quantity = new Quantity(2);
        $price = new Price(1999, 'EUR');

        $this->order->addBook($bookId, $quantity, $price);

        $this->assertCount(1, $this->order->getItems());
        $this->assertSame(2, $this->order->getItemCount());
    }

    // ... autres tests
}
```
</details>

## 🔄 Domain Events

### Étape 1 : Interface et Trait pour Events

```php
<?php
// src/Domain/Event/DomainEventInterface.php

namespace App\Domain\Event;

interface DomainEventInterface
{
    public function getOccurredAt(): \DateTimeImmutable;
    public function getAggregateId(): string;
}
```

```php
<?php
// src/Domain/Event/DomainEventTrait.php

namespace App\Domain\Event;

trait DomainEventTrait
{
    private array $domainEvents = [];

    protected function recordEvent(DomainEventInterface $event): void
    {
        $this->domainEvents[] = $event;
    }

    public function getUncommittedEvents(): array
    {
        return $this->domainEvents;
    }

    public function markEventsAsCommitted(): void
    {
        $this->domainEvents = [];
    }
}
```

### Étape 2 : Events Spécifiques

```php
<?php
// src/Domain/Event/OrderConfirmed.php

namespace App\Domain\Event;

use App\Domain\ValueObject\CustomerId;
use App\Domain\ValueObject\OrderId;
use App\Domain\ValueObject\Price;

final readonly class OrderConfirmed implements DomainEventInterface
{
    public function __construct(
        public OrderId $orderId,
        public CustomerId $customerId,
        public Price $totalAmount,
        public int $itemCount,
        public \DateTimeImmutable $occurredAt = new \DateTimeImmutable()
    ) {}

    public function getOccurredAt(): \DateTimeImmutable
    {
        return $this->occurredAt;
    }

    public function getAggregateId(): string
    {
        return $this->orderId->value;
    }
}
```

### Étape 3 : Intégration dans l'Agrégat

```php
<?php
// Modifier la classe Order

use App\Domain\Event\DomainEventTrait;
use App\Domain\Event\OrderConfirmed;

class Order
{
    use DomainEventTrait;

    // ... code existant

    public function confirm(): void
    {
        $this->guardAgainstEmptyOrder();
        $this->guardAgainstAlreadyConfirmed();

        $this->status = OrderStatus::CONFIRMED;

        // Enregistrer l'événement
        $this->recordEvent(new OrderConfirmed(
            $this->id,
            $this->customerId,
            $this->getTotalPrice(),
            $this->getItemCount()
        ));
    }
}
```

## 🎯 Exercice Pratique 2 : Agrégat Library

Créez un agrégat `Library` qui gère les emprunts avec ces règles :
- Un membre peut emprunter max 3 livres
- Durée d'emprunt : 14 jours
- Amende : 0.50€/jour de retard
- Les livres en retard bloquent nouveaux emprunts

<details>
<summary>💡 Structure suggérée</summary>

```
Library (Aggregate Root)
├── LibraryId
├── Loans[] (Entités enfants)
│   ├── LoanId
│   ├── MemberId
│   ├── BookId
│   ├── BorrowedAt
│   ├── DueDate
│   └── ReturnedAt?
└── Rules (Value Object)
    ├── MaxLoansPerMember
    ├── LoanDurationDays
    └── DailyFineAmount
```
</details>

## 🔍 Patterns Avancés

### Specification Pattern

```php
<?php
// src/Domain/Specification/OrderSpecification.php

namespace App\Domain\Specification;

use App\Domain\Model\Order;

interface OrderSpecificationInterface
{
    public function isSatisfiedBy(Order $order): bool;
}

class CanBeShippedSpecification implements OrderSpecificationInterface
{
    public function isSatisfiedBy(Order $order): bool
    {
        return $order->getStatus()->canBeShipped()
            && $order->getItemCount() > 0
            && $order->getTotalPrice()->amount > 0;
    }
}

class IsEligibleForDiscountSpecification implements OrderSpecificationInterface
{
    public function __construct(private float $minimumAmount) {}

    public function isSatisfiedBy(Order $order): bool
    {
        return $order->getTotalPrice()->amount >= $this->minimumAmount;
    }
}
```

### Factory Pattern pour Agrégats

```php
<?php
// src/Domain/Factory/OrderFactory.php

namespace App\Domain\Factory;

use App\Domain\Model\Order;
use App\Domain\ValueObject\CustomerId;

class OrderFactory
{
    public function createForCustomer(CustomerId $customerId): Order
    {
        return new Order($customerId);
    }

    public function createFromCart(CustomerId $customerId, array $cartItems): Order
    {
        $order = new Order($customerId);

        foreach ($cartItems as $item) {
            $order->addBook(
                $item['bookId'],
                $item['quantity'],
                $item['price']
            );
        }

        return $order;
    }
}
```

## ✅ Checkpoint - Validation des Acquis

Avant de passer au guide suivant, vérifiez que vous savez :

- [ ] **Concevoir** des agrégats avec leurs frontières
- [ ] **Implémenter** des invariants complexes
- [ ] **Gérer** les entités enfants dans un agrégat
- [ ] **Utiliser** les domain events
- [ ] **Appliquer** les patterns Specification et Factory

## 🎯 Mini-Projet : E-commerce Order Management

Créez un système de gestion de commandes avec :

1. **Agrégat Order** complet avec items
2. **Domain Events** pour les changements d'état
3. **Specifications** pour les règles business
4. **Factory** pour créer des commandes
5. **Tests** complets avec tous les invariants

**Temps estimé** : 2-3 heures

## 🚀 Prochaine Étape

Une fois ce guide maîtrisé, passez à **[02-domain-services.md](02-domain-services.md)** pour apprendre :
- Les services de domaine
- La logique métier inter-agrégats
- Les policies et strategies
- L'orchestration complexe

---

**Excellent !** 🎉 Vous maîtrisez maintenant les agrégats complexes et leurs invariants !
