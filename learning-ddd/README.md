# 🎓 Learning DDD - Apprentissage Progressif

## 📋 Vue d'Ensemble

Ce dossier contient une approche d'apprentissage progressive du Domain-Driven Design (DDD), organisée par niveaux de difficulté et par couches. Chaque guide vous fait progresser étape par étape, du plus simple au plus complexe.

## 🎯 Objectif Pédagogique

Apprendre le DDD de manière **progressive** et **pratique** :
- ✅ **Commencer simple** : Une classe, un test, un concept
- ✅ **Complexifier graduellement** : Ajouter des patterns et des relations
- ✅ **Pratiquer constamment** : Chaque concept avec des exercices
- ✅ **Tester systématiquement** : Tests unitaires et d'intégration
- ✅ **Refactorer intelligemment** : Améliorer le code existant

## 📚 Structure d'Apprentissage

### 🟢 Niveau Débutant (Beginner)
**Objectif** : Comprendre les bases de chaque couche DDD

```
beginner/
├── 01-domain-basics.md          ← Value Objects simples
├── 02-entity-basics.md          ← Entités avec ID et état
├── 03-repository-basics.md      ← Repository en mémoire
├── 04-application-basics.md     ← Commands/Queries simples
├── 05-infrastructure-basics.md  ← Persistance basique
├── 06-api-basics.md             ← API REST simple
├── 07-tests-basics.md           ← Tests unitaires de base
└── exercises/                   ← Exercices guidés
    ├── exercise-01-book.md
    ├── exercise-02-author.md
    └── exercise-03-simple-api.md
```

### 🟡 Niveau Intermédiaire (Intermediate)
**Objectif** : Maîtriser les patterns et relations entre couches

```
intermediate/
├── 01-advanced-domain.md        ← Agrégats et invariants
├── 02-domain-services.md       ← Services de domaine
├── 03-cqrs-patterns.md         ← CQRS avec Symfony Messenger
├── 04-repository-patterns.md   ← Repositories avec Doctrine
├── 05-api-platform.md          ← API Platform avancée
├── 06-testing-strategies.md    ← Tests d'intégration
├── 07-event-driven.md          ← Domain Events
└── exercises/
    ├── exercise-01-library.md
    ├── exercise-02-ordering.md
    └── exercise-03-notifications.md
```

### 🔴 Niveau Avancé (Advanced)
**Objectif** : Architectures complexes et optimisations

```
advanced/
├── 01-bounded-contexts.md      ← Multiples contextes
├── 02-hexagonal-architecture.md ← Architecture hexagonale complète
├── 03-performance-optimization.md ← Optimisations
├── 04-microservices-ddd.md    ← DDD et microservices
├── 05-event-sourcing.md       ← Event Sourcing
├── 06-production-ready.md     ← Déploiement et monitoring
└── exercises/
    ├── exercise-01-ecommerce.md
    ├── exercise-02-multi-tenant.md
    └── exercise-03-event-sourcing.md
```

## 🎮 Méthode d'Apprentissage

### 1. **Lecture Active** (15 min)
- Lire le guide du niveau
- Comprendre les concepts théoriques
- Analyser les exemples de code

### 2. **Pratique Guidée** (30 min)
- Suivre les exemples pas à pas
- Écrire le code dans votre environnement
- Exécuter les tests

### 3. **Exercice Autonome** (45 min)
- Réaliser l'exercice proposé
- Adapter les concepts à votre contexte
- Créer vos propres tests

### 4. **Réflexion et Amélioration** (15 min)
- Analyser ce qui a été appris
- Identifier les points d'amélioration
- Préparer le niveau suivant

## 🛠️ Prérequis Techniques

### Environnement de Développement
- **PHP 8.2+** avec extensions (json, pdo, intl)
- **Composer** pour la gestion des dépendances
- **Symfony CLI** pour les commandes
- **PHPUnit** pour les tests
- **Git** pour le versioning

### Connaissances Recommandées
- **PHP orienté objet** (classes, interfaces, traits)
- **Symfony basics** (services, configuration)
- **Bases de données** (SQL, relations)
- **Tests unitaires** (PHPUnit)
- **API REST** (HTTP, JSON)

## 📈 Progression Recommandée

### Semaine 1 : Fondations
- **Jour 1-2** : Domain Basics (Value Objects, Entités)
- **Jour 3-4** : Repository et Application Basics
- **Jour 5-7** : Infrastructure, API et Tests Basics

### Semaine 2 : Patterns
- **Jour 1-2** : Advanced Domain (Agrégats, Services)
- **Jour 3-4** : CQRS et Repository Patterns
- **Jour 5-7** : API Platform et Testing Strategies

### Semaine 3 : Architecture
- **Jour 1-2** : Event-Driven et Bounded Contexts
- **Jour 3-4** : Hexagonal Architecture
- **Jour 5-7** : Performance et Production

## 🎯 Objectifs par Niveau

### 🟢 Débutant - "Je comprends les bases"
- [ ] Créer des Value Objects avec validation
- [ ] Modéliser des entités simples
- [ ] Implémenter un repository en mémoire
- [ ] Écrire des commands/queries basiques
- [ ] Créer une API REST simple
- [ ] Écrire des tests unitaires

### 🟡 Intermédiaire - "Je maîtrise les patterns"
- [ ] Concevoir des agrégats avec invariants
- [ ] Utiliser des services de domaine
- [ ] Implémenter CQRS avec Symfony Messenger
- [ ] Configurer Doctrine pour DDD
- [ ] Créer des APIs avec API Platform
- [ ] Écrire des tests d'intégration

### 🔴 Avancé - "Je conçois des architectures"
- [ ] Organiser plusieurs bounded contexts
- [ ] Implémenter l'architecture hexagonale
- [ ] Optimiser les performances
- [ ] Gérer les domain events
- [ ] Déployer en production
- [ ] Monitorer et maintenir

## 🏆 Validation des Acquis

### Auto-Évaluation
Chaque niveau contient des **checkpoints** pour valider vos acquis :
- ✅ **Quiz théoriques** : Concepts et principes
- ✅ **Exercices pratiques** : Implémentation
- ✅ **Code reviews** : Qualité et bonnes pratiques
- ✅ **Tests** : Couverture et pertinence

### Critères de Réussite
- **Code fonctionnel** : Les exemples s'exécutent
- **Tests passants** : Couverture > 80%
- **Bonnes pratiques** : Respect des principes DDD
- **Compréhension** : Capacité à expliquer les choix

## 🚀 Commencer Maintenant

1. **Choisissez votre niveau** selon votre expérience
2. **Lisez le premier guide** de votre niveau
3. **Suivez les exemples** pas à pas
4. **Réalisez l'exercice** proposé
5. **Passez au guide suivant**

---

**Conseil** : Ne brûlez pas les étapes ! Chaque niveau construit sur le précédent. Mieux vaut bien maîtriser les bases que de se précipiter vers la complexité.

**Bonne formation !** 🎓✨
