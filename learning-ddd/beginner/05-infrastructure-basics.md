# 🟢 Infrastructure Basics - Persistance et Configuration

## 🎯 Objectifs de ce Guide

À la fin de ce chapitre, vous saurez :
- ✅ Configurer Doctrine pour vos entités
- ✅ Créer un repository Doctrine simple
- ✅ Configurer l'injection de dépendances
- ✅ Basculer entre repositories (mémoire/Doctrine)

**Temps estimé** : 45 minutes

## 📚 Infrastructure vs Domain

### Séparation des Responsabilités

- **Domain** : Règles métier, entités, interfaces
- **Infrastructure** : Détails techniques, base de données, frameworks

```
Domain ← Infrastructure
  ↑         ↓
Interfaces  Implémentations
```

## 🗃️ Configuration Doctrine Simple

### Étape 1 : Mapping des Entités

```php
<?php
// src/Domain/Model/Book.php - Ajouter les annotations Doctrine

namespace App\Domain\Model;

use App\Domain\ValueObject\BookId;
use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\Price;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'books')]
class Book
{
    #[ORM\Id]
    #[ORM\Column(type: 'string', length: 255)]
    private string $id;

    #[ORM\Column(type: 'string', length: 255)]
    private string $title;

    #[ORM\Column(type: 'float')]
    private float $price;

    #[ORM\Column(type: 'string', length: 3)]
    private string $currency;

    #[ORM\Column(type: 'boolean')]
    private bool $isAvailable = true;

    #[ORM\Column(type: 'datetime_immutable', nullable: true)]
    private ?\DateTimeImmutable $borrowedAt = null;

    public function __construct(
        BookId $id,
        BookTitle $title,
        Price $price
    ) {
        $this->id = $id->value;
        $this->title = $title->value;
        $this->price = $price->amount;
        $this->currency = $price->currency;
    }

    // Méthodes pour reconstruire les Value Objects
    public function getId(): BookId
    {
        return new BookId($this->id);
    }

    public function getTitle(): BookTitle
    {
        return new BookTitle($this->title);
    }

    public function getPrice(): Price
    {
        return new Price($this->price, $this->currency);
    }

    // ... autres méthodes métier
}
```

### Étape 2 : Repository Doctrine

```php
<?php
// src/Infrastructure/Repository/DoctrineBookRepository.php

namespace App\Infrastructure\Repository;

use App\Domain\Model\Book;
use App\Domain\Repository\BookRepositoryInterface;
use App\Domain\ValueObject\BookId;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class DoctrineBookRepository extends ServiceEntityRepository implements BookRepositoryInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Book::class);
    }

    public function save(Book $book): void
    {
        $this->getEntityManager()->persist($book);
        $this->getEntityManager()->flush();
    }

    public function findById(BookId $id): ?Book
    {
        return $this->find($id->value);
    }

    public function findAll(): array
    {
        return $this->findBy([], ['title' => 'ASC']);
    }

    public function findAvailable(): array
    {
        return $this->findBy(['isAvailable' => true], ['title' => 'ASC']);
    }

    public function remove(Book $book): void
    {
        $this->getEntityManager()->remove($book);
        $this->getEntityManager()->flush();
    }

    public function count(): int
    {
        return $this->createQueryBuilder('b')
            ->select('COUNT(b.id)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function findByTitleContaining(string $search): array
    {
        return $this->createQueryBuilder('b')
            ->where('LOWER(b.title) LIKE LOWER(:search)')
            ->setParameter('search', '%' . $search . '%')
            ->orderBy('b.title', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findByPriceRange(float $minPrice, float $maxPrice): array
    {
        return $this->createQueryBuilder('b')
            ->where('b.price >= :minPrice')
            ->andWhere('b.price <= :maxPrice')
            ->setParameter('minPrice', $minPrice)
            ->setParameter('maxPrice', $maxPrice)
            ->orderBy('b.price', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findBorrowedLongerThan(int $days): array
    {
        $cutoffDate = new \DateTimeImmutable("-{$days} days");

        return $this->createQueryBuilder('b')
            ->where('b.isAvailable = false')
            ->andWhere('b.borrowedAt < :cutoffDate')
            ->setParameter('cutoffDate', $cutoffDate)
            ->getQuery()
            ->getResult();
    }
}
```

## ⚙️ Configuration des Services

### Étape 1 : Configuration Symfony

```yaml
# config/packages/doctrine.yaml
doctrine:
    dbal:
        url: '%env(resolve:DATABASE_URL)%'
    orm:
        auto_generate_proxy_classes: true
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        auto_mapping: true
        mappings:
            App:
                is_bundle: false
                type: attribute
                dir: '%kernel.project_dir%/src/Domain/Model'
                prefix: 'App\Domain\Model'
```

### Étape 2 : Services en Production

```yaml
# config/services.yaml
services:
    _defaults:
        autowire: true
        autoconfigure: true

    # Repositories
    App\Domain\Repository\BookRepositoryInterface:
        class: App\Infrastructure\Repository\DoctrineBookRepository

    App\Domain\Repository\MemberRepositoryInterface:
        class: App\Infrastructure\Repository\DoctrineMemberRepository

    # Handlers
    App\Application\Handler\:
        resource: '../src/Application/Handler'

    # Services
    App\Application\Service\:
        resource: '../src/Application/Service'
```

### Étape 3 : Services pour les Tests

```yaml
# config/services_test.yaml
services:
    # Override repositories pour les tests
    App\Domain\Repository\BookRepositoryInterface:
        class: App\Infrastructure\Repository\InMemoryBookRepository

    App\Domain\Repository\MemberRepositoryInterface:
        class: App\Infrastructure\Repository\InMemoryMemberRepository

    # Rendre les repositories accessibles dans les tests
    App\Infrastructure\Repository\InMemoryBookRepository:
        public: true

    App\Infrastructure\Repository\InMemoryMemberRepository:
        public: true
```

## 🧪 Test avec Vraie Base de Données

### Étape 1 : Configuration de Test

```php
<?php
// tests/Integration/Infrastructure/DoctrineBookRepositoryTest.php

namespace App\Tests\Integration\Infrastructure;

use App\Domain\Repository\BookRepositoryInterface;
use App\Tests\Support\BookFactory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class DoctrineBookRepositoryTest extends KernelTestCase
{
    private BookRepositoryInterface $repository;
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        self::bootKernel();
        
        $this->entityManager = self::getContainer()
            ->get(EntityManagerInterface::class);
        
        $this->repository = self::getContainer()
            ->get(BookRepositoryInterface::class);

        // Nettoyer la base de données
        $this->cleanDatabase();
    }

    protected function tearDown(): void
    {
        $this->cleanDatabase();
        parent::tearDown();
    }

    private function cleanDatabase(): void
    {
        $this->entityManager->createQuery('DELETE FROM App\Domain\Model\Book')->execute();
        $this->entityManager->clear();
    }

    public function testSaveAndFindBook(): void
    {
        // Arrange
        $book = BookFactory::create('Test Book', 25.99);

        // Act
        $this->repository->save($book);
        $foundBook = $this->repository->findById($book->getId());

        // Assert
        $this->assertNotNull($foundBook);
        $this->assertTrue($book->getId()->equals($foundBook->getId()));
        $this->assertSame('Test Book', $foundBook->getTitle()->value);
        $this->assertSame(25.99, $foundBook->getPrice()->amount);
    }

    public function testFindAvailableBooks(): void
    {
        // Arrange
        $availableBook = BookFactory::create('Available Book');
        $borrowedBook = BookFactory::createBorrowed('Borrowed Book');

        $this->repository->save($availableBook);
        $this->repository->save($borrowedBook);

        // Act
        $availableBooks = $this->repository->findAvailable();

        // Assert
        $this->assertCount(1, $availableBooks);
        $this->assertTrue(
            $availableBook->getId()->equals($availableBooks[0]->getId())
        );
    }

    public function testSearchByTitle(): void
    {
        // Arrange
        $books = [
            BookFactory::create('Clean Code'),
            BookFactory::create('Clean Architecture'),
            BookFactory::create('Dirty Code'),
        ];

        foreach ($books as $book) {
            $this->repository->save($book);
        }

        // Act
        $cleanBooks = $this->repository->findByTitleContaining('Clean');

        // Assert
        $this->assertCount(2, $cleanBooks);
    }
}
```

## 🔄 Basculer entre Implémentations

### Factory Pattern pour les Tests

```php
<?php
// tests/Support/RepositoryFactory.php

namespace App\Tests\Support;

use App\Domain\Repository\BookRepositoryInterface;
use App\Infrastructure\Repository\InMemoryBookRepository;
use App\Infrastructure\Repository\DoctrineBookRepository;

class RepositoryFactory
{
    public static function createBookRepository(string $type = 'memory'): BookRepositoryInterface
    {
        return match ($type) {
            'memory' => new InMemoryBookRepository(),
            'doctrine' => self::createDoctrineBookRepository(),
            default => throw new \InvalidArgumentException("Unknown repository type: {$type}")
        };
    }

    private static function createDoctrineBookRepository(): DoctrineBookRepository
    {
        // Pour les tests d'intégration
        $kernel = \Symfony\Bundle\FrameworkBundle\Test\KernelTestCase::bootKernel();
        return $kernel->getContainer()->get(DoctrineBookRepository::class);
    }
}
```

### Test Abstrait pour Tous les Repositories

```php
<?php
// tests/Unit/Repository/BookRepositoryTestCase.php

namespace App\Tests\Unit\Repository;

use App\Domain\Repository\BookRepositoryInterface;
use App\Tests\Support\BookFactory;
use PHPUnit\Framework\TestCase;

abstract class BookRepositoryTestCase extends TestCase
{
    protected BookRepositoryInterface $repository;

    abstract protected function createRepository(): BookRepositoryInterface;

    protected function setUp(): void
    {
        $this->repository = $this->createRepository();
    }

    public function testSaveAndFindBook(): void
    {
        $book = BookFactory::create();
        
        $this->repository->save($book);
        $foundBook = $this->repository->findById($book->getId());
        
        $this->assertNotNull($foundBook);
        $this->assertTrue($book->getId()->equals($foundBook->getId()));
    }

    public function testFindAvailableBooks(): void
    {
        $availableBook = BookFactory::create();
        $borrowedBook = BookFactory::createBorrowed();

        $this->repository->save($availableBook);
        $this->repository->save($borrowedBook);

        $availableBooks = $this->repository->findAvailable();

        $this->assertCount(1, $availableBooks);
    }

    // ... autres tests communs
}
```

### Tests Spécifiques par Implémentation

```php
<?php
// tests/Unit/Repository/InMemoryBookRepositoryTest.php

namespace App\Tests\Unit\Repository;

use App\Domain\Repository\BookRepositoryInterface;
use App\Infrastructure\Repository\InMemoryBookRepository;

class InMemoryBookRepositoryTest extends BookRepositoryTestCase
{
    protected function createRepository(): BookRepositoryInterface
    {
        return new InMemoryBookRepository();
    }

    public function testClearRepository(): void
    {
        // Test spécifique à l'implémentation en mémoire
        $book = \App\Tests\Support\BookFactory::create();
        $this->repository->save($book);

        $this->repository->clear();

        $this->assertSame(0, $this->repository->count());
    }
}
```

## 🎯 Exercice Pratique : Migration de Données

Créez un script qui :
1. Lit des données depuis un fichier JSON
2. Les convertit en entités
3. Les sauvegarde via le repository

<details>
<summary>💡 Structure du JSON</summary>

```json
{
  "books": [
    {
      "title": "Clean Code",
      "price": 29.99,
      "currency": "EUR"
    },
    {
      "title": "Clean Architecture",
      "price": 35.99,
      "currency": "EUR"
    }
  ]
}
```
</details>

## ✅ Checkpoint - Validation des Acquis

Avant de passer au guide suivant, vérifiez que vous savez :

- [ ] **Configurer** Doctrine pour vos entités
- [ ] **Créer** un repository Doctrine
- [ ] **Configurer** l'injection de dépendances
- [ ] **Tester** avec une vraie base de données
- [ ] **Basculer** entre implémentations

## 🎯 Mini-Projet : Configuration Complète

Configurez un projet complet avec :

1. **Entités** mappées avec Doctrine
2. **Repositories** Doctrine et en mémoire
3. **Configuration** pour production et tests
4. **Tests** d'intégration avec base de données
5. **Migration** de données depuis un fichier

**Temps estimé** : 90 minutes

## 🚀 Prochaine Étape

Une fois ce guide maîtrisé, passez à **[06-api-basics.md](06-api-basics.md)** pour apprendre :
- Créer une API REST simple
- Exposer vos cas d'usage
- Gérer les erreurs HTTP

---

**Parfait !** 🎉 Vous savez maintenant persister vos données avec Doctrine et configurer votre infrastructure !
