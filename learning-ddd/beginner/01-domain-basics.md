# 🟢 Domain Basics - Les Fondations du Domaine

## 🎯 Objectifs de ce Guide

À la fin de ce chapitre, vous saurez :
- ✅ Créer votre premier Value Object
- ✅ Comprendre l'immutabilité et la validation
- ✅ Écrire des tests unitaires simples
- ✅ Utiliser les Value Objects dans une entité basique

**Temps estimé** : 45 minutes

## 📚 Qu'est-ce qu'un Value Object ?

Un **Value Object** est un objet simple qui :
- N'a **pas d'identité** (pas d'ID)
- Est **immuable** (ne change jamais)
- Est défini par ses **valeurs**
- **Valide** ses données

### Exemple Concret : Un Prix

Imaginez un prix dans un magasin :
- 15.99€ = 15.99€ (même valeur = même objet)
- Ne peut pas être négatif
- Ne change pas une fois créé

## 🛠️ Votre Premier Value Object

### Étape 1 : Créer la Classe Price

```php
<?php
// src/Domain/ValueObject/Price.php

namespace App\Domain\ValueObject;

final readonly class Price
{
    public function __construct(
        public float $amount,
        public string $currency = 'EUR'
    ) {
        $this->validate($amount, $currency);
    }

    private function validate(float $amount, string $currency): void
    {
        if ($amount < 0) {
            throw new \InvalidArgumentException('Price cannot be negative');
        }

        if (!in_array($currency, ['EUR', 'USD', 'GBP'])) {
            throw new \InvalidArgumentException('Invalid currency');
        }
    }

    public function __toString(): string
    {
        return sprintf('%.2f %s', $this->amount, $this->currency);
    }
}
```

### Étape 2 : Tester Votre Value Object

```php
<?php
// tests/Unit/Domain/ValueObject/PriceTest.php

namespace App\Tests\Unit\Domain\ValueObject;

use App\Domain\ValueObject\Price;
use PHPUnit\Framework\TestCase;

class PriceTest extends TestCase
{
    public function testCreateValidPrice(): void
    {
        // Arrange & Act
        $price = new Price(15.99, 'EUR');

        // Assert
        $this->assertSame(15.99, $price->amount);
        $this->assertSame('EUR', $price->currency);
        $this->assertSame('15.99 EUR', (string) $price);
    }

    public function testRejectNegativePrice(): void
    {
        // Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Price cannot be negative');

        // Act
        new Price(-10.0);
    }

    public function testRejectInvalidCurrency(): void
    {
        // Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid currency');

        // Act
        new Price(15.99, 'XYZ');
    }
}
```

### Étape 3 : Exécuter le Test

```bash
# Créer le dossier de test
mkdir -p tests/Unit/Domain/ValueObject

# Exécuter le test
./vendor/bin/phpunit tests/Unit/Domain/ValueObject/PriceTest.php
```

## 🎯 Exercice Pratique 1 : BookTitle

Créez un Value Object `BookTitle` qui :
- Accepte une chaîne de caractères
- Refuse les titres vides
- Refuse les titres de plus de 255 caractères
- Nettoie les espaces en début/fin

<details>
<summary>💡 Solution</summary>

```php
<?php
// src/Domain/ValueObject/BookTitle.php

namespace App\Domain\ValueObject;

final readonly class BookTitle
{
    public function __construct(public string $value)
    {
        $this->validate($value);
    }

    private function validate(string $value): void
    {
        $trimmed = trim($value);
        
        if (empty($trimmed)) {
            throw new \InvalidArgumentException('Book title cannot be empty');
        }

        if (strlen($trimmed) > 255) {
            throw new \InvalidArgumentException('Book title too long');
        }
    }

    public function __toString(): string
    {
        return trim($this->value);
    }
}
```

```php
<?php
// tests/Unit/Domain/ValueObject/BookTitleTest.php

namespace App\Tests\Unit\Domain\ValueObject;

use App\Domain\ValueObject\BookTitle;
use PHPUnit\Framework\TestCase;

class BookTitleTest extends TestCase
{
    public function testCreateValidTitle(): void
    {
        $title = new BookTitle('Clean Code');
        $this->assertSame('Clean Code', $title->value);
    }

    public function testRejectEmptyTitle(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        new BookTitle('');
    }

    public function testRejectTooLongTitle(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        new BookTitle(str_repeat('a', 256));
    }
}
```
</details>

## 🏛️ Votre Première Entité Simple

Une **entité** a une identité unique et peut changer d'état.

### Étape 1 : Créer l'Entité Book

```php
<?php
// src/Domain/Model/Book.php

namespace App\Domain\Model;

use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\Price;

class Book
{
    private string $id;

    public function __construct(
        private BookTitle $title,
        private Price $price
    ) {
        $this->id = uniqid('book_', true);
    }

    public function changePrice(Price $newPrice): void
    {
        $this->price = $newPrice;
    }

    // Getters
    public function getId(): string
    {
        return $this->id;
    }

    public function getTitle(): BookTitle
    {
        return $this->title;
    }

    public function getPrice(): Price
    {
        return $this->price;
    }
}
```

### Étape 2 : Tester l'Entité

```php
<?php
// tests/Unit/Domain/Model/BookTest.php

namespace App\Tests\Unit\Domain\Model;

use App\Domain\Model\Book;
use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\Price;
use PHPUnit\Framework\TestCase;

class BookTest extends TestCase
{
    public function testCreateBook(): void
    {
        // Arrange
        $title = new BookTitle('Clean Code');
        $price = new Price(29.99);

        // Act
        $book = new Book($title, $price);

        // Assert
        $this->assertNotEmpty($book->getId());
        $this->assertSame($title, $book->getTitle());
        $this->assertSame($price, $book->getPrice());
    }

    public function testChangePrice(): void
    {
        // Arrange
        $book = new Book(
            new BookTitle('Clean Code'),
            new Price(29.99)
        );
        $newPrice = new Price(24.99);

        // Act
        $book->changePrice($newPrice);

        // Assert
        $this->assertSame($newPrice, $book->getPrice());
        $this->assertSame(24.99, $book->getPrice()->amount);
    }

    public function testBooksHaveDifferentIds(): void
    {
        // Arrange & Act
        $book1 = new Book(new BookTitle('Book 1'), new Price(10.0));
        $book2 = new Book(new BookTitle('Book 2'), new Price(20.0));

        // Assert
        $this->assertNotSame($book1->getId(), $book2->getId());
    }
}
```

## 🎯 Exercice Pratique 2 : Author

Créez :
1. Un Value Object `AuthorName` (nom d'auteur)
2. Un Value Object `Email` (email de l'auteur)
3. Une entité `Author` qui utilise ces Value Objects

<details>
<summary>💡 Solution</summary>

```php
<?php
// src/Domain/ValueObject/AuthorName.php

namespace App\Domain\ValueObject;

final readonly class AuthorName
{
    public function __construct(public string $value)
    {
        if (empty(trim($value))) {
            throw new \InvalidArgumentException('Author name cannot be empty');
        }
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

```php
<?php
// src/Domain/ValueObject/Email.php

namespace App\Domain\ValueObject;

final readonly class Email
{
    public function __construct(public string $value)
    {
        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Invalid email format');
        }
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

```php
<?php
// src/Domain/Model/Author.php

namespace App\Domain\Model;

use App\Domain\ValueObject\AuthorName;
use App\Domain\ValueObject\Email;

class Author
{
    private string $id;

    public function __construct(
        private AuthorName $name,
        private Email $email
    ) {
        $this->id = uniqid('author_', true);
    }

    public function updateEmail(Email $newEmail): void
    {
        $this->email = $newEmail;
    }

    // Getters
    public function getId(): string { return $this->id; }
    public function getName(): AuthorName { return $this->name; }
    public function getEmail(): Email { return $this->email; }
}
```
</details>

## ✅ Checkpoint - Validation des Acquis

Avant de passer au guide suivant, vérifiez que vous savez :

- [ ] **Créer** un Value Object avec validation
- [ ] **Écrire** des tests unitaires pour vos Value Objects
- [ ] **Comprendre** l'immutabilité (readonly)
- [ ] **Créer** une entité simple avec des Value Objects
- [ ] **Tester** le comportement d'une entité

## 🎯 Mini-Projet : Bibliothèque Simple

Créez un mini-système avec :

1. **Value Objects** :
   - `ISBN` (format : XXX-X-XXXX-XXXX-X)
   - `PublicationYear` (entre 1000 et année actuelle)

2. **Entité** :
   - `Book` avec titre, ISBN, prix, année de publication

3. **Tests** pour tout valider

**Temps estimé** : 30 minutes

## 🚀 Prochaine Étape

Une fois ce guide maîtrisé, passez à **[02-entity-basics.md](02-entity-basics.md)** pour apprendre :
- Les entités avec cycle de vie
- Les méthodes métier
- Les invariants simples

---

**Bravo !** 🎉 Vous avez créé vos premiers Value Objects et entités. C'est la base de tout système DDD !
