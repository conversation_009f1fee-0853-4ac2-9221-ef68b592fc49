# 🟢 API Basics - Interface REST Simple

## 🎯 Objectifs de ce Guide

À la fin de ce chapitre, vous saurez :
- ✅ Créer des contrôleurs REST simples
- ✅ Exposer vos cas d'usage via HTTP
- ✅ Gérer les erreurs et validations
- ✅ Tester votre API

**Temps estimé** : 45 minutes

## 📚 API REST et DDD

### Principe de Séparation

- **API** = Interface utilisateur (HTTP)
- **Application** = Cas d'usage métier
- **Domain** = Règles métier

```
HTTP Request → Controller → Handler → Domain
HTTP Response ← Controller ← Handler ← Domain
```

## 🛠️ Votre Premier Contrôleur

### Étape 1 : Contrôleur pour les Livres

```php
<?php
// src/Infrastructure/Controller/BookController.php

namespace App\Infrastructure\Controller;

use App\Application\Command\BorrowBookCommand;
use App\Application\Command\ReturnBookCommand;
use App\Application\Handler\BorrowBookHandler;
use App\Application\Handler\ReturnBookHandler;
use App\Application\Handler\FindAvailableBooksHandler;
use App\Application\Query\FindAvailableBooksQuery;
use App\Domain\ValueObject\BookId;
use App\Domain\ValueObject\MemberId;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/books', name: 'api_books_')]
class BookController extends AbstractController
{
    public function __construct(
        private FindAvailableBooksHandler $findAvailableBooksHandler,
        private BorrowBookHandler $borrowBookHandler,
        private ReturnBookHandler $returnBookHandler
    ) {}

    #[Route('', name: 'list', methods: ['GET'])]
    public function list(Request $request): JsonResponse
    {
        try {
            $query = new FindAvailableBooksQuery(
                titleSearch: $request->query->get('search'),
                maxPrice: $request->query->get('maxPrice') 
                    ? (float) $request->query->get('maxPrice') 
                    : null
            );

            $books = $this->findAvailableBooksHandler->handle($query);

            return $this->json([
                'books' => array_map(fn($book) => [
                    'id' => $book->id,
                    'title' => $book->title,
                    'price' => $book->price,
                    'currency' => $book->currency,
                    'isAvailable' => $book->isAvailable
                ], $books)
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to fetch books',
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/{bookId}/borrow', name: 'borrow', methods: ['POST'])]
    public function borrow(string $bookId, Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            
            if (!isset($data['memberId'])) {
                return $this->json([
                    'error' => 'Member ID is required'
                ], Response::HTTP_BAD_REQUEST);
            }

            $command = new BorrowBookCommand(
                new BookId($bookId),
                new MemberId($data['memberId'])
            );

            $this->borrowBookHandler->handle($command);

            return $this->json([
                'message' => 'Book borrowed successfully'
            ], Response::HTTP_OK);

        } catch (\DomainException $e) {
            return $this->json([
                'error' => 'Business rule violation',
                'message' => $e->getMessage()
            ], Response::HTTP_CONFLICT);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to borrow book',
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/{bookId}/return', name: 'return', methods: ['POST'])]
    public function return(string $bookId): JsonResponse
    {
        try {
            $command = new ReturnBookCommand(new BookId($bookId));
            $this->returnBookHandler->handle($command);

            return $this->json([
                'message' => 'Book returned successfully'
            ], Response::HTTP_OK);

        } catch (\DomainException $e) {
            return $this->json([
                'error' => 'Business rule violation',
                'message' => $e->getMessage()
            ], Response::HTTP_CONFLICT);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to return book',
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
```

### Étape 2 : Configuration des Routes

```yaml
# config/routes.yaml
api:
    resource: '../src/Infrastructure/Controller'
    type: attribute
    prefix: /api
```

## 🧪 Tests de l'API

### Étape 1 : Test du Contrôleur

```php
<?php
// tests/Integration/Infrastructure/Controller/BookControllerTest.php

namespace App\Tests\Integration\Infrastructure\Controller;

use App\Infrastructure\Repository\InMemoryBookRepository;
use App\Infrastructure\Repository\InMemoryMemberRepository;
use App\Tests\Support\BookFactory;
use App\Tests\Support\MemberFactory;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class BookControllerTest extends WebTestCase
{
    private InMemoryBookRepository $bookRepository;
    private InMemoryMemberRepository $memberRepository;

    protected function setUp(): void
    {
        self::bootKernel();
        
        $this->bookRepository = self::getContainer()
            ->get(InMemoryBookRepository::class);
        $this->memberRepository = self::getContainer()
            ->get(InMemoryMemberRepository::class);

        // Nettoyer les repositories
        $this->bookRepository->clear();
        $this->memberRepository->clear();
    }

    public function testListAvailableBooks(): void
    {
        // Arrange
        $book1 = BookFactory::create('Book 1', 10.0);
        $book2 = BookFactory::create('Book 2', 20.0);
        $borrowedBook = BookFactory::createBorrowed('Borrowed Book');

        $this->bookRepository->save($book1);
        $this->bookRepository->save($book2);
        $this->bookRepository->save($borrowedBook);

        // Act
        $client = static::createClient();
        $client->request('GET', '/api/books');

        // Assert
        $this->assertResponseIsSuccessful();
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $data = json_decode($client->getResponse()->getContent(), true);
        $this->assertArrayHasKey('books', $data);
        $this->assertCount(2, $data['books']); // Seulement les livres disponibles
    }

    public function testListBooksWithSearch(): void
    {
        // Arrange
        $cleanBook = BookFactory::create('Clean Code', 30.0);
        $dirtyBook = BookFactory::create('Dirty Code', 25.0);

        $this->bookRepository->save($cleanBook);
        $this->bookRepository->save($dirtyBook);

        // Act
        $client = static::createClient();
        $client->request('GET', '/api/books?search=Clean');

        // Assert
        $this->assertResponseIsSuccessful();

        $data = json_decode($client->getResponse()->getContent(), true);
        $this->assertCount(1, $data['books']);
        $this->assertSame('Clean Code', $data['books'][0]['title']);
    }

    public function testBorrowBookSuccessfully(): void
    {
        // Arrange
        $book = BookFactory::create('Test Book');
        $member = MemberFactory::create();

        $this->bookRepository->save($book);
        $this->memberRepository->save($member);

        // Act
        $client = static::createClient();
        $client->request('POST', '/api/books/' . $book->getId()->value . '/borrow', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], json_encode([
            'memberId' => $member->getId()->value
        ]));

        // Assert
        $this->assertResponseIsSuccessful();

        $data = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame('Book borrowed successfully', $data['message']);

        // Vérifier que le livre est emprunté
        $updatedBook = $this->bookRepository->findById($book->getId());
        $this->assertTrue($updatedBook->isBorrowed());
    }

    public function testBorrowBookWithoutMemberId(): void
    {
        // Arrange
        $book = BookFactory::create('Test Book');
        $this->bookRepository->save($book);

        // Act
        $client = static::createClient();
        $client->request('POST', '/api/books/' . $book->getId()->value . '/borrow', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], json_encode([]));

        // Assert
        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $data = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame('Member ID is required', $data['error']);
    }

    public function testBorrowNonExistentBook(): void
    {
        // Arrange
        $member = MemberFactory::create();
        $this->memberRepository->save($member);

        // Act
        $client = static::createClient();
        $client->request('POST', '/api/books/non-existent-id/borrow', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], json_encode([
            'memberId' => $member->getId()->value
        ]));

        // Assert
        $this->assertResponseStatusCodeSame(Response::HTTP_CONFLICT);

        $data = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame('Business rule violation', $data['error']);
        $this->assertSame('Book not found', $data['message']);
    }

    public function testReturnBookSuccessfully(): void
    {
        // Arrange
        $book = BookFactory::createBorrowed('Test Book');
        $this->bookRepository->save($book);

        // Act
        $client = static::createClient();
        $client->request('POST', '/api/books/' . $book->getId()->value . '/return');

        // Assert
        $this->assertResponseIsSuccessful();

        $data = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame('Book returned successfully', $data['message']);

        // Vérifier que le livre est disponible
        $updatedBook = $this->bookRepository->findById($book->getId());
        $this->assertTrue($updatedBook->isAvailable());
    }
}
```

## 🎯 Exercice Pratique 1 : Contrôleur Member

Créez un `MemberController` avec :
- `GET /api/members` - Liste des membres actifs
- `POST /api/members` - Créer un nouveau membre
- `PUT /api/members/{id}/deactivate` - Désactiver un membre

<details>
<summary>💡 Structure de base</summary>

```php
<?php
// src/Infrastructure/Controller/MemberController.php

namespace App\Infrastructure\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/members', name: 'api_members_')]
class MemberController extends AbstractController
{
    #[Route('', name: 'list', methods: ['GET'])]
    public function list(): JsonResponse
    {
        // TODO: Implémenter
    }

    #[Route('', name: 'create', methods: ['POST'])]
    public function create(Request $request): JsonResponse
    {
        // TODO: Implémenter
    }

    #[Route('/{memberId}/deactivate', name: 'deactivate', methods: ['PUT'])]
    public function deactivate(string $memberId): JsonResponse
    {
        // TODO: Implémenter
    }
}
```
</details>

## 🔧 Amélioration : Validation des Données

### Étape 1 : DTO de Requête

```php
<?php
// src/Infrastructure/DTO/BorrowBookRequest.php

namespace App\Infrastructure\DTO;

use Symfony\Component\Validator\Constraints as Assert;

class BorrowBookRequest
{
    #[Assert\NotBlank(message: 'Member ID is required')]
    #[Assert\Type(type: 'string')]
    public string $memberId;

    public static function fromArray(array $data): self
    {
        $request = new self();
        $request->memberId = $data['memberId'] ?? '';
        return $request;
    }
}
```

### Étape 2 : Validation dans le Contrôleur

```php
<?php
// Modifier la méthode borrow du BookController

#[Route('/{bookId}/borrow', name: 'borrow', methods: ['POST'])]
public function borrow(string $bookId, Request $request, ValidatorInterface $validator): JsonResponse
{
    try {
        $data = json_decode($request->getContent(), true);
        $borrowRequest = BorrowBookRequest::fromArray($data);

        // Valider les données
        $errors = $validator->validate($borrowRequest);
        if (count($errors) > 0) {
            $errorMessages = [];
            foreach ($errors as $error) {
                $errorMessages[] = $error->getMessage();
            }
            
            return $this->json([
                'error' => 'Validation failed',
                'messages' => $errorMessages
            ], Response::HTTP_BAD_REQUEST);
        }

        $command = new BorrowBookCommand(
            new BookId($bookId),
            new MemberId($borrowRequest->memberId)
        );

        $this->borrowBookHandler->handle($command);

        return $this->json([
            'message' => 'Book borrowed successfully'
        ], Response::HTTP_OK);

    } catch (\DomainException $e) {
        return $this->json([
            'error' => 'Business rule violation',
            'message' => $e->getMessage()
        ], Response::HTTP_CONFLICT);
    }
}
```

## 📊 Documentation API Simple

### Étape 1 : Annotations OpenAPI

```php
<?php
// Ajouter à BookController

use OpenApi\Attributes as OA;

#[OA\Get(
    path: '/api/books',
    summary: 'List available books',
    parameters: [
        new OA\Parameter(
            name: 'search',
            in: 'query',
            description: 'Search in book titles',
            schema: new OA\Schema(type: 'string')
        ),
        new OA\Parameter(
            name: 'maxPrice',
            in: 'query',
            description: 'Maximum price filter',
            schema: new OA\Schema(type: 'number')
        )
    ],
    responses: [
        new OA\Response(
            response: 200,
            description: 'List of available books',
            content: new OA\JsonContent(
                properties: [
                    'books' => new OA\Property(
                        type: 'array',
                        items: new OA\Items(
                            properties: [
                                'id' => new OA\Property(type: 'string'),
                                'title' => new OA\Property(type: 'string'),
                                'price' => new OA\Property(type: 'number'),
                                'currency' => new OA\Property(type: 'string'),
                                'isAvailable' => new OA\Property(type: 'boolean')
                            ]
                        )
                    )
                ]
            )
        )
    ]
)]
#[Route('', name: 'list', methods: ['GET'])]
public function list(Request $request): JsonResponse
{
    // ... implémentation
}
```

## ✅ Checkpoint - Validation des Acquis

Avant de passer au guide suivant, vérifiez que vous savez :

- [ ] **Créer** des contrôleurs REST
- [ ] **Exposer** vos cas d'usage via HTTP
- [ ] **Gérer** les erreurs et codes de statut
- [ ] **Valider** les données d'entrée
- [ ] **Tester** votre API

## 🎯 Mini-Projet : API Complète

Créez une API REST complète avec :

1. **Endpoints** :
   - CRUD pour les livres
   - CRUD pour les membres
   - Actions métier (emprunter, retourner)

2. **Validation** des données d'entrée

3. **Gestion d'erreurs** appropriée

4. **Tests** d'intégration complets

5. **Documentation** OpenAPI

**Temps estimé** : 90 minutes

## 🚀 Prochaine Étape

Une fois ce guide maîtrisé, passez à **[07-tests-basics.md](07-tests-basics.md)** pour apprendre :
- Stratégies de tests complètes
- Tests unitaires, d'intégration et end-to-end
- Mocks et fixtures

---

**Excellent !** 🎉 Vous savez maintenant exposer votre logique métier via une API REST propre !
