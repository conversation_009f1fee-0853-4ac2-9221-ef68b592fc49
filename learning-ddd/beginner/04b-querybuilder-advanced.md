# 🟢 QueryBuilder Avancé - Requêtes Flexibles

## 🎯 Objectifs de ce Guide

À la fin de ce chapitre, vous saurez :
- ✅ Implémenter le pattern QueryBuilder
- ✅ Créer des requêtes complexes et flexibles
- ✅ Gérer la pagination et le tri
- ✅ Optimiser les performances des requêtes
- ✅ Tester les QueryBuilders

**Temps estimé** : 45 minutes  
**Prérequis** : Avoir lu le guide 04-application-basics.md

## 📚 Pourquoi un QueryBuilder ?

### Problème Sans fQueryBuilder

```php
// Méthodes multiples dans le repository
public function findByTitle(string $title): array;
public function findByAuthor(string $author): array;
public function findByTitleAndAuthor(string $title, string $author): array;
public function findAvailableByTitle(string $title): array;
// ... explosion combinatoire !
```

### Solution Avec QueryBuilder

```php
// Une seule méthode flexible
$books = (new BookQueryBuilder())
    ->withTitle('Clean')
    ->withAuthor('Martin')
    ->onlyAvailable()
    ->sortBy('price')
    ->limit(10)
    ->execute();
```

## 🛠️ QueryBuilder Complet

### Étape 1 : QueryBuilder avec Validation

```php
<?php
// src/Application/Query/Builder/BookQueryBuilder.php

namespace App\Application\Query\Builder;

use App\Application\Query\FindBooksQuery;

class BookQueryBuilder
{
    private array $criteria = [];
    private array $sorting = [];
    private ?int $limit = null;
    private int $offset = 0;

    // === CRITÈRES DE RECHERCHE ===

    public function withTitle(string $title): self
    {
        if (empty(trim($title))) {
            throw new \InvalidArgumentException('Title cannot be empty');
        }
        
        $this->criteria['title'] = trim($title);
        return $this;
    }

    public function withAuthor(string $author): self
    {
        if (empty(trim($author))) {
            throw new \InvalidArgumentException('Author cannot be empty');
        }
        
        $this->criteria['author'] = trim($author);
        return $this;
    }

    public function withIsbn(string $isbn): self
    {
        if (!preg_match('/^\d{3}-\d{1}-\d{4}-\d{4}-\d{1}$/', $isbn)) {
            throw new \InvalidArgumentException('Invalid ISBN format');
        }
        
        $this->criteria['isbn'] = $isbn;
        return $this;
    }

    public function onlyAvailable(): self
    {
        $this->criteria['available'] = true;
        return $this;
    }

    public function onlyBorrowed(): self
    {
        $this->criteria['available'] = false;
        return $this;
    }

    public function withPriceRange(float $min, float $max): self
    {
        if ($min < 0 || $max < 0) {
            throw new \InvalidArgumentException('Prices must be positive');
        }
        
        if ($min > $max) {
            throw new \InvalidArgumentException('Min price cannot be greater than max price');
        }
        
        $this->criteria['minPrice'] = $min;
        $this->criteria['maxPrice'] = $max;
        return $this;
    }

    public function withMinPrice(float $price): self
    {
        if ($price < 0) {
            throw new \InvalidArgumentException('Price must be positive');
        }
        
        $this->criteria['minPrice'] = $price;
        return $this;
    }

    public function withMaxPrice(float $price): self
    {
        if ($price < 0) {
            throw new \InvalidArgumentException('Price must be positive');
        }
        
        $this->criteria['maxPrice'] = $price;
        return $this;
    }

    // === TRI ===

    public function sortBy(string $field, string $direction = 'ASC'): self
    {
        $allowedFields = ['title', 'author', 'price', 'createdAt'];
        $allowedDirections = ['ASC', 'DESC'];
        
        if (!in_array($field, $allowedFields)) {
            throw new \InvalidArgumentException("Invalid sort field: {$field}");
        }
        
        $direction = strtoupper($direction);
        if (!in_array($direction, $allowedDirections)) {
            throw new \InvalidArgumentException("Invalid sort direction: {$direction}");
        }
        
        $this->sorting = ['field' => $field, 'direction' => $direction];
        return $this;
    }

    public function sortByTitle(string $direction = 'ASC'): self
    {
        return $this->sortBy('title', $direction);
    }

    public function sortByPrice(string $direction = 'ASC'): self
    {
        return $this->sortBy('price', $direction);
    }

    public function sortByNewest(): self
    {
        return $this->sortBy('createdAt', 'DESC');
    }

    public function sortByOldest(): self
    {
        return $this->sortBy('createdAt', 'ASC');
    }

    // === PAGINATION ===

    public function limit(int $limit): self
    {
        if ($limit <= 0) {
            throw new \InvalidArgumentException('Limit must be positive');
        }
        
        if ($limit > 100) {
            throw new \InvalidArgumentException('Limit cannot exceed 100');
        }
        
        $this->limit = $limit;
        return $this;
    }

    public function offset(int $offset): self
    {
        if ($offset < 0) {
            throw new \InvalidArgumentException('Offset cannot be negative');
        }
        
        $this->offset = $offset;
        return $this;
    }

    public function page(int $page, int $perPage = 20): self
    {
        if ($page < 1) {
            throw new \InvalidArgumentException('Page must be >= 1');
        }
        
        $this->limit($perPage);
        $this->offset(($page - 1) * $perPage);
        return $this;
    }

    // === CONSTRUCTION ===

    public function build(): FindBooksQuery
    {
        return new FindBooksQuery(
            titleSearch: $this->criteria['title'] ?? null,
            authorSearch: $this->criteria['author'] ?? null,
            isbn: $this->criteria['isbn'] ?? null,
            availableOnly: $this->criteria['available'] ?? null,
            minPrice: $this->criteria['minPrice'] ?? null,
            maxPrice: $this->criteria['maxPrice'] ?? null,
            sortBy: $this->sorting['field'] ?? null,
            sortDirection: $this->sorting['direction'] ?? 'ASC',
            limit: $this->limit,
            offset: $this->offset
        );
    }

    public function getCriteria(): array
    {
        return $this->criteria;
    }

    public function getSorting(): array
    {
        return $this->sorting;
    }

    public function reset(): self
    {
        $this->criteria = [];
        $this->sorting = [];
        $this->limit = null;
        $this->offset = 0;
        return $this;
    }
}
```

### Étape 2 : Query Enrichie

```php
<?php
// src/Application/Query/FindBooksQuery.php (version complète)

namespace App\Application\Query;

final readonly class FindBooksQuery
{
    public function __construct(
        public ?string $titleSearch = null,
        public ?string $authorSearch = null,
        public ?string $isbn = null,
        public ?bool $availableOnly = null,
        public ?float $minPrice = null,
        public ?float $maxPrice = null,
        public ?string $sortBy = null,
        public string $sortDirection = 'ASC',
        public ?int $limit = null,
        public int $offset = 0
    ) {}

    public function hasFilters(): bool
    {
        return $this->titleSearch !== null
            || $this->authorSearch !== null
            || $this->isbn !== null
            || $this->availableOnly !== null
            || $this->minPrice !== null
            || $this->maxPrice !== null;
    }

    public function hasPagination(): bool
    {
        return $this->limit !== null || $this->offset > 0;
    }

    public function hasSorting(): bool
    {
        return $this->sortBy !== null;
    }
}
```

## 🧪 Tests Complets du QueryBuilder

### Test de Validation

```php
<?php
// tests/Unit/Application/Query/Builder/BookQueryBuilderTest.php

namespace App\Tests\Unit\Application\Query\Builder;

use App\Application\Query\Builder\BookQueryBuilder;
use PHPUnit\Framework\TestCase;

class BookQueryBuilderTest extends TestCase
{
    private BookQueryBuilder $builder;

    protected function setUp(): void
    {
        $this->builder = new BookQueryBuilder();
    }

    public function testBuildEmptyQuery(): void
    {
        $query = $this->builder->build();

        $this->assertNull($query->titleSearch);
        $this->assertNull($query->authorSearch);
        $this->assertNull($query->availableOnly);
        $this->assertSame('ASC', $query->sortDirection);
        $this->assertSame(0, $query->offset);
    }

    public function testWithTitle(): void
    {
        $query = $this->builder
            ->withTitle('Clean Code')
            ->build();

        $this->assertSame('Clean Code', $query->titleSearch);
    }

    public function testWithTitleTrimsWhitespace(): void
    {
        $query = $this->builder
            ->withTitle('  Clean Code  ')
            ->build();

        $this->assertSame('Clean Code', $query->titleSearch);
    }

    public function testWithEmptyTitleThrowsException(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Title cannot be empty');

        $this->builder->withTitle('');
    }

    public function testWithPriceRange(): void
    {
        $query = $this->builder
            ->withPriceRange(10.0, 50.0)
            ->build();

        $this->assertSame(10.0, $query->minPrice);
        $this->assertSame(50.0, $query->maxPrice);
    }

    public function testWithInvalidPriceRangeThrowsException(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Min price cannot be greater than max price');

        $this->builder->withPriceRange(50.0, 10.0);
    }

    public function testSortBy(): void
    {
        $query = $this->builder
            ->sortBy('price', 'DESC')
            ->build();

        $this->assertSame('price', $query->sortBy);
        $this->assertSame('DESC', $query->sortDirection);
    }

    public function testSortByInvalidFieldThrowsException(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid sort field: invalid');

        $this->builder->sortBy('invalid');
    }

    public function testPagination(): void
    {
        $query = $this->builder
            ->page(2, 10)
            ->build();

        $this->assertSame(10, $query->limit);
        $this->assertSame(10, $query->offset); // (2-1) * 10
    }

    public function testLimitTooHighThrowsException(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Limit cannot exceed 100');

        $this->builder->limit(101);
    }

    public function testFluentInterface(): void
    {
        $result = $this->builder
            ->withTitle('Test')
            ->withAuthor('Author')
            ->onlyAvailable()
            ->sortByPrice('DESC')
            ->limit(5);

        $this->assertSame($this->builder, $result);
    }

    public function testComplexQuery(): void
    {
        $query = $this->builder
            ->withTitle('Clean')
            ->withAuthor('Martin')
            ->onlyAvailable()
            ->withPriceRange(20.0, 40.0)
            ->sortByPrice('DESC')
            ->page(2, 10)
            ->build();

        $this->assertSame('Clean', $query->titleSearch);
        $this->assertSame('Martin', $query->authorSearch);
        $this->assertTrue($query->availableOnly);
        $this->assertSame(20.0, $query->minPrice);
        $this->assertSame(40.0, $query->maxPrice);
        $this->assertSame('price', $query->sortBy);
        $this->assertSame('DESC', $query->sortDirection);
        $this->assertSame(10, $query->limit);
        $this->assertSame(10, $query->offset);
    }

    public function testReset(): void
    {
        $this->builder
            ->withTitle('Test')
            ->withAuthor('Author')
            ->limit(10)
            ->reset();

        $query = $this->builder->build();

        $this->assertNull($query->titleSearch);
        $this->assertNull($query->authorSearch);
        $this->assertNull($query->limit);
    }
}
```

## 🗃️ Intégration avec Doctrine QueryBuilder

### Repository avec Doctrine QueryBuilder

```php
<?php
// src/Infrastructure/Repository/DoctrineBookRepository.php

namespace App\Infrastructure\Repository;

use App\Application\Query\FindBooksQuery;
use App\Domain\Model\Book;
use App\Domain\Repository\BookRepositoryInterface;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\fQueryBuilderfQueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

class DoctrineBookRepository extends ServiceEntityRepository implements BookRepositoryInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Book::class);
    }

    public function findByQuery(FindBooksQuery $query): array
    {
        $qb = $this->createQueryBuilder('b');

        $this->applyFilters($qb, $query);
        $this->applySorting($qb, $query);
        $this->applyPagination($qb, $query);

        return $qb->getQuery()->getResult();
    }

    private function applyFilters(QueryBuilder $qb, FindBooksQuery $query): void
    {
        // Filtre par titre
        if ($query->titleSearch) {
            $qb->andWhere('LOWER(b.title) LIKE LOWER(:title)')
               ->setParameter('title', '%' . $query->titleSearch . '%');
        }

        // Filtre par auteur
        if ($query->authorSearch) {
            $qb->andWhere('LOWER(b.author) LIKE LOWER(:author)')
               ->setParameter('author', '%' . $query->authorSearch . '%');
        }

        // Filtre par ISBN
        if ($query->isbn) {
            $qb->andWhere('b.isbn = :isbn')
               ->setParameter('isbn', $query->isbn);
        }

        // Filtre par disponibilité
        if ($query->availableOnly !== null) {
            $qb->andWhere('b.isAvailable = :available')
               ->setParameter('available', $query->availableOnly);
        }

        // Filtre par prix minimum
        if ($query->minPrice !== null) {
            $qb->andWhere('b.price >= :minPrice')
               ->setParameter('minPrice', $query->minPrice);
        }

        // Filtre par prix maximum
        if ($query->maxPrice !== null) {
            $qb->andWhere('b.price <= :maxPrice')
               ->setParameter('maxPrice', $query->maxPrice);
        }
    }

    private function applySorting(QueryBuilder $qb, FindBooksQuery $query): void
    {
        if ($query->sortBy) {
            $field = match ($query->sortBy) {
                'title' => 'b.title',
                'author' => 'b.author',
                'price' => 'b.price',
                'createdAt' => 'b.createdAt',
                default => 'b.title'
            };

            $qb->orderBy($field, $query->sortDirection);
        } else {
            // Tri par défaut
            $qb->orderBy('b.title', 'ASC');
        }
    }

    private function applyPagination(QueryBuilder $qb, FindBooksQuery $query): void
    {
        if ($query->limit) {
            $qb->setMaxResults($query->limit);
        }

        if ($query->offset > 0) {
            $qb->setFirstResult($query->offset);
        }
    }

    public function countByQuery(FindBooksQuery $query): int
    {
        $qb = $this->createQueryBuilder('b')
                   ->select('COUNT(b.id)');

        $this->applyFilters($qb, $query);

        return (int) $qb->getQuery()->getSingleScalarResult();
    }
}
```

### QueryBuilder Avancé avec Jointures

```php
<?php
// src/Infrastructure/Repository/DoctrineBookRepositoryAdvanced.php

namespace App\Infrastructure\Repository;

use Doctrine\ORM\QueryBuilder;

class DoctrineBookRepositoryAdvanced extends DoctrineBookRepository
{
    public function findBooksWithAuthorDetails(FindBooksQuery $query): array
    {
        $qb = $this->createQueryBuilder('b')
                   ->leftJoin('b.author', 'a')
                   ->addSelect('a');

        $this->applyAdvancedFilters($qb, $query);
        $this->applySorting($qb, $query);
        $this->applyPagination($qb, $query);

        return $qb->getQuery()->getResult();
    }

    private function applyAdvancedFilters(QueryBuilder $qb, FindBooksQuery $query): void
    {
        // Filtres de base
        $this->applyFilters($qb, $query);

        // Filtre par nationalité de l'auteur
        if ($query->authorNationality) {
            $qb->andWhere('a.nationality = :nationality')
               ->setParameter('nationality', $query->authorNationality);
        }

        // Filtre par auteur actif
        if ($query->activeAuthorsOnly) {
            $qb->andWhere('a.isActive = true');
        }

        // Recherche full-text (PostgreSQL/MySQL)
        if ($query->fullTextSearch) {
            $qb->andWhere('MATCH(b.title, b.description) AGAINST(:search IN BOOLEAN MODE)')
               ->setParameter('search', $query->fullTextSearch);
        }
    }

    public function findPopularBooks(int $limit = 10): array
    {
        return $this->createQueryBuilder('b')
            ->leftJoin('b.loans', 'l')
            ->groupBy('b.id')
            ->orderBy('COUNT(l.id)', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function findBooksByPriceRange(float $min, float $max): array
    {
        return $this->createQueryBuilder('b')
            ->where('b.price BETWEEN :min AND :max')
            ->setParameter('min', $min)
            ->setParameter('max', $max)
            ->orderBy('b.price', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function getBookStatistics(): array
    {
        $qb = $this->createQueryBuilder('b');

        return $qb->select([
                'COUNT(b.id) as totalBooks',
                'AVG(b.price) as averagePrice',
                'MIN(b.price) as minPrice',
                'MAX(b.price) as maxPrice',
                'SUM(CASE WHEN b.isAvailable = true THEN 1 ELSE 0 END) as availableBooks',
                'SUM(CASE WHEN b.isAvailable = false THEN 1 ELSE 0 END) as borrowedBooks'
            ])
            ->getQuery()
            ->getSingleResult();
    }
}
```

### Service avec Doctrine QueryBuilder

```php
<?php
// src/Application/Service/BookSearchService.php

namespace App\Application\Service;

use App\Application\Query\Builder\BookQueryBuilder;
use App\Application\Query\FindBooksQuery;
use App\Infrastructure\Repository\DoctrineBookRepository;

class BookSearchService
{
    public function __construct(
        private DoctrineBookRepository $bookRepository
    ) {}

    public function searchWithPagination(array $criteria, int $page = 1, int $perPage = 20): array
    {
        $builder = new BookQueryBuilder();

        // Construire la query depuis les critères
        if (isset($criteria['title'])) {
            $builder->withTitle($criteria['title']);
        }

        if (isset($criteria['author'])) {
            $builder->withAuthor($criteria['author']);
        }

        if (isset($criteria['available']) && $criteria['available']) {
            $builder->onlyAvailable();
        }

        if (isset($criteria['minPrice'], $criteria['maxPrice'])) {
            $builder->withPriceRange($criteria['minPrice'], $criteria['maxPrice']);
        }

        if (isset($criteria['sortBy'])) {
            $sortDir = $criteria['sortDir'] ?? 'ASC';
            $builder->sortBy($criteria['sortBy'], $sortDir);
        }

        $builder->page($page, $perPage);
        $query = $builder->build();

        // Exécuter avec Doctrine
        $books = $this->bookRepository->findByQuery($query);
        $total = $this->bookRepository->countByQuery($query);

        return [
            'books' => $books,
            'pagination' => [
                'page' => $page,
                'perPage' => $perPage,
                'total' => $total,
                'totalPages' => ceil($total / $perPage),
                'hasNext' => $page < ceil($total / $perPage),
                'hasPrev' => $page > 1,
            ]
        ];
    }
}
```

## 🎯 Utilisation Pratique

### Dans un Contrôleur

```php
<?php
// src/Infrastructure/Controller/BookController.php

use App\Application\Service\BookSearchService;
use Doctrine\ORM\QueryBuilder;

public function search(Request $request, BookSearchService $searchService): JsonResponse
{
    try {
        $criteria = [
            'title' => $request->query->get('title'),
            'author' => $request->query->get('author'),
            'available' => $request->query->getBoolean('available'),
            'minPrice' => $request->query->get('minPrice') ? (float) $request->query->get('minPrice') : null,
            'maxPrice' => $request->query->get('maxPrice') ? (float) $request->query->get('maxPrice') : null,
            'sortBy' => $request->query->get('sortBy'),
            'sortDir' => $request->query->get('sortDir', 'ASC'),
        ];

        $page = (int) $request->query->get('page', 1);
        $perPage = (int) $request->query->get('perPage', 20);

        $result = $searchService->searchWithPagination($criteria, $page, $perPage);

        return $this->json([
            'books' => array_map(fn($book) => [
                'id' => $book->getId(),
                'title' => $book->getTitle()->value,
                'author' => $book->getAuthor()->name,
                'price' => $book->getPrice()->amount,
                'isAvailable' => $book->isAvailable(),
            ], $result['books']),
            'pagination' => $result['pagination']
        ]);

    } catch (\InvalidArgumentException $e) {
        return $this->json([
            'error' => 'Invalid query parameters',
            'message' => $e->getMessage()
        ], Response::HTTP_BAD_REQUEST);
    }
}
```

### Test avec Doctrine QueryBuilder

```php
<?php
// tests/Integration/Repository/DoctrineBookRepositoryTest.php

namespace App\Tests\Integration\Repository;

use App\Application\Query\Builder\BookQueryBuilder;
use App\Infrastructure\Repository\DoctrineBookRepository;
use App\Tests\Support\BookFactory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class DoctrineBookRepositoryTest extends KernelTestCase
{
    private DoctrineBookRepository $repository;
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->repository = self::getContainer()->get(DoctrineBookRepository::class);

        $this->cleanDatabase();
    }

    public function testFindByQueryWithFilters(): void
    {
        // Arrange
        $books = [
            BookFactory::create('Clean Code', 'Robert Martin', 29.99),
            BookFactory::create('Clean Architecture', 'Robert Martin', 35.99),
            BookFactory::create('Refactoring', 'Martin Fowler', 39.99),
        ];

        foreach ($books as $book) {
            $this->entityManager->persist($book);
        }
        $this->entityManager->flush();

        // Act
        $query = (new BookQueryBuilder())
            ->withTitle('Clean')
            ->withAuthor('Martin')
            ->sortByPrice('ASC')
            ->build();

        $result = $this->repository->findByQuery($query);

        // Assert
        $this->assertCount(2, $result);
        $this->assertSame('Clean Code', $result[0]->getTitle()->value);
        $this->assertSame('Clean Architecture', $result[1]->getTitle()->value);
    }

    public function testCountByQuery(): void
    {
        // Arrange
        $books = [
            BookFactory::create('Book 1', 'Author 1', 10.0),
            BookFactory::create('Book 2', 'Author 2', 20.0),
            BookFactory::create('Book 3', 'Author 3', 30.0),
        ];

        foreach ($books as $book) {
            $this->entityManager->persist($book);
        }
        $this->entityManager->flush();

        // Act
        $query = (new BookQueryBuilder())
            ->withMaxPrice(25.0)
            ->build();

        $count = $this->repository->countByQuery($query);

        // Assert
        $this->assertSame(2, $count);
    }

    private function cleanDatabase(): void
    {
        $this->entityManager->createQuery('DELETE FROM App\Domain\Model\Book')->execute();
        $this->entityManager->clear();
    }
}
```

### Exemples d'Utilisation avec Doctrine

```php
// Recherche simple avec Doctrine
$query = (new BookQueryBuilder())
    ->withTitle('Clean')
    ->build();
$books = $repository->findByQuery($query);

// Recherche avec tri et pagination
$query = (new BookQueryBuilder())
    ->withAuthor('Martin')
    ->sortByPrice('DESC')
    ->page(1, 10)
    ->build();
$books = $repository->findByQuery($query);
$total = $repository->countByQuery($query);

// Recherche complexe avec jointures
$query = (new BookQueryBuilder())
    ->withTitle('Architecture')
    ->withPriceRange(20.0, 50.0)
    ->onlyAvailable()
    ->sortBy('title')
    ->limit(5)
    ->build();
$books = $repository->findBooksWithAuthorDetails($query);

// Statistiques avec Doctrine QueryBuilder
$stats = $repository->getBookStatistics();
// Retourne: ['totalBooks' => 150, 'averagePrice' => 25.50, ...]
```

## 🔧 Optimisations Doctrine

### Index de Base de Données

```sql
-- Optimiser les recherches fréquentes
CREATE INDEX idx_books_title ON books(title);
CREATE INDEX idx_books_author ON books(author);
CREATE INDEX idx_books_price ON books(price);
CREATE INDEX idx_books_available ON books(is_available);
CREATE INDEX idx_books_title_author ON books(title, author);
```

### Cache des Requêtes

```php
<?php
// src/Infrastructure/Repository/CachedBookRepository.php

namespace App\Infrastructure\Repository;

use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;

class CachedBookRepository extends DoctrineBookRepository
{
    public function __construct(
        ManagerRegistry $registry,
        private CacheInterface $cache
    ) {
        parent::__construct($registry);
    }

    public function findByQuery(FindBooksQuery $query): array
    {
        // Cache seulement les requêtes sans pagination
        if (!$query->hasPagination()) {
            $cacheKey = 'books_' . md5(serialize($query));

            return $this->cache->get($cacheKey, function (ItemInterface $item) use ($query) {
                $item->expiresAfter(300); // 5 minutes
                return parent::findByQuery($query);
            });
        }

        return parent::findByQuery($query);
    }
}
```

## ✅ Checkpoint - Validation des Acquis

Avant de passer au guide suivant, vérifiez que vous savez :

- [ ] **Créer** un QueryBuilder avec interface fluide
- [ ] **Valider** les paramètres de requête
- [ ] **Gérer** la pagination et le tri
- [ ] **Tester** le QueryBuilder complètement
- [ ] **Utiliser** le QueryBuilder dans un contrôleur

## 💡 Points Clés à Retenir

- ✅ **QueryBuilder** évite l'explosion combinatoire des méthodes
- ✅ **Interface fluide** rend le code lisible et expressif
- ✅ **Validation** des paramètres évite les erreurs
- ✅ **Pagination** et **tri** sont intégrés naturellement
- ✅ **Tests** valident tous les cas d'usage

---

**Parfait !** 🎉 Vous maîtrisez maintenant les QueryBuilders pour créer des requêtes flexibles et robustes !
