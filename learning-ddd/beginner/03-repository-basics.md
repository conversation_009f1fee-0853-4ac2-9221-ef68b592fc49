# 🟢 Repository Basics - Persistance Simple

## 🎯 Objectifs de ce Guide

À la fin de ce chapitre, vous saurez :
- ✅ Comprendre le pattern Repository
- ✅ Créer une interface de repository
- ✅ Implémenter un repository en mémoire
- ✅ Utiliser le repository dans vos tests

**Temps estimé** : 45 minutes

## 📚 Qu'est-ce qu'un Repository ?

Un **Repository** est comme une collection d'objets en mémoire :
- ✅ **Cache** la complexité de la persistance
- ✅ **Centralise** les requêtes de données
- ✅ **Facilite** les tests avec des implémentations en mémoire
- ✅ **Respecte** l'inversion de dépendance

### Analogie : Une Bibliothèque

Imaginez une bibliothèque :
- Vous demandez un livre par son titre
- Le bibliothécaire le trouve pour vous
- Vous ne savez pas où il était rangé
- Le repository fait pareil avec vos entités !

## 🛠️ Votre Premier Repository

### Étape 1 : Interface du Repository

```php
<?php
// src/Domain/Repository/BookRepositoryInterface.php

namespace App\Domain\Repository;

use App\Domain\Model\Book;
use App\Domain\ValueObject\BookId;

interface BookRepositoryInterface
{
    /**
     * Sauvegarde un livre
     */
    public function save(Book $book): void;

    /**
     * Trouve un livre par son ID
     */
    public function findById(BookId $id): ?Book;

    /**
     * Trouve tous les livres
     * @return Book[]
     */
    public function findAll(): array;

    /**
     * Trouve les livres disponibles
     * @return Book[]
     */
    public function findAvailable(): array;

    /**
     * Supprime un livre
     */
    public function remove(Book $book): void;

    /**
     * Compte le nombre total de livres
     */
    public function count(): int;
}
```

### Étape 2 : Implémentation en Mémoire

```php
<?php
// src/Infrastructure/Repository/InMemoryBookRepository.php

namespace App\Infrastructure\Repository;

use App\Domain\Model\Book;
use App\Domain\Repository\BookRepositoryInterface;
use App\Domain\ValueObject\BookId;

class InMemoryBookRepository implements BookRepositoryInterface
{
    /** @var Book[] */
    private array $books = [];

    public function save(Book $book): void
    {
        $this->books[$book->getId()->value] = $book;
    }

    public function findById(BookId $id): ?Book
    {
        return $this->books[$id->value] ?? null;
    }

    public function findAll(): array
    {
        return array_values($this->books);
    }

    public function findAvailable(): array
    {
        return array_filter(
            $this->books,
            fn(Book $book) => $book->isAvailable()
        );
    }

    public function remove(Book $book): void
    {
        unset($this->books[$book->getId()->value]);
    }

    public function count(): int
    {
        return count($this->books);
    }

    /**
     * Méthode utilitaire pour les tests
     */
    public function clear(): void
    {
        $this->books = [];
    }
}
```

### Étape 3 : Tests du Repository

```php
<?php
// tests/Unit/Infrastructure/Repository/InMemoryBookRepositoryTest.php

namespace App\Tests\Unit\Infrastructure\Repository;

use App\Domain\Model\Book;
use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\Price;
use App\Infrastructure\Repository\InMemoryBookRepository;
use PHPUnit\Framework\TestCase;

class InMemoryBookRepositoryTest extends TestCase
{
    private InMemoryBookRepository $repository;

    protected function setUp(): void
    {
        $this->repository = new InMemoryBookRepository();
    }

    public function testSaveAndFindBook(): void
    {
        // Arrange
        $book = Book::create(
            new BookTitle('Clean Code'),
            new Price(29.99)
        );

        // Act
        $this->repository->save($book);
        $foundBook = $this->repository->findById($book->getId());

        // Assert
        $this->assertSame($book, $foundBook);
    }

    public function testFindNonExistentBook(): void
    {
        // Arrange
        $nonExistentId = \App\Domain\ValueObject\BookId::generate();

        // Act
        $result = $this->repository->findById($nonExistentId);

        // Assert
        $this->assertNull($result);
    }

    public function testFindAll(): void
    {
        // Arrange
        $book1 = Book::create(new BookTitle('Book 1'), new Price(10.0));
        $book2 = Book::create(new BookTitle('Book 2'), new Price(20.0));

        $this->repository->save($book1);
        $this->repository->save($book2);

        // Act
        $allBooks = $this->repository->findAll();

        // Assert
        $this->assertCount(2, $allBooks);
        $this->assertContains($book1, $allBooks);
        $this->assertContains($book2, $allBooks);
    }

    public function testFindAvailable(): void
    {
        // Arrange
        $availableBook = Book::create(new BookTitle('Available'), new Price(10.0));
        $borrowedBook = Book::create(new BookTitle('Borrowed'), new Price(20.0));
        $borrowedBook->borrow();

        $this->repository->save($availableBook);
        $this->repository->save($borrowedBook);

        // Act
        $availableBooks = $this->repository->findAvailable();

        // Assert
        $this->assertCount(1, $availableBooks);
        $this->assertContains($availableBook, $availableBooks);
        $this->assertNotContains($borrowedBook, $availableBooks);
    }

    public function testRemoveBook(): void
    {
        // Arrange
        $book = Book::create(new BookTitle('To Remove'), new Price(15.0));
        $this->repository->save($book);

        // Act
        $this->repository->remove($book);

        // Assert
        $this->assertNull($this->repository->findById($book->getId()));
        $this->assertSame(0, $this->repository->count());
    }

    public function testCount(): void
    {
        // Arrange
        $this->assertSame(0, $this->repository->count());

        // Act
        $book1 = Book::create(new BookTitle('Book 1'), new Price(10.0));
        $book2 = Book::create(new BookTitle('Book 2'), new Price(20.0));

        $this->repository->save($book1);
        $this->assertSame(1, $this->repository->count());

        $this->repository->save($book2);
        $this->assertSame(2, $this->repository->count());
    }

    public function testClear(): void
    {
        // Arrange
        $book = Book::create(new BookTitle('Test'), new Price(10.0));
        $this->repository->save($book);

        // Act
        $this->repository->clear();

        // Assert
        $this->assertSame(0, $this->repository->count());
        $this->assertEmpty($this->repository->findAll());
    }
}
```

## 🎯 Exercice Pratique 1 : MemberRepository

Créez un `MemberRepositoryInterface` et son implémentation en mémoire avec :
- `save(Member $member): void`
- `findById(MemberId $id): ?Member`
- `findByEmail(Email $email): ?Member`
- `findActive(): Member[]`

<details>
<summary>💡 Solution</summary>

```php
<?php
// src/Domain/Repository/MemberRepositoryInterface.php

namespace App\Domain\Repository;

use App\Domain\Model\Member;
use App\Domain\ValueObject\MemberId;
use App\Domain\ValueObject\Email;

interface MemberRepositoryInterface
{
    public function save(Member $member): void;
    public function findById(MemberId $id): ?Member;
    public function findByEmail(Email $email): ?Member;
    public function findActive(): array;
    public function remove(Member $member): void;
    public function count(): int;
}
```

```php
<?php
// src/Infrastructure/Repository/InMemoryMemberRepository.php

namespace App\Infrastructure\Repository;

use App\Domain\Model\Member;
use App\Domain\Repository\MemberRepositoryInterface;
use App\Domain\ValueObject\MemberId;
use App\Domain\ValueObject\Email;

class InMemoryMemberRepository implements MemberRepositoryInterface
{
    /** @var Member[] */
    private array $members = [];

    public function save(Member $member): void
    {
        $this->members[$member->getId()->value] = $member;
    }

    public function findById(MemberId $id): ?Member
    {
        return $this->members[$id->value] ?? null;
    }

    public function findByEmail(Email $email): ?Member
    {
        foreach ($this->members as $member) {
            if ($member->getEmail()->value === $email->value) {
                return $member;
            }
        }
        return null;
    }

    public function findActive(): array
    {
        return array_filter(
            $this->members,
            fn(Member $member) => $member->isActive()
        );
    }

    public function remove(Member $member): void
    {
        unset($this->members[$member->getId()->value]);
    }

    public function count(): int
    {
        return count($this->members);
    }

    public function clear(): void
    {
        $this->members = [];
    }
}
```
</details>

## 🔍 Repository avec Critères de Recherche

### Étape 1 : Méthodes de Recherche Avancées

```php
<?php
// Ajouter à BookRepositoryInterface

interface BookRepositoryInterface
{
    // ... méthodes existantes

    /**
     * Trouve les livres par titre (recherche partielle)
     * @return Book[]
     */
    public function findByTitleContaining(string $search): array;

    /**
     * Trouve les livres dans une fourchette de prix
     * @return Book[]
     */
    public function findByPriceRange(float $minPrice, float $maxPrice): array;

    /**
     * Trouve les livres empruntés depuis plus de X jours
     * @return Book[]
     */
    public function findBorrowedLongerThan(int $days): array;
}
```

### Étape 2 : Implémentation des Recherches

```php
<?php
// Ajouter à InMemoryBookRepository

public function findByTitleContaining(string $search): array
{
    $searchLower = strtolower($search);
    
    return array_filter(
        $this->books,
        fn(Book $book) => str_contains(
            strtolower($book->getTitle()->value),
            $searchLower
        )
    );
}

public function findByPriceRange(float $minPrice, float $maxPrice): array
{
    return array_filter(
        $this->books,
        fn(Book $book) => $book->getPrice()->amount >= $minPrice 
                       && $book->getPrice()->amount <= $maxPrice
    );
}

public function findBorrowedLongerThan(int $days): array
{
    $cutoffDate = new \DateTimeImmutable("-{$days} days");
    
    return array_filter(
        $this->books,
        fn(Book $book) => $book->isBorrowed() 
                       && $book->getBorrowedAt() < $cutoffDate
    );
}
```

## 🧪 Utilisation dans les Tests

### Factory pour les Tests

```php
<?php
// tests/Support/BookFactory.php

namespace App\Tests\Support;

use App\Domain\Model\Book;
use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\Price;

class BookFactory
{
    public static function create(
        string $title = 'Default Book',
        float $price = 19.99
    ): Book {
        return Book::create(
            new BookTitle($title),
            new Price($price)
        );
    }

    public static function createBorrowed(
        string $title = 'Borrowed Book',
        float $price = 19.99
    ): Book {
        $book = self::create($title, $price);
        $book->borrow();
        return $book;
    }

    public static function createMany(int $count): array
    {
        $books = [];
        for ($i = 1; $i <= $count; $i++) {
            $books[] = self::create("Book {$i}", $i * 10.0);
        }
        return $books;
    }
}
```

### Test d'Intégration Simple

```php
<?php
// tests/Integration/BookManagementTest.php

namespace App\Tests\Integration;

use App\Infrastructure\Repository\InMemoryBookRepository;
use App\Tests\Support\BookFactory;
use PHPUnit\Framework\TestCase;

class BookManagementTest extends TestCase
{
    private InMemoryBookRepository $bookRepository;

    protected function setUp(): void
    {
        $this->bookRepository = new InMemoryBookRepository();
    }

    public function testCompleteBookLifecycle(): void
    {
        // 1. Créer et sauvegarder un livre
        $book = BookFactory::create('Clean Architecture', 35.99);
        $this->bookRepository->save($book);

        // 2. Vérifier qu'il est disponible
        $availableBooks = $this->bookRepository->findAvailable();
        $this->assertCount(1, $availableBooks);
        $this->assertContains($book, $availableBooks);

        // 3. Emprunter le livre
        $book->borrow();
        $this->bookRepository->save($book);

        // 4. Vérifier qu'il n'est plus disponible
        $availableBooks = $this->bookRepository->findAvailable();
        $this->assertCount(0, $availableBooks);

        // 5. Retourner le livre
        $book->return();
        $this->bookRepository->save($book);

        // 6. Vérifier qu'il est à nouveau disponible
        $availableBooks = $this->bookRepository->findAvailable();
        $this->assertCount(1, $availableBooks);
    }

    public function testSearchFunctionality(): void
    {
        // Arrange
        $books = [
            BookFactory::create('Clean Code', 29.99),
            BookFactory::create('Clean Architecture', 35.99),
            BookFactory::create('Refactoring', 39.99),
        ];

        foreach ($books as $book) {
            $this->bookRepository->save($book);
        }

        // Act & Assert
        $cleanBooks = $this->bookRepository->findByTitleContaining('Clean');
        $this->assertCount(2, $cleanBooks);

        $expensiveBooks = $this->bookRepository->findByPriceRange(30.0, 40.0);
        $this->assertCount(2, $expensiveBooks);
    }
}
```

## ✅ Checkpoint - Validation des Acquis

Avant de passer au guide suivant, vérifiez que vous savez :

- [ ] **Créer** une interface de repository
- [ ] **Implémenter** un repository en mémoire
- [ ] **Ajouter** des méthodes de recherche
- [ ] **Tester** le repository unitairement
- [ ] **Utiliser** le repository dans des tests d'intégration

## 🎯 Mini-Projet : Système de Bibliothèque

Créez un système complet avec :

1. **Repositories** :
   - `BookRepository` (avec recherche par titre, prix)
   - `MemberRepository` (avec recherche par email)

2. **Tests** :
   - Tests unitaires pour chaque repository
   - Test d'intégration pour un scénario complet

3. **Factories** :
   - `BookFactory` et `MemberFactory` pour les tests

**Temps estimé** : 60 minutes

## 🚀 Prochaine Étape

Une fois ce guide maîtrisé, passez à **[04-application-basics.md](04-application-basics.md)** pour apprendre :
- Les Commands et Queries
- Les Handlers
- L'orchestration des cas d'usage

---

**Parfait !** 🎉 Vous savez maintenant persister et récupérer vos entités avec le pattern Repository !
