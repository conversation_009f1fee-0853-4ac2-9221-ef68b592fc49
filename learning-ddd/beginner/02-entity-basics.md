# 🟢 Entity Basics - Entités avec Cycle de Vie

## 🎯 Objectifs de ce Guide

À la fin de ce chapitre, vous saurez :
- ✅ Créer des entités avec identité forte
- ✅ Implémenter des méthodes métier expressives
- ✅ Gérer le cycle de vie d'une entité
- ✅ Protéger les invariants métier

**Temps estimé** : 45 minutes

## 📚 Qu'est-ce qu'une Entité ?

Une **entité** est différente d'un Value Object :
- ✅ A une **identité unique** (ID)
- ✅ Peut **changer d'état** au cours du temps
- ✅ A un **cycle de vie** (création → modifications → suppression)
- ✅ Encapsule des **règles métier**

### Exemple : Un Livre dans une Bibliothèque

Un livre peut :
- Être emprunté/rendu
- Changer de prix
- Être réservé
- Avoir des avis

## 🛠️ Entité avec Identité Forte

### Étape 1 : Value Object pour l'ID

```php
<?php
// src/Domain/ValueObject/BookId.php

namespace App\Domain\ValueObject;

final readonly class BookId
{
    public function __construct(public string $value)
    {
        if (empty($value)) {
            throw new \InvalidArgumentException('BookId cannot be empty');
        }
    }

    public static function generate(): self
    {
        return new self(uniqid('book_', true));
    }

    public function equals(BookId $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

### Étape 2 : Entité Book Améliorée

```php
<?php
// src/Domain/Model/Book.php

namespace App\Domain\Model;

use App\Domain\ValueObject\BookId;
use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\Price;

class Book
{
    private bool $isAvailable = true;
    private ?\DateTimeImmutable $borrowedAt = null;

    public function __construct(
        private readonly BookId $id,
        private BookTitle $title,
        private Price $price
    ) {}

    public static function create(BookTitle $title, Price $price): self
    {
        return new self(BookId::generate(), $title, $price);
    }

    // Méthodes métier expressives
    public function borrow(): void
    {
        if (!$this->isAvailable) {
            throw new \DomainException('Book is not available for borrowing');
        }

        $this->isAvailable = false;
        $this->borrowedAt = new \DateTimeImmutable();
    }

    public function return(): void
    {
        if ($this->isAvailable) {
            throw new \DomainException('Book is not currently borrowed');
        }

        $this->isAvailable = true;
        $this->borrowedAt = null;
    }

    public function changePrice(Price $newPrice): void
    {
        if (!$this->isAvailable) {
            throw new \DomainException('Cannot change price of borrowed book');
        }

        $this->price = $newPrice;
    }

    // Méthodes de requête
    public function isAvailable(): bool
    {
        return $this->isAvailable;
    }

    public function isBorrowed(): bool
    {
        return !$this->isAvailable;
    }

    public function getBorrowDuration(): ?\DateInterval
    {
        if ($this->borrowedAt === null) {
            return null;
        }

        return $this->borrowedAt->diff(new \DateTimeImmutable());
    }

    // Getters
    public function getId(): BookId { return $this->id; }
    public function getTitle(): BookTitle { return $this->title; }
    public function getPrice(): Price { return $this->price; }
    public function getBorrowedAt(): ?\DateTimeImmutable { return $this->borrowedAt; }
}
```

### Étape 3 : Tests de l'Entité

```php
<?php
// tests/Unit/Domain/Model/BookTest.php

namespace App\Tests\Unit\Domain\Model;

use App\Domain\Model\Book;
use App\Domain\ValueObject\BookId;
use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\Price;
use PHPUnit\Framework\TestCase;

class BookTest extends TestCase
{
    private Book $book;

    protected function setUp(): void
    {
        $this->book = Book::create(
            new BookTitle('Clean Code'),
            new Price(29.99)
        );
    }

    public function testCreateBook(): void
    {
        $this->assertInstanceOf(BookId::class, $this->book->getId());
        $this->assertTrue($this->book->isAvailable());
        $this->assertFalse($this->book->isBorrowed());
        $this->assertNull($this->book->getBorrowedAt());
    }

    public function testBorrowAvailableBook(): void
    {
        // Act
        $this->book->borrow();

        // Assert
        $this->assertFalse($this->book->isAvailable());
        $this->assertTrue($this->book->isBorrowed());
        $this->assertNotNull($this->book->getBorrowedAt());
    }

    public function testCannotBorrowUnavailableBook(): void
    {
        // Arrange
        $this->book->borrow();

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Book is not available for borrowing');

        // Act
        $this->book->borrow();
    }

    public function testReturnBorrowedBook(): void
    {
        // Arrange
        $this->book->borrow();

        // Act
        $this->book->return();

        // Assert
        $this->assertTrue($this->book->isAvailable());
        $this->assertFalse($this->book->isBorrowed());
        $this->assertNull($this->book->getBorrowedAt());
    }

    public function testCannotReturnAvailableBook(): void
    {
        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Book is not currently borrowed');

        // Act
        $this->book->return();
    }

    public function testChangePriceOfAvailableBook(): void
    {
        // Arrange
        $newPrice = new Price(24.99);

        // Act
        $this->book->changePrice($newPrice);

        // Assert
        $this->assertSame($newPrice, $this->book->getPrice());
    }

    public function testCannotChangePriceOfBorrowedBook(): void
    {
        // Arrange
        $this->book->borrow();

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Cannot change price of borrowed book');

        // Act
        $this->book->changePrice(new Price(19.99));
    }

    public function testGetBorrowDuration(): void
    {
        // Arrange
        $this->book->borrow();

        // Act
        $duration = $this->book->getBorrowDuration();

        // Assert
        $this->assertInstanceOf(\DateInterval::class, $duration);
        $this->assertSame(0, $duration->days); // Emprunté aujourd'hui
    }
}
```

## 🎯 Exercice Pratique 1 : Member (Membre)

Créez une entité `Member` qui :
- A un ID unique (`MemberId`)
- A un nom (`MemberName`) et email (`Email`)
- Peut être activé/désactivé
- Ne peut emprunter que s'il est actif

<details>
<summary>💡 Solution</summary>

```php
<?php
// src/Domain/ValueObject/MemberId.php

namespace App\Domain\ValueObject;

final readonly class MemberId
{
    public function __construct(public string $value)
    {
        if (empty($value)) {
            throw new \InvalidArgumentException('MemberId cannot be empty');
        }
    }

    public static function generate(): self
    {
        return new self(uniqid('member_', true));
    }

    public function equals(MemberId $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

```php
<?php
// src/Domain/Model/Member.php

namespace App\Domain\Model;

use App\Domain\ValueObject\MemberId;
use App\Domain\ValueObject\AuthorName;
use App\Domain\ValueObject\Email;

class Member
{
    private bool $isActive = true;

    public function __construct(
        private readonly MemberId $id,
        private AuthorName $name,
        private Email $email
    ) {}

    public static function register(AuthorName $name, Email $email): self
    {
        return new self(MemberId::generate(), $name, $email);
    }

    public function deactivate(): void
    {
        $this->isActive = false;
    }

    public function activate(): void
    {
        $this->isActive = true;
    }

    public function canBorrow(): bool
    {
        return $this->isActive;
    }

    public function updateEmail(Email $newEmail): void
    {
        $this->email = $newEmail;
    }

    // Getters
    public function getId(): MemberId { return $this->id; }
    public function getName(): AuthorName { return $this->name; }
    public function getEmail(): Email { return $this->email; }
    public function isActive(): bool { return $this->isActive; }
}
```
</details>

## 🔄 Entité avec États Complexes

### Étape 1 : Value Object pour le Statut

```php
<?php
// src/Domain/ValueObject/BookStatus.php

namespace App\Domain\ValueObject;

enum BookStatus: string
{
    case AVAILABLE = 'available';
    case BORROWED = 'borrowed';
    case RESERVED = 'reserved';
    case MAINTENANCE = 'maintenance';

    public function canBeBorrowed(): bool
    {
        return $this === self::AVAILABLE;
    }

    public function canBeReserved(): bool
    {
        return in_array($this, [self::BORROWED, self::RESERVED]);
    }
}
```

### Étape 2 : Entité avec Machine à États

```php
<?php
// src/Domain/Model/AdvancedBook.php

namespace App\Domain\Model;

use App\Domain\ValueObject\BookId;
use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\BookStatus;
use App\Domain\ValueObject\Price;

class AdvancedBook
{
    private BookStatus $status = BookStatus::AVAILABLE;
    private array $reservations = [];

    public function __construct(
        private readonly BookId $id,
        private BookTitle $title,
        private Price $price
    ) {}

    public function borrow(): void
    {
        if (!$this->status->canBeBorrowed()) {
            throw new \DomainException(
                "Cannot borrow book with status: {$this->status->value}"
            );
        }

        $this->status = BookStatus::BORROWED;
    }

    public function reserve(string $memberId): void
    {
        if (!$this->status->canBeReserved()) {
            throw new \DomainException(
                "Cannot reserve book with status: {$this->status->value}"
            );
        }

        if (count($this->reservations) >= 3) {
            throw new \DomainException('Maximum reservations reached');
        }

        if (in_array($memberId, $this->reservations)) {
            throw new \DomainException('Member already has reservation');
        }

        $this->reservations[] = $memberId;
        $this->status = BookStatus::RESERVED;
    }

    public function return(): void
    {
        if ($this->status !== BookStatus::BORROWED) {
            throw new \DomainException('Book is not borrowed');
        }

        $this->status = empty($this->reservations) 
            ? BookStatus::AVAILABLE 
            : BookStatus::RESERVED;
    }

    public function sendToMaintenance(): void
    {
        if ($this->status === BookStatus::BORROWED) {
            throw new \DomainException('Cannot send borrowed book to maintenance');
        }

        $this->status = BookStatus::MAINTENANCE;
        $this->reservations = []; // Annuler les réservations
    }

    // Getters
    public function getStatus(): BookStatus { return $this->status; }
    public function getReservations(): array { return $this->reservations; }
    public function getReservationCount(): int { return count($this->reservations); }
}
```

## 🎯 Exercice Pratique 2 : Loan (Emprunt)

Créez une entité `Loan` qui :
- Relie un `Member` et un `Book`
- A une date d'emprunt et une date de retour prévue
- Calcule les jours de retard
- Peut être prolongée (une seule fois)

<details>
<summary>💡 Indice</summary>

Pensez aux Value Objects :
- `LoanId`
- `LoanPeriod` (dates d'emprunt et de retour)
- `Fine` (amende pour retard)

Et aux règles métier :
- Durée standard : 14 jours
- Prolongation : +7 jours (une seule fois)
- Amende : 0.50€ par jour de retard
</details>

## ✅ Checkpoint - Validation des Acquis

Avant de passer au guide suivant, vérifiez que vous savez :

- [ ] **Créer** des entités avec identité forte
- [ ] **Implémenter** des méthodes métier expressives
- [ ] **Protéger** les invariants avec des exceptions
- [ ] **Gérer** les états et transitions
- [ ] **Tester** le comportement métier

## 🎯 Mini-Projet : Système de Réservation

Créez un système simple avec :

1. **Entités** :
   - `Book` (avec statuts)
   - `Member` (actif/inactif)
   - `Reservation` (membre + livre + date)

2. **Règles métier** :
   - Un membre actif peut réserver un livre emprunté
   - Maximum 3 réservations par livre
   - Une réservation expire après 7 jours

**Temps estimé** : 45 minutes

## 🚀 Prochaine Étape

Une fois ce guide maîtrisé, passez à **[03-repository-basics.md](03-repository-basics.md)** pour apprendre :
- Le pattern Repository
- Persistance en mémoire
- Interfaces et implémentations

---

**Excellent !** 🎉 Vous maîtrisez maintenant les entités avec leur cycle de vie et leurs règles métier !
