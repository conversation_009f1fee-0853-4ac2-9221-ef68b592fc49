# 🎯 Exercice 3 : API REST Simple pour Bibliothèque

## 📋 Objectif

Créer une API REST complète pour gérer une bibliothèque simple, en intégrant tous les concepts DDD appris.

**Durée estimée** : 90 minutes  
**Niveau** : Débutant (synthèse)  
**Concepts couverts** : API REST, Commands/Queries, Handlers, Tests E2E

## 🎯 Cahier des Charges

### Fonctionnalités à Implémenter

1. **CRUD Livres** : Créer, lire, modifier, supprimer des livres
2. **CRUD Auteurs** : Gérer les auteurs avec recherche
3. **Emprunts** : Emprunter et retourner des livres
4. **Recherche** : Rechercher livres par titre, auteur, disponibilité

### Endpoints à Créer

```
GET    /api/books              - Liste des livres
POST   /api/books              - Créer un livre
GET    /api/books/{id}         - Détails d'un livre
PUT    /api/books/{id}         - Modifier un livre
DELETE /api/books/{id}         - Supprimer un livre
POST   /api/books/{id}/borrow  - Emprunter un livre
POST   /api/books/{id}/return  - Retourner un livre

GET    /api/authors            - Liste des auteurs
POST   /api/authors            - Créer un auteur
GET    /api/authors/{id}       - Détails d'un auteur
PUT    /api/authors/{id}       - Modifier un auteur
DELETE /api/authors/{id}       - Supprimer un auteur
```

## 🛠️ Étape 1 : Commands et Queries

### Commands pour les Livres

```php
<?php
// src/Application/Command/CreateBookCommand.php

namespace App\Application\Command;

final readonly class CreateBookCommand
{
    public function __construct(
        public string $title,
        public string $authorName,
        public string $isbn,
        public float $price,
        public string $currency = 'EUR'
    ) {}
}
```

```php
<?php
// src/Application/Command/CreateBookCommandHandler.php

namespace App\Application\Handler;

use App\Application\Command\CreateBookCommand;
use App\Domain\Model\Book;
use App\Domain\Repository\BookRepositoryInterface;
use App\Domain\ValueObject\Author;
use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\ISBN;
use App\Domain\ValueObject\Price;

class CreateBookCommandHandler
{
    public function __construct(
        private BookRepositoryInterface $bookRepository
    ) {}

    public function handle(CreateBookCommand $command): Book
    {
        $book = new Book(
            new BookTitle($command->title),
            new Author($command->authorName),
            new ISBN($command->isbn),
            new Price($command->price, $command->currency)
        );

        $this->bookRepository->save($book);

        return $book;
    }
}
```

### Queries pour les Livres

```php
<?php
// src/Application/Query/FindBooksQuery.php

namespace App\Application\Query;

final readonly class FindBooksQuery
{
    public function __construct(
        public ?string $titleSearch = null,
        public ?string $authorSearch = null,
        public ?bool $availableOnly = null,
        public ?float $maxPrice = null
    ) {}
}
```

```php
<?php
// src/Application/DTO/BookDTO.php

namespace App\Application\DTO;

use App\Domain\Model\Book;

final readonly class BookDTO
{
    public function __construct(
        public string $id,
        public string $title,
        public string $author,
        public string $isbn,
        public float $price,
        public string $currency,
        public bool $isAvailable
    ) {}

    public static function fromBook(Book $book): self
    {
        return new self(
            id: $book->getId(),
            title: $book->getTitle()->value,
            author: $book->getAuthor()->name,
            isbn: $book->getIsbn()->value,
            price: $book->getPrice()->amount,
            currency: $book->getPrice()->currency,
            isAvailable: $book->isAvailable()
        );
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'author' => $this->author,
            'isbn' => $this->isbn,
            'price' => $this->price,
            'currency' => $this->currency,
            'isAvailable' => $this->isAvailable,
        ];
    }
}
```

```php
<?php
// src/Application/Handler/FindBooksQueryHandler.php

namespace App\Application\Handler;

use App\Application\DTO\BookDTO;
use App\Application\Query\FindBooksQuery;
use App\Domain\Repository\BookRepositoryInterface;

class FindBooksQueryHandler
{
    public function __construct(
        private BookRepositoryInterface $bookRepository
    ) {}

    /**
     * @return BookDTO[]
     */
    public function handle(FindBooksQuery $query): array
    {
        $books = $this->bookRepository->findAll();

        // Filtrer par disponibilité
        if ($query->availableOnly !== null) {
            $books = array_filter(
                $books,
                fn($book) => $book->isAvailable() === $query->availableOnly
            );
        }

        // Filtrer par titre
        if ($query->titleSearch) {
            $books = array_filter(
                $books,
                fn($book) => str_contains(
                    strtolower($book->getTitle()->value),
                    strtolower($query->titleSearch)
                )
            );
        }

        // Filtrer par auteur
        if ($query->authorSearch) {
            $books = array_filter(
                $books,
                fn($book) => str_contains(
                    strtolower($book->getAuthor()->name),
                    strtolower($query->authorSearch)
                )
            );
        }

        // Filtrer par prix
        if ($query->maxPrice) {
            $books = array_filter(
                $books,
                fn($book) => $book->getPrice()->amount <= $query->maxPrice
            );
        }

        return array_map(
            fn($book) => BookDTO::fromBook($book),
            array_values($books)
        );
    }
}
```

## 🎮 Étape 2 : Contrôleurs API

### BookController

```php
<?php
// src/Infrastructure/Controller/BookController.php

namespace App\Infrastructure\Controller;

use App\Application\Command\CreateBookCommand;
use App\Application\Handler\CreateBookCommandHandler;
use App\Application\Handler\FindBooksQueryHandler;
use App\Application\Query\FindBooksQuery;
use App\Domain\Repository\BookRepositoryInterface;
use App\Domain\ValueObject\BookId;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/books', name: 'api_books_')]
class BookController extends AbstractController
{
    public function __construct(
        private CreateBookCommandHandler $createBookHandler,
        private FindBooksQueryHandler $findBooksHandler,
        private BookRepositoryInterface $bookRepository
    ) {}

    #[Route('', name: 'list', methods: ['GET'])]
    public function list(Request $request): JsonResponse
    {
        try {
            $query = new FindBooksQuery(
                titleSearch: $request->query->get('title'),
                authorSearch: $request->query->get('author'),
                availableOnly: $request->query->get('available') ? 
                    filter_var($request->query->get('available'), FILTER_VALIDATE_BOOLEAN) : null,
                maxPrice: $request->query->get('maxPrice') ? 
                    (float) $request->query->get('maxPrice') : null
            );

            $books = $this->findBooksHandler->handle($query);

            return $this->json([
                'books' => array_map(fn($book) => $book->toArray(), $books),
                'total' => count($books)
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to fetch books',
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('', name: 'create', methods: ['POST'])]
    public function create(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (!$data) {
                return $this->json([
                    'error' => 'Invalid JSON'
                ], Response::HTTP_BAD_REQUEST);
            }

            $this->validateCreateBookData($data);

            $command = new CreateBookCommand(
                title: $data['title'],
                authorName: $data['author'],
                isbn: $data['isbn'],
                price: $data['price'],
                currency: $data['currency'] ?? 'EUR'
            );

            $book = $this->createBookHandler->handle($command);
            $bookDTO = \App\Application\DTO\BookDTO::fromBook($book);

            return $this->json($bookDTO->toArray(), Response::HTTP_CREATED);

        } catch (\InvalidArgumentException|\DomainException $e) {
            return $this->json([
                'error' => 'Validation error',
                'message' => $e->getMessage()
            ], Response::HTTP_BAD_REQUEST);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to create book',
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/{id}', name: 'show', methods: ['GET'])]
    public function show(string $id): JsonResponse
    {
        try {
            $book = $this->bookRepository->findById(new BookId($id));

            if (!$book) {
                return $this->json([
                    'error' => 'Book not found'
                ], Response::HTTP_NOT_FOUND);
            }

            $bookDTO = \App\Application\DTO\BookDTO::fromBook($book);
            return $this->json($bookDTO->toArray());

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to fetch book',
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/{id}/borrow', name: 'borrow', methods: ['POST'])]
    public function borrow(string $id): JsonResponse
    {
        try {
            $book = $this->bookRepository->findById(new BookId($id));

            if (!$book) {
                return $this->json([
                    'error' => 'Book not found'
                ], Response::HTTP_NOT_FOUND);
            }

            $book->borrow();
            $this->bookRepository->save($book);

            return $this->json([
                'message' => 'Book borrowed successfully'
            ]);

        } catch (\DomainException $e) {
            return $this->json([
                'error' => 'Cannot borrow book',
                'message' => $e->getMessage()
            ], Response::HTTP_CONFLICT);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to borrow book',
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/{id}/return', name: 'return', methods: ['POST'])]
    public function return(string $id): JsonResponse
    {
        try {
            $book = $this->bookRepository->findById(new BookId($id));

            if (!$book) {
                return $this->json([
                    'error' => 'Book not found'
                ], Response::HTTP_NOT_FOUND);
            }

            $book->return();
            $this->bookRepository->save($book);

            return $this->json([
                'message' => 'Book returned successfully'
            ]);

        } catch (\DomainException $e) {
            return $this->json([
                'error' => 'Cannot return book',
                'message' => $e->getMessage()
            ], Response::HTTP_CONFLICT);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to return book',
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function validateCreateBookData(array $data): void
    {
        $required = ['title', 'author', 'isbn', 'price'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new \InvalidArgumentException("Field '{$field}' is required");
            }
        }

        if (!is_numeric($data['price']) || $data['price'] < 0) {
            throw new \InvalidArgumentException('Price must be a positive number');
        }
    }
}
```

## 🧪 Étape 3 : Tests End-to-End

### Test API Complète

```php
<?php
// tests/EndToEnd/LibraryApiTest.php

namespace App\Tests\EndToEnd;

use App\Infrastructure\Repository\InMemoryBookRepository;
use App\Tests\Support\BookFactory;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class LibraryApiTest extends WebTestCase
{
    private InMemoryBookRepository $bookRepository;

    protected function setUp(): void
    {
        self::bootKernel();
        
        $this->bookRepository = self::getContainer()
            ->get(InMemoryBookRepository::class);
        $this->bookRepository->clear();
    }

    public function testCompleteBookManagementWorkflow(): void
    {
        $client = static::createClient();

        // 1. Créer un livre via API
        $bookData = [
            'title' => 'Clean Code',
            'author' => 'Robert C. Martin',
            'isbn' => '978-0-1234-5678-9',
            'price' => 29.99,
            'currency' => 'EUR'
        ];

        $client->request('POST', '/api/books', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], json_encode($bookData));

        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        $response = json_decode($client->getResponse()->getContent(), true);
        $bookId = $response['id'];

        // 2. Lister les livres
        $client->request('GET', '/api/books');
        $this->assertResponseIsSuccessful();

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame(1, $response['total']);
        $this->assertSame('Clean Code', $response['books'][0]['title']);

        // 3. Récupérer le livre par ID
        $client->request('GET', '/api/books/' . $bookId);
        $this->assertResponseIsSuccessful();

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame('Clean Code', $response['title']);
        $this->assertTrue($response['isAvailable']);

        // 4. Emprunter le livre
        $client->request('POST', '/api/books/' . $bookId . '/borrow');
        $this->assertResponseIsSuccessful();

        // 5. Vérifier que le livre n'est plus disponible
        $client->request('GET', '/api/books/' . $bookId);
        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertFalse($response['isAvailable']);

        // 6. Retourner le livre
        $client->request('POST', '/api/books/' . $bookId . '/return');
        $this->assertResponseIsSuccessful();

        // 7. Vérifier que le livre est à nouveau disponible
        $client->request('GET', '/api/books/' . $bookId);
        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertTrue($response['isAvailable']);
    }

    public function testSearchFunctionality(): void
    {
        $client = static::createClient();

        // Préparer des données
        $books = [
            ['title' => 'Clean Code', 'author' => 'Robert C. Martin', 'price' => 29.99],
            ['title' => 'Clean Architecture', 'author' => 'Robert C. Martin', 'price' => 35.99],
            ['title' => 'Refactoring', 'author' => 'Martin Fowler', 'price' => 39.99],
        ];

        foreach ($books as $i => $bookData) {
            $bookData['isbn'] = "978-0-1234-567{$i}-9";
            $client->request('POST', '/api/books', [], [], [
                'CONTENT_TYPE' => 'application/json',
            ], json_encode($bookData));
        }

        // Test recherche par titre
        $client->request('GET', '/api/books?title=Clean');
        $this->assertResponseIsSuccessful();

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame(2, $response['total']);

        // Test recherche par auteur
        $client->request('GET', '/api/books?author=Fowler');
        $this->assertResponseIsSuccessful();

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame(1, $response['total']);
        $this->assertSame('Refactoring', $response['books'][0]['title']);

        // Test filtre par prix
        $client->request('GET', '/api/books?maxPrice=30');
        $this->assertResponseIsSuccessful();

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame(1, $response['total']);
        $this->assertSame('Clean Code', $response['books'][0]['title']);
    }

    public function testErrorHandling(): void
    {
        $client = static::createClient();

        // Test création avec données invalides
        $invalidData = [
            'title' => '',
            'author' => 'Test Author',
            'isbn' => 'invalid-isbn',
            'price' => -10
        ];

        $client->request('POST', '/api/books', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], json_encode($invalidData));

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        // Test récupération livre inexistant
        $client->request('GET', '/api/books/non-existent-id');
        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);

        // Test emprunt livre inexistant
        $client->request('POST', '/api/books/non-existent-id/borrow');
        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function testBusinessRuleViolations(): void
    {
        $client = static::createClient();

        // Créer un livre
        $book = BookFactory::create();
        $this->bookRepository->save($book);

        // Emprunter le livre
        $client->request('POST', '/api/books/' . $book->getId() . '/borrow');
        $this->assertResponseIsSuccessful();

        // Essayer d'emprunter à nouveau (doit échouer)
        $client->request('POST', '/api/books/' . $book->getId() . '/borrow');
        $this->assertResponseStatusCodeSame(Response::HTTP_CONFLICT);

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame('Cannot borrow book', $response['error']);
    }
}
```

## 🎯 Étape 4 : Exercices Supplémentaires

### Exercice 4.1 : AuthorController

Créez un contrôleur complet pour les auteurs avec :
- CRUD complet
- Recherche par nom et nationalité
- Gestion des erreurs

### Exercice 4.2 : Validation Avancée

Ajoutez :
- Validation des données d'entrée avec Symfony Validator
- Messages d'erreur personnalisés
- Validation de l'unicité des ISBN

### Exercice 4.3 : Documentation API

Ajoutez :
- Annotations OpenAPI/Swagger
- Documentation des endpoints
- Exemples de requêtes/réponses

## ✅ Validation

### Critères de Réussite

- [ ] Tous les endpoints fonctionnent correctement
- [ ] Les règles métier sont respectées
- [ ] La gestion d'erreurs est appropriée
- [ ] Les tests E2E passent tous
- [ ] L'API est bien documentée

### Commandes de Test

```bash
# Tests end-to-end
./vendor/bin/phpunit tests/EndToEnd/

# Démarrer le serveur de développement
symfony server:start

# Tester manuellement avec curl
curl -X GET http://localhost:8000/api/books
curl -X POST http://localhost:8000/api/books \
  -H "Content-Type: application/json" \
  -d '{"title":"Test Book","author":"Test Author","isbn":"978-0-1111-1111-1","price":19.99}'
```

## 🚀 Extensions Possibles

1. **Pagination** pour les listes
2. **Authentification** JWT
3. **Rate limiting** pour l'API
4. **Cache** pour les requêtes fréquentes
5. **Logs** des actions utilisateur
6. **Métriques** d'utilisation de l'API

## 💡 Points Clés Appris

- ✅ **API REST** expose la logique métier via HTTP
- ✅ **Commands/Queries** orchestrent les cas d'usage
- ✅ **DTOs** transportent les données vers l'extérieur
- ✅ **Gestion d'erreurs** communique les problèmes clairement
- ✅ **Tests E2E** valident l'API complète

---

**Félicitations !** 🎉 Vous avez créé une API REST complète en appliquant tous les concepts DDD du niveau débutant !
