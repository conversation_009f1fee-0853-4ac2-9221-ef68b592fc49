# 🎯 Exercice 2 : Système d'Auteur avec Repository

## 📋 Objectif

Créer un système de gestion d'auteurs avec repository et recherche, en appliquant les patterns DDD.

**Durée estimée** : 60 minutes  
**Niveau** : Débutant  
**Concepts couverts** : Entités, Repositories, Recherche, Tests d'intégration

## 🎯 Cahier des Charges

### Fonctionnalités à Implémenter

1. **Gestion des auteurs** avec nom, email, biographie et nationalité
2. **Repository** avec recherche par nom et nationalité
3. **Validation** des données (email valide, nom non vide)
4. **Tests** unitaires et d'intégration

### Règles Métier

- ✅ Le nom ne peut pas être vide
- ✅ L'email doit être valide et unique
- ✅ La biographie est optionnelle mais limitée à 1000 caractères
- ✅ La nationalité doit être un code pays ISO (FR, US, GB, etc.)
- ✅ Un auteur peut être activé/désactivé

## 🛠️ Étape 1 : Value Objects

### AuthorName

```php
<?php
// src/Domain/ValueObject/AuthorName.php

namespace App\Domain\ValueObject;

final readonly class AuthorName
{
    public function __construct(public string $value)
    {
        $this->validate($value);
    }

    private function validate(string $value): void
    {
        $trimmed = trim($value);
        
        if (empty($trimmed)) {
            throw new \InvalidArgumentException('Author name cannot be empty');
        }

        if (strlen($trimmed) < 2) {
            throw new \InvalidArgumentException('Author name must be at least 2 characters');
        }

        if (strlen($trimmed) > 100) {
            throw new \InvalidArgumentException('Author name cannot exceed 100 characters');
        }
    }

    public function __toString(): string
    {
        return trim($this->value);
    }
}
```

### Email

```php
<?php
// src/Domain/ValueObject/Email.php

namespace App\Domain\ValueObject;

final readonly class Email
{
    public function __construct(public string $value)
    {
        $this->validate($value);
    }

    private function validate(string $value): void
    {
        $trimmed = trim($value);
        
        if (!filter_var($trimmed, FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Invalid email format');
        }

        if (strlen($trimmed) > 320) {
            throw new \InvalidArgumentException('Email too long');
        }
    }

    public function getDomain(): string
    {
        return substr($this->value, strpos($this->value, '@') + 1);
    }

    public function getLocalPart(): string
    {
        return substr($this->value, 0, strpos($this->value, '@'));
    }

    public function __toString(): string
    {
        return trim($this->value);
    }
}
```

### Biography

```php
<?php
// src/Domain/ValueObject/Biography.php

namespace App\Domain\ValueObject;

final readonly class Biography
{
    public function __construct(public string $value)
    {
        $this->validate($value);
    }

    private function validate(string $value): void
    {
        if (strlen($value) > 1000) {
            throw new \InvalidArgumentException('Biography cannot exceed 1000 characters');
        }
    }

    public function isEmpty(): bool
    {
        return empty(trim($this->value));
    }

    public function getWordCount(): int
    {
        if ($this->isEmpty()) {
            return 0;
        }
        
        return str_word_count($this->value);
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

### CountryCode

```php
<?php
// src/Domain/ValueObject/CountryCode.php

namespace App\Domain\ValueObject;

final readonly class CountryCode
{
    private const VALID_CODES = [
        'FR', 'US', 'GB', 'DE', 'ES', 'IT', 'CA', 'AU', 'JP', 'BR',
        'IN', 'CN', 'RU', 'MX', 'AR', 'CL', 'CO', 'PE', 'VE', 'UY'
    ];

    public function __construct(public string $value)
    {
        $this->validate($value);
    }

    private function validate(string $value): void
    {
        $upperValue = strtoupper(trim($value));
        
        if (!in_array($upperValue, self::VALID_CODES, true)) {
            throw new \InvalidArgumentException(
                'Invalid country code. Allowed: ' . implode(', ', self::VALID_CODES)
            );
        }
    }

    public function __toString(): string
    {
        return strtoupper($this->value);
    }
}
```

### AuthorId

```php
<?php
// src/Domain/ValueObject/AuthorId.php

namespace App\Domain\ValueObject;

final readonly class AuthorId
{
    public function __construct(public string $value)
    {
        if (empty($value)) {
            throw new \InvalidArgumentException('AuthorId cannot be empty');
        }
    }

    public static function generate(): self
    {
        return new self(uniqid('author_', true));
    }

    public function equals(AuthorId $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

## 🏛️ Étape 2 : Entité Author

```php
<?php
// src/Domain/Model/Author.php

namespace App\Domain\Model;

use App\Domain\ValueObject\AuthorId;
use App\Domain\ValueObject\AuthorName;
use App\Domain\ValueObject\Biography;
use App\Domain\ValueObject\CountryCode;
use App\Domain\ValueObject\Email;

class Author
{
    private readonly AuthorId $id;
    private bool $isActive = true;
    private \DateTimeImmutable $createdAt;

    public function __construct(
        private AuthorName $name,
        private Email $email,
        private Biography $biography,
        private CountryCode $nationality
    ) {
        $this->id = AuthorId::generate();
        $this->createdAt = new \DateTimeImmutable();
    }

    public function updateName(AuthorName $newName): void
    {
        $this->name = $newName;
    }

    public function updateEmail(Email $newEmail): void
    {
        $this->email = $newEmail;
    }

    public function updateBiography(Biography $newBiography): void
    {
        $this->biography = $newBiography;
    }

    public function deactivate(): void
    {
        $this->isActive = false;
    }

    public function activate(): void
    {
        $this->isActive = true;
    }

    public function hasBiography(): bool
    {
        return !$this->biography->isEmpty();
    }

    public function isFromCountry(CountryCode $country): bool
    {
        return $this->nationality->value === $country->value;
    }

    // Getters
    public function getId(): AuthorId { return $this->id; }
    public function getName(): AuthorName { return $this->name; }
    public function getEmail(): Email { return $this->email; }
    public function getBiography(): Biography { return $this->biography; }
    public function getNationality(): CountryCode { return $this->nationality; }
    public function isActive(): bool { return $this->isActive; }
    public function getCreatedAt(): \DateTimeImmutable { return $this->createdAt; }
}
```

## 🗃️ Étape 3 : Repository Interface

```php
<?php
// src/Domain/Repository/AuthorRepositoryInterface.php

namespace App\Domain\Repository;

use App\Domain\Model\Author;
use App\Domain\ValueObject\AuthorId;
use App\Domain\ValueObject\CountryCode;
use App\Domain\ValueObject\Email;

interface AuthorRepositoryInterface
{
    /**
     * Sauvegarde un auteur
     */
    public function save(Author $author): void;

    /**
     * Trouve un auteur par son ID
     */
    public function findById(AuthorId $id): ?Author;

    /**
     * Trouve un auteur par son email
     */
    public function findByEmail(Email $email): ?Author;

    /**
     * Trouve tous les auteurs actifs
     * @return Author[]
     */
    public function findActive(): array;

    /**
     * Trouve les auteurs par nationalité
     * @return Author[]
     */
    public function findByNationality(CountryCode $nationality): array;

    /**
     * Recherche par nom (partiel)
     * @return Author[]
     */
    public function searchByName(string $nameSearch): array;

    /**
     * Supprime un auteur
     */
    public function remove(Author $author): void;

    /**
     * Compte le nombre total d'auteurs
     */
    public function count(): int;

    /**
     * Vérifie si un email existe déjà
     */
    public function emailExists(Email $email): bool;
}
```

## 🧠 Étape 4 : Repository en Mémoire

```php
<?php
// src/Infrastructure/Repository/InMemoryAuthorRepository.php

namespace App\Infrastructure\Repository;

use App\Domain\Model\Author;
use App\Domain\Repository\AuthorRepositoryInterface;
use App\Domain\ValueObject\AuthorId;
use App\Domain\ValueObject\CountryCode;
use App\Domain\ValueObject\Email;

class InMemoryAuthorRepository implements AuthorRepositoryInterface
{
    /** @var Author[] */
    private array $authors = [];

    public function save(Author $author): void
    {
        $this->authors[$author->getId()->value] = $author;
    }

    public function findById(AuthorId $id): ?Author
    {
        return $this->authors[$id->value] ?? null;
    }

    public function findByEmail(Email $email): ?Author
    {
        foreach ($this->authors as $author) {
            if ($author->getEmail()->value === $email->value) {
                return $author;
            }
        }
        return null;
    }

    public function findActive(): array
    {
        return array_filter(
            $this->authors,
            fn(Author $author) => $author->isActive()
        );
    }

    public function findByNationality(CountryCode $nationality): array
    {
        return array_filter(
            $this->authors,
            fn(Author $author) => $author->isFromCountry($nationality)
        );
    }

    public function searchByName(string $nameSearch): array
    {
        $searchLower = strtolower($nameSearch);
        
        return array_filter(
            $this->authors,
            fn(Author $author) => str_contains(
                strtolower($author->getName()->value),
                $searchLower
            )
        );
    }

    public function remove(Author $author): void
    {
        unset($this->authors[$author->getId()->value]);
    }

    public function count(): int
    {
        return count($this->authors);
    }

    public function emailExists(Email $email): bool
    {
        return $this->findByEmail($email) !== null;
    }

    /**
     * Méthode utilitaire pour les tests
     */
    public function clear(): void
    {
        $this->authors = [];
    }

    /**
     * Ajouter plusieurs auteurs d'un coup
     */
    public function addMany(Author ...$authors): void
    {
        foreach ($authors as $author) {
            $this->save($author);
        }
    }
}
```

## 🧪 Étape 5 : Tests Unitaires

### Test AuthorName

```php
<?php
// tests/Unit/Domain/ValueObject/AuthorNameTest.php

namespace App\Tests\Unit\Domain\ValueObject;

use App\Domain\ValueObject\AuthorName;
use PHPUnit\Framework\TestCase;

class AuthorNameTest extends TestCase
{
    public function testCreateValidName(): void
    {
        $name = new AuthorName('Robert C. Martin');
        
        $this->assertSame('Robert C. Martin', $name->value);
        $this->assertSame('Robert C. Martin', (string) $name);
    }

    public function testTrimWhitespace(): void
    {
        $name = new AuthorName('  Martin Fowler  ');
        
        $this->assertSame('Martin Fowler', (string) $name);
    }

    /**
     * @dataProvider invalidNameProvider
     */
    public function testRejectInvalidName(string $invalidName, string $expectedMessage): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage($expectedMessage);
        
        new AuthorName($invalidName);
    }

    public static function invalidNameProvider(): array
    {
        return [
            'empty' => ['', 'Author name cannot be empty'],
            'whitespace only' => ['   ', 'Author name cannot be empty'],
            'too short' => ['A', 'Author name must be at least 2 characters'],
            'too long' => [str_repeat('a', 101), 'Author name cannot exceed 100 characters'],
        ];
    }
}
```

### Test Author

```php
<?php
// tests/Unit/Domain/Model/AuthorTest.php

namespace App\Tests\Unit\Domain\Model;

use App\Domain\Model\Author;
use App\Domain\ValueObject\AuthorName;
use App\Domain\ValueObject\Biography;
use App\Domain\ValueObject\CountryCode;
use App\Domain\ValueObject\Email;
use PHPUnit\Framework\TestCase;

class AuthorTest extends TestCase
{
    private Author $author;

    protected function setUp(): void
    {
        $this->author = new Author(
            new AuthorName('Robert C. Martin'),
            new Email('<EMAIL>'),
            new Biography('Software engineer and author'),
            new CountryCode('US')
        );
    }

    public function testCreateAuthor(): void
    {
        $this->assertNotEmpty($this->author->getId()->value);
        $this->assertSame('Robert C. Martin', $this->author->getName()->value);
        $this->assertSame('<EMAIL>', $this->author->getEmail()->value);
        $this->assertTrue($this->author->isActive());
        $this->assertTrue($this->author->hasBiography());
    }

    public function testUpdateName(): void
    {
        $newName = new AuthorName('Uncle Bob');
        
        $this->author->updateName($newName);
        
        $this->assertSame('Uncle Bob', $this->author->getName()->value);
    }

    public function testDeactivateAndActivate(): void
    {
        $this->assertTrue($this->author->isActive());
        
        $this->author->deactivate();
        $this->assertFalse($this->author->isActive());
        
        $this->author->activate();
        $this->assertTrue($this->author->isActive());
    }

    public function testIsFromCountry(): void
    {
        $this->assertTrue($this->author->isFromCountry(new CountryCode('US')));
        $this->assertFalse($this->author->isFromCountry(new CountryCode('FR')));
    }

    public function testAuthorWithoutBiography(): void
    {
        $author = new Author(
            new AuthorName('Jane Doe'),
            new Email('<EMAIL>'),
            new Biography(''),
            new CountryCode('FR')
        );

        $this->assertFalse($author->hasBiography());
    }
}
```

### Test Repository

```php
<?php
// tests/Unit/Infrastructure/Repository/InMemoryAuthorRepositoryTest.php

namespace App\Tests\Unit\Infrastructure\Repository;

use App\Domain\ValueObject\AuthorName;
use App\Domain\ValueObject\Biography;
use App\Domain\ValueObject\CountryCode;
use App\Domain\ValueObject\Email;
use App\Infrastructure\Repository\InMemoryAuthorRepository;
use App\Tests\Support\AuthorFactory;
use PHPUnit\Framework\TestCase;

class InMemoryAuthorRepositoryTest extends TestCase
{
    private InMemoryAuthorRepository $repository;

    protected function setUp(): void
    {
        $this->repository = new InMemoryAuthorRepository();
    }

    public function testSaveAndFindAuthor(): void
    {
        $author = AuthorFactory::create();
        
        $this->repository->save($author);
        $foundAuthor = $this->repository->findById($author->getId());
        
        $this->assertSame($author, $foundAuthor);
    }

    public function testFindByEmail(): void
    {
        $author = AuthorFactory::create(email: '<EMAIL>');
        $this->repository->save($author);
        
        $foundAuthor = $this->repository->findByEmail(new Email('<EMAIL>'));
        
        $this->assertSame($author, $foundAuthor);
    }

    public function testFindByNationality(): void
    {
        $frenchAuthor = AuthorFactory::create(nationality: 'FR');
        $usAuthor = AuthorFactory::create(nationality: 'US');
        
        $this->repository->addMany($frenchAuthor, $usAuthor);
        
        $frenchAuthors = $this->repository->findByNationality(new CountryCode('FR'));
        
        $this->assertCount(1, $frenchAuthors);
        $this->assertContains($frenchAuthor, $frenchAuthors);
    }

    public function testSearchByName(): void
    {
        $martin = AuthorFactory::create(name: 'Robert C. Martin');
        $fowler = AuthorFactory::create(name: 'Martin Fowler');
        $beck = AuthorFactory::create(name: 'Kent Beck');
        
        $this->repository->addMany($martin, $fowler, $beck);
        
        $martinAuthors = $this->repository->searchByName('Martin');
        
        $this->assertCount(2, $martinAuthors);
        $this->assertContains($martin, $martinAuthors);
        $this->assertContains($fowler, $martinAuthors);
    }

    public function testEmailExists(): void
    {
        $author = AuthorFactory::create(email: '<EMAIL>');
        $this->repository->save($author);
        
        $this->assertTrue($this->repository->emailExists(new Email('<EMAIL>')));
        $this->assertFalse($this->repository->emailExists(new Email('<EMAIL>')));
    }
}
```

## 🏭 Étape 6 : Factory pour les Tests

```php
<?php
// tests/Support/AuthorFactory.php

namespace App\Tests\Support;

use App\Domain\Model\Author;
use App\Domain\ValueObject\AuthorName;
use App\Domain\ValueObject\Biography;
use App\Domain\ValueObject\CountryCode;
use App\Domain\ValueObject\Email;

class AuthorFactory
{
    public static function create(
        string $name = 'Default Author',
        string $email = '<EMAIL>',
        string $biography = 'Default biography',
        string $nationality = 'US'
    ): Author {
        return new Author(
            new AuthorName($name),
            new Email($email),
            new Biography($biography),
            new CountryCode($nationality)
        );
    }

    public static function createFrench(string $name = 'Auteur Français'): Author
    {
        return self::create(
            name: $name,
            email: strtolower(str_replace(' ', '.', $name)) . '@example.fr',
            nationality: 'FR'
        );
    }

    public static function createWithoutBiography(string $name = 'No Bio Author'): Author
    {
        return self::create(
            name: $name,
            biography: ''
        );
    }

    public static function createInactive(string $name = 'Inactive Author'): Author
    {
        $author = self::create($name);
        $author->deactivate();
        return $author;
    }
}
```

## 🔗 Étape 7 : Test d'Intégration

```php
<?php
// tests/Integration/AuthorManagementTest.php

namespace App\Tests\Integration;

use App\Infrastructure\Repository\InMemoryAuthorRepository;
use App\Tests\Support\AuthorFactory;
use PHPUnit\Framework\TestCase;

class AuthorManagementTest extends TestCase
{
    private InMemoryAuthorRepository $repository;

    protected function setUp(): void
    {
        $this->repository = new InMemoryAuthorRepository();
    }

    public function testCompleteAuthorWorkflow(): void
    {
        // 1. Créer et sauvegarder des auteurs
        $martin = AuthorFactory::create('Robert C. Martin', '<EMAIL>');
        $fowler = AuthorFactory::createFrench('Martin Fowler');
        
        $this->repository->addMany($martin, $fowler);
        
        // 2. Rechercher par nom
        $martinAuthors = $this->repository->searchByName('Martin');
        $this->assertCount(2, $martinAuthors);
        
        // 3. Filtrer par nationalité
        $frenchAuthors = $this->repository->findByNationality(new \App\Domain\ValueObject\CountryCode('FR'));
        $this->assertCount(1, $frenchAuthors);
        
        // 4. Désactiver un auteur
        $martin->deactivate();
        $this->repository->save($martin);
        
        $activeAuthors = $this->repository->findActive();
        $this->assertCount(1, $activeAuthors);
        $this->assertContains($fowler, $activeAuthors);
        
        // 5. Vérifier l'unicité des emails
        $this->assertTrue($this->repository->emailExists(new \App\Domain\ValueObject\Email('<EMAIL>')));
    }
}
```

## ✅ Validation

### Critères de Réussite

- [ ] Tous les Value Objects valident correctement
- [ ] L'entité Author encapsule ses règles métier
- [ ] Le repository implémente toutes les méthodes de recherche
- [ ] Les tests couvrent tous les cas d'usage
- [ ] L'unicité des emails est respectée

### Commandes de Test

```bash
# Tous les tests
./vendor/bin/phpunit tests/Unit/Domain/
./vendor/bin/phpunit tests/Integration/

# Tests spécifiques
./vendor/bin/phpunit tests/Unit/Domain/Model/AuthorTest.php
./vendor/bin/phpunit tests/Unit/Infrastructure/Repository/InMemoryAuthorRepositoryTest.php
```

## 🚀 Extensions Possibles

1. **Pagination** dans les méthodes de recherche
2. **Tri** par nom, date de création, nationalité
3. **Statistiques** par nationalité
4. **Export** des auteurs en CSV/JSON
5. **Validation** d'unicité lors de la sauvegarde

## 💡 Points Clés Appris

- ✅ **Repositories** centralisent l'accès aux données
- ✅ **Recherche** peut être implémentée dans le repository
- ✅ **Unicité** est une règle métier à valider
- ✅ **Tests d'intégration** valident les workflows complets
- ✅ **Factories** facilitent la création de données de test

---

**Excellent !** 🎉 Vous maîtrisez maintenant les repositories avec recherche et les tests d'intégration !
