# 🎯 Exercice 1 : Système de Livre Simple

## 📋 Objectif

Créer un système de gestion de livres simple en appliquant les concepts DDD de base.

**Durée estimée** : 45 minutes  
**Niveau** : Débutant  
**Concepts couverts** : Value Objects, Entités, Tests unitaires

## 🎯 Cahier des Charges

### Fonctionnalités à Implémenter

1. **Gestion des livres** avec titre, auteur, prix et ISBN
2. **Validation** des données (ISBN format, prix positif)
3. **Changement de prix** avec règles métier
4. **Tests unitaires** complets

### Règles Métier

- ✅ Le titre ne peut pas être vide
- ✅ L'ISBN doit respecter le format XXX-X-XXXX-XXXX-X
- ✅ Le prix doit être positif
- ✅ Un livre gratuit (prix = 0) ne peut pas changer de prix
- ✅ Une réduction ne peut pas dépasser 50%

## 🛠️ Étape 1 : Value Objects

### BookTitle

```php
<?php
// src/Domain/ValueObject/BookTitle.php

namespace App\Domain\ValueObject;

final readonly class BookTitle
{
    public function __construct(public string $value)
    {
        $this->validate($value);
    }

    private function validate(string $value): void
    {
        $trimmed = trim($value);
        
        if (empty($trimmed)) {
            throw new \InvalidArgumentException('Book title cannot be empty');
        }

        if (strlen($trimmed) > 255) {
            throw new \InvalidArgumentException('Book title cannot exceed 255 characters');
        }
    }

    public function __toString(): string
    {
        return trim($this->value);
    }
}
```

### ISBN

```php
<?php
// src/Domain/ValueObject/ISBN.php

namespace App\Domain\ValueObject;

final readonly class ISBN
{
    public function __construct(public string $value)
    {
        $this->validate($value);
    }

    private function validate(string $value): void
    {
        // Format: XXX-X-XXXX-XXXX-X
        $pattern = '/^\d{3}-\d{1}-\d{4}-\d{4}-\d{1}$/';
        
        if (!preg_match($pattern, $value)) {
            throw new \InvalidArgumentException('Invalid ISBN format. Expected: XXX-X-XXXX-XXXX-X');
        }
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
```

### Author

```php
<?php
// src/Domain/ValueObject/Author.php

namespace App\Domain\ValueObject;

final readonly class Author
{
    public function __construct(public string $name)
    {
        if (empty(trim($name))) {
            throw new \InvalidArgumentException('Author name cannot be empty');
        }
    }

    public function __toString(): string
    {
        return $this->name;
    }
}
```

### Price (Réutiliser du guide précédent)

```php
<?php
// src/Domain/ValueObject/Price.php

namespace App\Domain\ValueObject;

final readonly class Price
{
    public function __construct(
        public float $amount,
        public string $currency = 'EUR'
    ) {
        $this->validate($amount, $currency);
    }

    private function validate(float $amount, string $currency): void
    {
        if ($amount < 0) {
            throw new \InvalidArgumentException('Price cannot be negative');
        }

        if (!in_array($currency, ['EUR', 'USD', 'GBP'])) {
            throw new \InvalidArgumentException('Invalid currency');
        }
    }

    public function applyDiscount(float $percentage): self
    {
        if ($percentage < 0 || $percentage > 50) {
            throw new \InvalidArgumentException('Discount must be between 0 and 50%');
        }

        $discountedAmount = $this->amount * (1 - $percentage / 100);
        return new self($discountedAmount, $this->currency);
    }

    public function isFree(): bool
    {
        return $this->amount === 0.0;
    }

    public function __toString(): string
    {
        return sprintf('%.2f %s', $this->amount, $this->currency);
    }
}
```

## 🏛️ Étape 2 : Entité Book

```php
<?php
// src/Domain/Model/Book.php

namespace App\Domain\Model;

use App\Domain\ValueObject\Author;
use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\ISBN;
use App\Domain\ValueObject\Price;

class Book
{
    private string $id;

    public function __construct(
        private BookTitle $title,
        private Author $author,
        private ISBN $isbn,
        private Price $price
    ) {
        $this->id = uniqid('book_', true);
    }

    public function changePrice(Price $newPrice): void
    {
        if ($this->price->isFree()) {
            throw new \DomainException('Cannot change price of a free book');
        }

        $this->price = $newPrice;
    }

    public function applyDiscount(float $percentage): void
    {
        if ($this->price->isFree()) {
            throw new \DomainException('Cannot apply discount to a free book');
        }

        $this->price = $this->price->applyDiscount($percentage);
    }

    public function updateTitle(BookTitle $newTitle): void
    {
        $this->title = $newTitle;
    }

    // Getters
    public function getId(): string { return $this->id; }
    public function getTitle(): BookTitle { return $this->title; }
    public function getAuthor(): Author { return $this->author; }
    public function getIsbn(): ISBN { return $this->isbn; }
    public function getPrice(): Price { return $this->price; }
}
```

## 🧪 Étape 3 : Tests Unitaires

### Test BookTitle

```php
<?php
// tests/Unit/Domain/ValueObject/BookTitleTest.php

namespace App\Tests\Unit\Domain\ValueObject;

use App\Domain\ValueObject\BookTitle;
use PHPUnit\Framework\TestCase;

class BookTitleTest extends TestCase
{
    public function testCreateValidTitle(): void
    {
        $title = new BookTitle('Clean Code');
        
        $this->assertSame('Clean Code', $title->value);
        $this->assertSame('Clean Code', (string) $title);
    }

    public function testTrimWhitespace(): void
    {
        $title = new BookTitle('  Clean Code  ');
        
        $this->assertSame('Clean Code', (string) $title);
    }

    public function testRejectEmptyTitle(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Book title cannot be empty');
        
        new BookTitle('');
    }

    public function testRejectWhitespaceOnlyTitle(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Book title cannot be empty');
        
        new BookTitle('   ');
    }

    public function testRejectTooLongTitle(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Book title cannot exceed 255 characters');
        
        new BookTitle(str_repeat('a', 256));
    }
}
```

### Test ISBN

```php
<?php
// tests/Unit/Domain/ValueObject/ISBNTest.php

namespace App\Tests\Unit\Domain\ValueObject;

use App\Domain\ValueObject\ISBN;
use PHPUnit\Framework\TestCase;

class ISBNTest extends TestCase
{
    public function testCreateValidISBN(): void
    {
        $isbn = new ISBN('978-0-1234-5678-9');
        
        $this->assertSame('978-0-1234-5678-9', $isbn->value);
        $this->assertSame('978-0-1234-5678-9', (string) $isbn);
    }

    /**
     * @dataProvider invalidISBNProvider
     */
    public function testRejectInvalidISBN(string $invalidISBN): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid ISBN format');
        
        new ISBN($invalidISBN);
    }

    public static function invalidISBNProvider(): array
    {
        return [
            'too short' => ['978-0-1234-567'],
            'too long' => ['978-0-1234-5678-90'],
            'wrong separators' => ['978_0_1234_5678_9'],
            'letters' => ['abc-d-efgh-ijkl-m'],
            'no separators' => ['9780123456789'],
            'wrong format' => ['978-01-234-5678-9'],
        ];
    }
}
```

### Test Price

```php
<?php
// tests/Unit/Domain/ValueObject/PriceTest.php

namespace App\Tests\Unit\Domain\ValueObject;

use App\Domain\ValueObject\Price;
use PHPUnit\Framework\TestCase;

class PriceTest extends TestCase
{
    public function testCreateValidPrice(): void
    {
        $price = new Price(29.99, 'EUR');
        
        $this->assertSame(29.99, $price->amount);
        $this->assertSame('EUR', $price->currency);
        $this->assertSame('29.99 EUR', (string) $price);
    }

    public function testApplyValidDiscount(): void
    {
        $price = new Price(100.0, 'EUR');
        $discountedPrice = $price->applyDiscount(20.0);
        
        $this->assertSame(80.0, $discountedPrice->amount);
        $this->assertSame('EUR', $discountedPrice->currency);
        
        // Vérifier l'immutabilité
        $this->assertSame(100.0, $price->amount);
    }

    public function testRejectExcessiveDiscount(): void
    {
        $price = new Price(100.0, 'EUR');
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Discount must be between 0 and 50%');
        
        $price->applyDiscount(60.0);
    }

    public function testIsFree(): void
    {
        $freePrice = new Price(0.0, 'EUR');
        $paidPrice = new Price(10.0, 'EUR');
        
        $this->assertTrue($freePrice->isFree());
        $this->assertFalse($paidPrice->isFree());
    }
}
```

### Test Book

```php
<?php
// tests/Unit/Domain/Model/BookTest.php

namespace App\Tests\Unit\Domain\Model;

use App\Domain\Model\Book;
use App\Domain\ValueObject\Author;
use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\ISBN;
use App\Domain\ValueObject\Price;
use PHPUnit\Framework\TestCase;

class BookTest extends TestCase
{
    private Book $book;

    protected function setUp(): void
    {
        $this->book = new Book(
            new BookTitle('Clean Code'),
            new Author('Robert C. Martin'),
            new ISBN('978-0-1234-5678-9'),
            new Price(29.99, 'EUR')
        );
    }

    public function testCreateBook(): void
    {
        $this->assertNotEmpty($this->book->getId());
        $this->assertSame('Clean Code', $this->book->getTitle()->value);
        $this->assertSame('Robert C. Martin', $this->book->getAuthor()->name);
        $this->assertSame('978-0-1234-5678-9', $this->book->getIsbn()->value);
        $this->assertSame(29.99, $this->book->getPrice()->amount);
    }

    public function testChangePrice(): void
    {
        $newPrice = new Price(24.99, 'EUR');
        
        $this->book->changePrice($newPrice);
        
        $this->assertSame($newPrice, $this->book->getPrice());
        $this->assertSame(24.99, $this->book->getPrice()->amount);
    }

    public function testCannotChangePriceOfFreeBook(): void
    {
        $freeBook = new Book(
            new BookTitle('Free Book'),
            new Author('Free Author'),
            new ISBN('978-0-1111-1111-1'),
            new Price(0.0, 'EUR')
        );

        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Cannot change price of a free book');
        
        $freeBook->changePrice(new Price(10.0, 'EUR'));
    }

    public function testApplyDiscount(): void
    {
        $this->book->applyDiscount(20.0);
        
        $this->assertSame(23.99, $this->book->getPrice()->amount);
    }

    public function testCannotApplyDiscountToFreeBook(): void
    {
        $freeBook = new Book(
            new BookTitle('Free Book'),
            new Author('Free Author'),
            new ISBN('978-0-1111-1111-1'),
            new Price(0.0, 'EUR')
        );

        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Cannot apply discount to a free book');
        
        $freeBook->applyDiscount(10.0);
    }

    public function testUpdateTitle(): void
    {
        $newTitle = new BookTitle('Clean Architecture');
        
        $this->book->updateTitle($newTitle);
        
        $this->assertSame('Clean Architecture', $this->book->getTitle()->value);
    }
}
```

## 🎯 Étape 4 : Factory pour les Tests

```php
<?php
// tests/Support/BookFactory.php

namespace App\Tests\Support;

use App\Domain\Model\Book;
use App\Domain\ValueObject\Author;
use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\ISBN;
use App\Domain\ValueObject\Price;

class BookFactory
{
    public static function create(
        string $title = 'Default Book',
        string $author = 'Default Author',
        string $isbn = '978-0-1234-5678-9',
        float $price = 19.99,
        string $currency = 'EUR'
    ): Book {
        return new Book(
            new BookTitle($title),
            new Author($author),
            new ISBN($isbn),
            new Price($price, $currency)
        );
    }

    public static function createFree(
        string $title = 'Free Book',
        string $author = 'Free Author'
    ): Book {
        return self::create($title, $author, '978-0-0000-0000-0', 0.0);
    }

    public static function createExpensive(
        string $title = 'Expensive Book',
        float $price = 99.99
    ): Book {
        return self::create($title, 'Premium Author', '978-0-9999-9999-9', $price);
    }
}
```

## ✅ Validation

### Critères de Réussite

- [ ] Tous les Value Objects valident correctement leurs données
- [ ] L'entité Book encapsule ses règles métier
- [ ] Les tests couvrent tous les cas (succès et échecs)
- [ ] Le code respecte les principes d'immutabilité
- [ ] Les exceptions sont appropriées et explicites

### Commandes de Test

```bash
# Exécuter tous les tests
./vendor/bin/phpunit tests/Unit/Domain/

# Test spécifique
./vendor/bin/phpunit tests/Unit/Domain/Model/BookTest.php

# Avec couverture
./vendor/bin/phpunit --coverage-text tests/Unit/Domain/
```

## 🚀 Extensions Possibles

Une fois l'exercice de base terminé, vous pouvez ajouter :

1. **Value Object Category** (Fiction, Non-Fiction, Technical)
2. **Value Object PublicationYear** avec validation
3. **Méthode Book::isClassic()** (plus de 50 ans)
4. **Comparaison de prix** entre livres
5. **Sérialisation** en JSON

## 💡 Points Clés Appris

- ✅ **Value Objects** encapsulent et valident les données primitives
- ✅ **Entités** ont une identité et encapsulent les règles métier
- ✅ **Tests unitaires** valident le comportement, pas l'implémentation
- ✅ **Factories** simplifient la création d'objets pour les tests
- ✅ **Exceptions** communiquent clairement les violations de règles

---

**Félicitations !** 🎉 Vous avez créé votre premier système DDD avec des Value Objects robustes et une entité métier !
