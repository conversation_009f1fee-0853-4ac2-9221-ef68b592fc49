# 🟢 Tests Basics - Stratégies de Test Complètes

## 🎯 Objectifs de ce Guide

À la fin de ce chapitre, vous saurez :
- ✅ Organiser vos tests par type et couche
- ✅ Créer des tests unitaires isolés
- ✅ Écrire des tests d'intégration efficaces
- ✅ Utiliser des mocks et fixtures appropriés
- ✅ Mesurer et améliorer la couverture de tests

**Temps estimé** : 60 minutes

## 📚 Pyramide des Tests Revisitée

### Types de Tests par Couche

```
        /\
       /E2E\     ← Tests End-to-End (API complète)
      /____\
     /      \
    /  Intég \   ← Tests d'Intégration (plusieurs couches)
   /__________\
  /            \
 /   Unitaires  \  ← Tests Unitaires (une classe)
/________________\
```

### Répartition Recommandée
- **70%** Tests Unitaires (rapides, isolés)
- **20%** Tests d'Intégration (moyens, réalistes)
- **10%** Tests End-to-End (lents, complets)

## 🧪 Tests Unitaires Avancés

### Étape 1 : Test d'Entité avec Tous les Cas

```php
<?php
// tests/Unit/Domain/Model/BookTest.php

namespace App\Tests\Unit\Domain\Model;

use App\Domain\Model\Book;
use App\Domain\ValueObject\BookId;
use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\Price;
use PHPUnit\Framework\TestCase;

class BookTest extends TestCase
{
    private Book $book;

    protected function setUp(): void
    {
        $this->book = new Book(
            BookId::generate(),
            new BookTitle('Clean Code'),
            new Price(29.99, 'EUR')
        );
    }

    /**
     * @dataProvider validPriceProvider
     */
    public function testChangePriceWithValidValues(float $amount, string $currency): void
    {
        // Arrange
        $newPrice = new Price($amount, $currency);

        // Act
        $this->book->changePrice($newPrice);

        // Assert
        $this->assertEquals($newPrice, $this->book->getPrice());
    }

    public static function validPriceProvider(): array
    {
        return [
            'EUR price' => [19.99, 'EUR'],
            'USD price' => [24.99, 'USD'],
            'GBP price' => [18.99, 'GBP'],
            'Zero price' => [0.0, 'EUR'],
            'High price' => [999.99, 'EUR'],
        ];
    }

    public function testBorrowAvailableBook(): void
    {
        // Arrange
        $this->assertTrue($this->book->isAvailable());

        // Act
        $this->book->borrow();

        // Assert
        $this->assertFalse($this->book->isAvailable());
        $this->assertTrue($this->book->isBorrowed());
        $this->assertInstanceOf(\DateTimeImmutable::class, $this->book->getBorrowedAt());
    }

    public function testCannotBorrowAlreadyBorrowedBook(): void
    {
        // Arrange
        $this->book->borrow();

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Book is not available for borrowing');

        // Act
        $this->book->borrow();
    }

    public function testReturnBorrowedBook(): void
    {
        // Arrange
        $this->book->borrow();
        $this->assertTrue($this->book->isBorrowed());

        // Act
        $this->book->return();

        // Assert
        $this->assertTrue($this->book->isAvailable());
        $this->assertFalse($this->book->isBorrowed());
        $this->assertNull($this->book->getBorrowedAt());
    }

    public function testCannotReturnAvailableBook(): void
    {
        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Book is not currently borrowed');

        // Act
        $this->book->return();
    }

    public function testCannotChangePriceOfBorrowedBook(): void
    {
        // Arrange
        $this->book->borrow();

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Cannot change price of borrowed book');

        // Act
        $this->book->changePrice(new Price(19.99, 'EUR'));
    }

    public function testBookEqualityBasedOnId(): void
    {
        // Arrange
        $sameIdBook = new Book(
            $this->book->getId(),
            new BookTitle('Different Title'),
            new Price(99.99, 'USD')
        );

        $differentIdBook = new Book(
            BookId::generate(),
            $this->book->getTitle(),
            $this->book->getPrice()
        );

        // Assert
        $this->assertTrue($this->book->getId()->equals($sameIdBook->getId()));
        $this->assertFalse($this->book->getId()->equals($differentIdBook->getId()));
    }
}
```

### Étape 2 : Test de Handler avec Mocks

```php
<?php
// tests/Unit/Application/Handler/BorrowBookHandlerTest.php

namespace App\Tests\Unit\Application\Handler;

use App\Application\Command\BorrowBookCommand;
use App\Application\Handler\BorrowBookHandler;
use App\Domain\Model\Book;
use App\Domain\Model\Member;
use App\Domain\Repository\BookRepositoryInterface;
use App\Domain\Repository\MemberRepositoryInterface;
use App\Domain\ValueObject\BookId;
use App\Domain\ValueObject\MemberId;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class BorrowBookHandlerTest extends TestCase
{
    private BookRepositoryInterface|MockObject $bookRepository;
    private MemberRepositoryInterface|MockObject $memberRepository;
    private BorrowBookHandler $handler;

    protected function setUp(): void
    {
        $this->bookRepository = $this->createMock(BookRepositoryInterface::class);
        $this->memberRepository = $this->createMock(MemberRepositoryInterface::class);
        $this->handler = new BorrowBookHandler(
            $this->bookRepository,
            $this->memberRepository
        );
    }

    public function testBorrowBookSuccessfully(): void
    {
        // Arrange
        $bookId = BookId::generate();
        $memberId = MemberId::generate();
        $command = new BorrowBookCommand($bookId, $memberId);

        $book = $this->createMock(Book::class);
        $member = $this->createMock(Member::class);

        // Configuration des mocks
        $this->bookRepository
            ->expects($this->once())
            ->method('findById')
            ->with($bookId)
            ->willReturn($book);

        $this->memberRepository
            ->expects($this->once())
            ->method('findById')
            ->with($memberId)
            ->willReturn($member);

        $member
            ->expects($this->once())
            ->method('canBorrow')
            ->willReturn(true);

        $book
            ->expects($this->once())
            ->method('borrow');

        $this->bookRepository
            ->expects($this->once())
            ->method('save')
            ->with($book);

        // Act
        $this->handler->handle($command);

        // Assert - Les expectations des mocks sont vérifiées automatiquement
    }

    public function testBorrowBookWhenBookNotFound(): void
    {
        // Arrange
        $bookId = BookId::generate();
        $memberId = MemberId::generate();
        $command = new BorrowBookCommand($bookId, $memberId);

        $this->bookRepository
            ->method('findById')
            ->with($bookId)
            ->willReturn(null);

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Book not found');

        // Act
        $this->handler->handle($command);
    }

    public function testBorrowBookWhenMemberNotFound(): void
    {
        // Arrange
        $bookId = BookId::generate();
        $memberId = MemberId::generate();
        $command = new BorrowBookCommand($bookId, $memberId);

        $book = $this->createMock(Book::class);

        $this->bookRepository->method('findById')->willReturn($book);
        $this->memberRepository->method('findById')->willReturn(null);

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Member not found');

        // Act
        $this->handler->handle($command);
    }

    public function testBorrowBookWhenMemberCannotBorrow(): void
    {
        // Arrange
        $bookId = BookId::generate();
        $memberId = MemberId::generate();
        $command = new BorrowBookCommand($bookId, $memberId);

        $book = $this->createMock(Book::class);
        $member = $this->createMock(Member::class);

        $this->bookRepository->method('findById')->willReturn($book);
        $this->memberRepository->method('findById')->willReturn($member);
        $member->method('canBorrow')->willReturn(false);

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Member cannot borrow books');

        // Act
        $this->handler->handle($command);
    }
}
```

## 🔗 Tests d'Intégration

### Étape 1 : Test de Service Complet

```php
<?php
// tests/Integration/Application/LibraryServiceIntegrationTest.php

namespace App\Tests\Integration\Application;

use App\Application\Command\BorrowBookCommand;
use App\Application\Command\ReturnBookCommand;
use App\Application\Query\FindAvailableBooksQuery;
use App\Application\Service\LibraryService;
use App\Infrastructure\Repository\InMemoryBookRepository;
use App\Infrastructure\Repository\InMemoryMemberRepository;
use App\Tests\Support\BookFactory;
use App\Tests\Support\MemberFactory;
use PHPUnit\Framework\TestCase;

class LibraryServiceIntegrationTest extends TestCase
{
    private LibraryService $libraryService;
    private InMemoryBookRepository $bookRepository;
    private InMemoryMemberRepository $memberRepository;

    protected function setUp(): void
    {
        $this->bookRepository = new InMemoryBookRepository();
        $this->memberRepository = new InMemoryMemberRepository();

        // Créer le service avec de vrais handlers et repositories
        $this->libraryService = new LibraryService(
            new \App\Application\Handler\BorrowBookHandler(
                $this->bookRepository,
                $this->memberRepository
            ),
            new \App\Application\Handler\ReturnBookHandler(
                $this->bookRepository
            ),
            new \App\Application\Handler\FindAvailableBooksHandler(
                $this->bookRepository
            )
        );
    }

    public function testCompleteBookBorrowingWorkflow(): void
    {
        // 1. Préparer les données
        $book = BookFactory::create('Integration Test Book');
        $member = MemberFactory::create();

        $this->bookRepository->save($book);
        $this->memberRepository->save($member);

        // 2. Vérifier que le livre est disponible
        $availableBooks = $this->libraryService->findAvailableBooks(
            new FindAvailableBooksQuery()
        );
        $this->assertCount(1, $availableBooks);
        $this->assertSame($book->getId()->value, $availableBooks[0]->id);

        // 3. Emprunter le livre
        $this->libraryService->borrowBook(
            new BorrowBookCommand($book->getId(), $member->getId())
        );

        // 4. Vérifier qu'il n'est plus disponible
        $availableBooks = $this->libraryService->findAvailableBooks(
            new FindAvailableBooksQuery()
        );
        $this->assertCount(0, $availableBooks);

        // 5. Vérifier l'état du livre dans le repository
        $borrowedBook = $this->bookRepository->findById($book->getId());
        $this->assertTrue($borrowedBook->isBorrowed());

        // 6. Retourner le livre
        $this->libraryService->returnBook(
            new ReturnBookCommand($book->getId())
        );

        // 7. Vérifier qu'il est à nouveau disponible
        $availableBooks = $this->libraryService->findAvailableBooks(
            new FindAvailableBooksQuery()
        );
        $this->assertCount(1, $availableBooks);

        $returnedBook = $this->bookRepository->findById($book->getId());
        $this->assertTrue($returnedBook->isAvailable());
    }

    public function testSearchFunctionality(): void
    {
        // Arrange
        $books = [
            BookFactory::create('Clean Code', 29.99),
            BookFactory::create('Clean Architecture', 35.99),
            BookFactory::create('Refactoring', 39.99),
            BookFactory::create('Design Patterns', 45.99),
        ];

        foreach ($books as $book) {
            $this->bookRepository->save($book);
        }

        // Test recherche par titre
        $cleanBooks = $this->libraryService->findAvailableBooks(
            new FindAvailableBooksQuery(titleSearch: 'Clean')
        );
        $this->assertCount(2, $cleanBooks);

        // Test filtre par prix
        $cheapBooks = $this->libraryService->findAvailableBooks(
            new FindAvailableBooksQuery(maxPrice: 30.0)
        );
        $this->assertCount(1, $cheapBooks);
        $this->assertSame('Clean Code', $cheapBooks[0]->title);

        // Test combinaison recherche + prix
        $expensiveCleanBooks = $this->libraryService->findAvailableBooks(
            new FindAvailableBooksQuery(titleSearch: 'Clean', maxPrice: 40.0)
        );
        $this->assertCount(2, $expensiveCleanBooks);
    }
}
```

## 🌐 Tests End-to-End (API)

### Étape 1 : Test API Complet

```php
<?php
// tests/EndToEnd/BookApiTest.php

namespace App\Tests\EndToEnd;

use App\Infrastructure\Repository\InMemoryBookRepository;
use App\Infrastructure\Repository\InMemoryMemberRepository;
use App\Tests\Support\BookFactory;
use App\Tests\Support\MemberFactory;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class BookApiTest extends WebTestCase
{
    private InMemoryBookRepository $bookRepository;
    private InMemoryMemberRepository $memberRepository;

    protected function setUp(): void
    {
        self::bootKernel();
        
        $this->bookRepository = self::getContainer()
            ->get(InMemoryBookRepository::class);
        $this->memberRepository = self::getContainer()
            ->get(InMemoryMemberRepository::class);

        $this->bookRepository->clear();
        $this->memberRepository->clear();
    }

    public function testCompleteBookManagementScenario(): void
    {
        $client = static::createClient();

        // 1. Préparer les données
        $book = BookFactory::create('API Test Book', 25.99);
        $member = MemberFactory::create();

        $this->bookRepository->save($book);
        $this->memberRepository->save($member);

        // 2. Lister les livres disponibles
        $client->request('GET', '/api/books');
        $this->assertResponseIsSuccessful();

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertCount(1, $response['books']);
        $this->assertSame('API Test Book', $response['books'][0]['title']);

        // 3. Emprunter le livre
        $client->request('POST', '/api/books/' . $book->getId()->value . '/borrow', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], json_encode([
            'memberId' => $member->getId()->value
        ]));

        $this->assertResponseIsSuccessful();
        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame('Book borrowed successfully', $response['message']);

        // 4. Vérifier que le livre n'est plus disponible
        $client->request('GET', '/api/books');
        $this->assertResponseIsSuccessful();

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertCount(0, $response['books']);

        // 5. Retourner le livre
        $client->request('POST', '/api/books/' . $book->getId()->value . '/return');
        $this->assertResponseIsSuccessful();

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame('Book returned successfully', $response['message']);

        // 6. Vérifier que le livre est à nouveau disponible
        $client->request('GET', '/api/books');
        $this->assertResponseIsSuccessful();

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertCount(1, $response['books']);
    }

    public function testErrorHandling(): void
    {
        $client = static::createClient();

        // Test emprunt d'un livre inexistant
        $client->request('POST', '/api/books/non-existent/borrow', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], json_encode([
            'memberId' => 'some-member-id'
        ]));

        $this->assertResponseStatusCodeSame(Response::HTTP_CONFLICT);

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame('Business rule violation', $response['error']);

        // Test retour d'un livre inexistant
        $client->request('POST', '/api/books/non-existent/return');
        $this->assertResponseStatusCodeSame(Response::HTTP_CONFLICT);

        // Test requête malformée
        $client->request('POST', '/api/books/some-id/borrow', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], 'invalid-json');

        $this->assertResponseStatusCodeSame(Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
```

## 🏭 Factories et Fixtures Avancées

### Étape 1 : Factory avec Builder Pattern

```php
<?php
// tests/Support/BookBuilder.php

namespace App\Tests\Support;

use App\Domain\Model\Book;
use App\Domain\ValueObject\BookId;
use App\Domain\ValueObject\BookTitle;
use App\Domain\ValueObject\Price;

class BookBuilder
{
    private ?BookId $id = null;
    private string $title = 'Default Book';
    private float $price = 19.99;
    private string $currency = 'EUR';
    private bool $borrowed = false;

    public function withId(BookId $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function withTitle(string $title): self
    {
        $this->title = $title;
        return $this;
    }

    public function withPrice(float $price, string $currency = 'EUR'): self
    {
        $this->price = $price;
        $this->currency = $currency;
        return $this;
    }

    public function borrowed(): self
    {
        $this->borrowed = true;
        return $this;
    }

    public function build(): Book
    {
        $book = new Book(
            $this->id ?? BookId::generate(),
            new BookTitle($this->title),
            new Price($this->price, $this->currency)
        );

        if ($this->borrowed) {
            $book->borrow();
        }

        return $book;
    }
}
```

### Étape 2 : Utilisation du Builder

```php
<?php
// Dans vos tests

public function testWithBuilder(): void
{
    $book = (new BookBuilder())
        ->withTitle('Clean Code')
        ->withPrice(29.99, 'EUR')
        ->borrowed()
        ->build();

    $this->assertTrue($book->isBorrowed());
    $this->assertSame('Clean Code', $book->getTitle()->value);
}
```

## 📊 Couverture de Tests

### Configuration PHPUnit

```xml
<!-- phpunit.xml.dist -->
<coverage processUncoveredFiles="true">
    <include>
        <directory suffix=".php">src</directory>
    </include>
    <exclude>
        <directory>src/Infrastructure/Controller</directory>
        <file>src/Kernel.php</file>
    </exclude>
    <report>
        <html outputDirectory="var/coverage"/>
        <text outputFile="php://stdout"/>
    </report>
</coverage>
```

### Commandes Utiles

```bash
# Exécuter tous les tests
./vendor/bin/phpunit

# Tests avec couverture
./vendor/bin/phpunit --coverage-html var/coverage

# Tests par type
./vendor/bin/phpunit tests/Unit
./vendor/bin/phpunit tests/Integration
./vendor/bin/phpunit tests/EndToEnd

# Tests spécifiques
./vendor/bin/phpunit tests/Unit/Domain/Model/BookTest.php
```

## ✅ Checkpoint - Validation des Acquis

Avant de terminer ce niveau débutant, vérifiez que vous savez :

- [ ] **Organiser** les tests par type et couche
- [ ] **Écrire** des tests unitaires isolés avec mocks
- [ ] **Créer** des tests d'intégration réalistes
- [ ] **Tester** l'API end-to-end
- [ ] **Utiliser** des factories et builders
- [ ] **Mesurer** la couverture de tests

## 🎯 Projet Final Débutant : Bibliothèque Complète

Créez un système de bibliothèque complet avec :

1. **Domain** : Book, Member, Loan (entités et value objects)
2. **Application** : Commands, Queries, Handlers
3. **Infrastructure** : Repositories, API REST
4. **Tests** : Unitaires (70%), Intégration (20%), E2E (10%)

**Critères de réussite** :
- ✅ Couverture de tests > 80%
- ✅ Tous les tests passent
- ✅ API fonctionnelle
- ✅ Code respectant les principes DDD

**Temps estimé** : 3-4 heures

## 🚀 Prochaine Étape

**Félicitations !** 🎉 Vous avez terminé le niveau débutant !

Vous pouvez maintenant passer au **niveau intermédiaire** pour apprendre :
- Les agrégats complexes
- Les services de domaine
- CQRS avec Symfony Messenger
- Les domain events
- L'architecture hexagonale avancée

---

**Bravo !** Vous maîtrisez maintenant les fondamentaux du DDD avec une approche de test solide !
