# 🟢 Application Basics - Commands et Queries

## 🎯 Objectifs de ce Guide

À la fin de ce chapitre, vous saurez :
- ✅ Comprendre la séparation Command/Query
- ✅ Créer des Commands pour modifier l'état
- ✅ Créer des Queries pour lire les données
- ✅ Implémenter des Handlers simples
- ✅ Orchestrer les cas d'usage

**Temps estimé** : 45 minutes

## 📚 Commands vs Queries

### Principe CQRS Simplifié

- **Command** = "Fais quelque chose" (écriture)
- **Query** = "Dis-moi quelque chose" (lecture)

| Command | Query |
|---------|-------|
| Modifie l'état | Lit les données |
| Peut échouer | Ne peut pas échouer |
| Retourne peu/rien | Retourne des données |
| `BorrowBook` | `FindAvailableBooks` |

## 🛠️ Votre Première Command

### Étape 1 : Command pour Emprunter un Livre

```php
<?php
// src/Application/Command/BorrowBookCommand.php

namespace App\Application\Command;

use App\Domain\ValueObject\BookId;
use App\Domain\ValueObject\MemberId;

final readonly class BorrowBookCommand
{
    public function __construct(
        public BookId $bookId,
        public MemberId $memberId
    ) {}
}
```

### Étape 2 : Handler de la Command

```php
<?php
// src/Application/Handler/BorrowBookHandler.php

namespace App\Application\Handler;

use App\Application\Command\BorrowBookCommand;
use App\Domain\Repository\BookRepositoryInterface;
use App\Domain\Repository\MemberRepositoryInterface;

class BorrowBookHandler
{
    public function __construct(
        private BookRepositoryInterface $bookRepository,
        private MemberRepositoryInterface $memberRepository
    ) {}

    public function handle(BorrowBookCommand $command): void
    {
        // 1. Récupérer le livre
        $book = $this->bookRepository->findById($command->bookId);
        if (!$book) {
            throw new \DomainException('Book not found');
        }

        // 2. Récupérer le membre
        $member = $this->memberRepository->findById($command->memberId);
        if (!$member) {
            throw new \DomainException('Member not found');
        }

        // 3. Vérifier que le membre peut emprunter
        if (!$member->canBorrow()) {
            throw new \DomainException('Member cannot borrow books');
        }

        // 4. Emprunter le livre
        $book->borrow();

        // 5. Sauvegarder
        $this->bookRepository->save($book);
    }
}
```

### Étape 3 : Test du Handler

```php
<?php
// tests/Unit/Application/Handler/BorrowBookHandlerTest.php

namespace App\Tests\Unit\Application\Handler;

use App\Application\Command\BorrowBookCommand;
use App\Application\Handler\BorrowBookHandler;
use App\Domain\Repository\BookRepositoryInterface;
use App\Domain\Repository\MemberRepositoryInterface;
use App\Tests\Support\BookFactory;
use App\Tests\Support\MemberFactory;
use PHPUnit\Framework\TestCase;

class BorrowBookHandlerTest extends TestCase
{
    private BookRepositoryInterface $bookRepository;
    private MemberRepositoryInterface $memberRepository;
    private BorrowBookHandler $handler;

    protected function setUp(): void
    {
        $this->bookRepository = $this->createMock(BookRepositoryInterface::class);
        $this->memberRepository = $this->createMock(MemberRepositoryInterface::class);
        $this->handler = new BorrowBookHandler(
            $this->bookRepository,
            $this->memberRepository
        );
    }

    public function testBorrowBookSuccessfully(): void
    {
        // Arrange
        $book = BookFactory::create();
        $member = MemberFactory::create();
        $command = new BorrowBookCommand($book->getId(), $member->getId());

        $this->bookRepository
            ->method('findById')
            ->with($book->getId())
            ->willReturn($book);

        $this->memberRepository
            ->method('findById')
            ->with($member->getId())
            ->willReturn($member);

        $this->bookRepository
            ->expects($this->once())
            ->method('save')
            ->with($book);

        // Act
        $this->handler->handle($command);

        // Assert
        $this->assertTrue($book->isBorrowed());
    }

    public function testBorrowNonExistentBook(): void
    {
        // Arrange
        $member = MemberFactory::create();
        $command = new BorrowBookCommand(
            \App\Domain\ValueObject\BookId::generate(),
            $member->getId()
        );

        $this->bookRepository
            ->method('findById')
            ->willReturn(null);

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Book not found');

        // Act
        $this->handler->handle($command);
    }

    public function testBorrowWithInactiveMember(): void
    {
        // Arrange
        $book = BookFactory::create();
        $member = MemberFactory::createInactive();
        $command = new BorrowBookCommand($book->getId(), $member->getId());

        $this->bookRepository->method('findById')->willReturn($book);
        $this->memberRepository->method('findById')->willReturn($member);

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Member cannot borrow books');

        // Act
        $this->handler->handle($command);
    }
}
```

## 📖 Votre Première Query

### Étape 1 : Query pour Trouver les Livres Disponibles

```php
<?php
// src/Application/Query/FindAvailableBooksQuery.php

namespace App\Application\Query;

final readonly class FindAvailableBooksQuery
{
    public function __construct(
        public ?string $titleSearch = null,
        public ?float $maxPrice = null
    ) {}
}
```

### Étape 2 : DTO pour la Réponse

```php
<?php
// src/Application/DTO/BookDTO.php

namespace App\Application\DTO;

use App\Domain\Model\Book;

final readonly class BookDTO
{
    public function __construct(
        public string $id,
        public string $title,
        public float $price,
        public string $currency,
        public bool $isAvailable
    ) {}

    public static function fromBook(Book $book): self
    {
        return new self(
            id: $book->getId()->value,
            title: $book->getTitle()->value,
            price: $book->getPrice()->amount,
            currency: $book->getPrice()->currency,
            isAvailable: $book->isAvailable()
        );
    }
}
```

### Étape 3 : Handler de la Query

```php
<?php
// src/Application/Handler/FindAvailableBooksHandler.php

namespace App\Application\Handler;

use App\Application\DTO\BookDTO;
use App\Application\Query\FindAvailableBooksQuery;
use App\Domain\Repository\BookRepositoryInterface;

class FindAvailableBooksHandler
{
    public function __construct(
        private BookRepositoryInterface $bookRepository
    ) {}

    /**
     * @return BookDTO[]
     */
    public function handle(FindAvailableBooksQuery $query): array
    {
        // 1. Récupérer les livres disponibles
        $books = $this->bookRepository->findAvailable();

        // 2. Filtrer par titre si demandé
        if ($query->titleSearch) {
            $books = array_filter(
                $books,
                fn($book) => str_contains(
                    strtolower($book->getTitle()->value),
                    strtolower($query->titleSearch)
                )
            );
        }

        // 3. Filtrer par prix si demandé
        if ($query->maxPrice) {
            $books = array_filter(
                $books,
                fn($book) => $book->getPrice()->amount <= $query->maxPrice
            );
        }

        // 4. Convertir en DTOs
        return array_map(
            fn($book) => BookDTO::fromBook($book),
            array_values($books)
        );
    }
}
```

### Étape 4 : Test du Query Handler

```php
<?php
// tests/Unit/Application/Handler/FindAvailableBooksHandlerTest.php

namespace App\Tests\Unit\Application\Handler;

use App\Application\Handler\FindAvailableBooksHandler;
use App\Application\Query\FindAvailableBooksQuery;
use App\Domain\Repository\BookRepositoryInterface;
use App\Tests\Support\BookFactory;
use PHPUnit\Framework\TestCase;

class FindAvailableBooksHandlerTest extends TestCase
{
    private BookRepositoryInterface $bookRepository;
    private FindAvailableBooksHandler $handler;

    protected function setUp(): void
    {
        $this->bookRepository = $this->createMock(BookRepositoryInterface::class);
        $this->handler = new FindAvailableBooksHandler($this->bookRepository);
    }

    public function testFindAllAvailableBooks(): void
    {
        // Arrange
        $books = [
            BookFactory::create('Book 1', 10.0),
            BookFactory::create('Book 2', 20.0),
        ];

        $this->bookRepository
            ->method('findAvailable')
            ->willReturn($books);

        $query = new FindAvailableBooksQuery();

        // Act
        $result = $this->handler->handle($query);

        // Assert
        $this->assertCount(2, $result);
        $this->assertSame('Book 1', $result[0]->title);
        $this->assertSame('Book 2', $result[1]->title);
    }

    public function testFindBooksWithTitleFilter(): void
    {
        // Arrange
        $books = [
            BookFactory::create('Clean Code', 30.0),
            BookFactory::create('Dirty Code', 25.0),
            BookFactory::create('Refactoring', 35.0),
        ];

        $this->bookRepository
            ->method('findAvailable')
            ->willReturn($books);

        $query = new FindAvailableBooksQuery(titleSearch: 'Clean');

        // Act
        $result = $this->handler->handle($query);

        // Assert
        $this->assertCount(1, $result);
        $this->assertSame('Clean Code', $result[0]->title);
    }

    public function testFindBooksWithPriceFilter(): void
    {
        // Arrange
        $books = [
            BookFactory::create('Cheap Book', 15.0),
            BookFactory::create('Expensive Book', 50.0),
        ];

        $this->bookRepository
            ->method('findAvailable')
            ->willReturn($books);

        $query = new FindAvailableBooksQuery(maxPrice: 20.0);

        // Act
        $result = $this->handler->handle($query);

        // Assert
        $this->assertCount(1, $result);
        $this->assertSame('Cheap Book', $result[0]->title);
    }
}
```

## 🎯 Exercice Pratique 1 : ReturnBookCommand

Créez une command `ReturnBookCommand` avec son handler qui :
- Prend un `BookId` en paramètre
- Trouve le livre et le retourne
- Gère les cas d'erreur (livre non trouvé, déjà disponible)

<details>
<summary>💡 Solution</summary>

```php
<?php
// src/Application/Command/ReturnBookCommand.php

namespace App\Application\Command;

use App\Domain\ValueObject\BookId;

final readonly class ReturnBookCommand
{
    public function __construct(public BookId $bookId) {}
}
```

```php
<?php
// src/Application/Handler/ReturnBookHandler.php

namespace App\Application\Handler;

use App\Application\Command\ReturnBookCommand;
use App\Domain\Repository\BookRepositoryInterface;

class ReturnBookHandler
{
    public function __construct(
        private BookRepositoryInterface $bookRepository
    ) {}

    public function handle(ReturnBookCommand $command): void
    {
        $book = $this->bookRepository->findById($command->bookId);
        if (!$book) {
            throw new \DomainException('Book not found');
        }

        $book->return();
        $this->bookRepository->save($book);
    }
}
```
</details>

## 🔄 Service d'Application Simple

### Orchestrateur de Cas d'Usage

```php
<?php
// src/Application/Service/LibraryService.php

namespace App\Application\Service;

use App\Application\Command\BorrowBookCommand;
use App\Application\Command\ReturnBookCommand;
use App\Application\Handler\BorrowBookHandler;
use App\Application\Handler\ReturnBookHandler;
use App\Application\Handler\FindAvailableBooksHandler;
use App\Application\Query\FindAvailableBooksQuery;

class LibraryService
{
    public function __construct(
        private BorrowBookHandler $borrowBookHandler,
        private ReturnBookHandler $returnBookHandler,
        private FindAvailableBooksHandler $findAvailableBooksHandler
    ) {}

    public function borrowBook(BorrowBookCommand $command): void
    {
        $this->borrowBookHandler->handle($command);
    }

    public function returnBook(ReturnBookCommand $command): void
    {
        $this->returnBookHandler->handle($command);
    }

    public function findAvailableBooks(FindAvailableBooksQuery $query): array
    {
        return $this->findAvailableBooksHandler->handle($query);
    }
}
```

## 🧪 Test d'Intégration

```php
<?php
// tests/Integration/LibraryServiceTest.php

namespace App\Tests\Integration;

use App\Application\Command\BorrowBookCommand;
use App\Application\Query\FindAvailableBooksQuery;
use App\Application\Service\LibraryService;
use App\Infrastructure\Repository\InMemoryBookRepository;
use App\Infrastructure\Repository\InMemoryMemberRepository;
use App\Tests\Support\BookFactory;
use App\Tests\Support\MemberFactory;
use PHPUnit\Framework\TestCase;

class LibraryServiceTest extends TestCase
{
    private LibraryService $libraryService;
    private InMemoryBookRepository $bookRepository;
    private InMemoryMemberRepository $memberRepository;

    protected function setUp(): void
    {
        $this->bookRepository = new InMemoryBookRepository();
        $this->memberRepository = new InMemoryMemberRepository();

        // Créer les handlers avec les vrais repositories
        $borrowHandler = new \App\Application\Handler\BorrowBookHandler(
            $this->bookRepository,
            $this->memberRepository
        );
        $returnHandler = new \App\Application\Handler\ReturnBookHandler(
            $this->bookRepository
        );
        $findHandler = new \App\Application\Handler\FindAvailableBooksHandler(
            $this->bookRepository
        );

        $this->libraryService = new LibraryService(
            $borrowHandler,
            $returnHandler,
            $findHandler
        );
    }

    public function testCompleteLibraryWorkflow(): void
    {
        // 1. Préparer les données
        $book = BookFactory::create('Test Book');
        $member = MemberFactory::create();

        $this->bookRepository->save($book);
        $this->memberRepository->save($member);

        // 2. Vérifier que le livre est disponible
        $availableBooks = $this->libraryService->findAvailableBooks(
            new FindAvailableBooksQuery()
        );
        $this->assertCount(1, $availableBooks);

        // 3. Emprunter le livre
        $this->libraryService->borrowBook(
            new BorrowBookCommand($book->getId(), $member->getId())
        );

        // 4. Vérifier qu'il n'est plus disponible
        $availableBooks = $this->libraryService->findAvailableBooks(
            new FindAvailableBooksQuery()
        );
        $this->assertCount(0, $availableBooks);

        // 5. Retourner le livre
        $this->libraryService->returnBook(
            new \App\Application\Command\ReturnBookCommand($book->getId())
        );

        // 6. Vérifier qu'il est à nouveau disponible
        $availableBooks = $this->libraryService->findAvailableBooks(
            new FindAvailableBooksQuery()
        );
        $this->assertCount(1, $availableBooks);
    }
}
```

## ✅ Checkpoint - Validation des Acquis

Avant de passer au guide suivant, vérifiez que vous savez :

- [ ] **Créer** des Commands pour les actions
- [ ] **Créer** des Queries pour les lectures
- [ ] **Implémenter** des Handlers
- [ ] **Utiliser** des DTOs pour les réponses
- [ ] **Orchestrer** les cas d'usage

## 🔍 QueryBuilder Pattern

### Étape 1 : Interface QueryBuilder

```php
<?php
// src/Application/Query/Builder/BookQueryBuilder.php

namespace App\Application\Query\Builder;

use App\Application\DTO\BookDTO;

class BookQueryBuilder
{
    private ?string $titleSearch = null;
    private ?string $authorSearch = null;
    private ?bool $availableOnly = null;
    private ?float $minPrice = null;
    private ?float $maxPrice = null;
    private ?string $sortBy = null;
    private string $sortDirection = 'ASC';
    private ?int $limit = null;
    private int $offset = 0;

    public function withTitle(string $title): self
    {
        $this->titleSearch = $title;
        return $this;
    }

    public function withAuthor(string $author): self
    {
        $this->authorSearch = $author;
        return $this;
    }

    public function onlyAvailable(): self
    {
        $this->availableOnly = true;
        return $this;
    }

    public function onlyBorrowed(): self
    {
        $this->availableOnly = false;
        return $this;
    }

    public function withPriceRange(float $min, float $max): self
    {
        $this->minPrice = $min;
        $this->maxPrice = $max;
        return $this;
    }

    public function sortBy(string $field, string $direction = 'ASC'): self
    {
        $this->sortBy = $field;
        $this->sortDirection = strtoupper($direction);
        return $this;
    }

    public function limit(int $limit): self
    {
        $this->limit = $limit;
        return $this;
    }

    public function offset(int $offset): self
    {
        $this->offset = $offset;
        return $this;
    }

    public function build(): FindBooksQuery
    {
        return new FindBooksQuery(
            titleSearch: $this->titleSearch,
            authorSearch: $this->authorSearch,
            availableOnly: $this->availableOnly,
            minPrice: $this->minPrice,
            maxPrice: $this->maxPrice,
            sortBy: $this->sortBy,
            sortDirection: $this->sortDirection,
            limit: $this->limit,
            offset: $this->offset
        );
    }
}
```

### Étape 2 : Query Enrichie

```php
<?php
// Modifier FindBooksQuery pour supporter plus d'options

namespace App\Application\Query;

final readonly class FindBooksQuery
{
    public function __construct(
        public ?string $titleSearch = null,
        public ?string $authorSearch = null,
        public ?bool $availableOnly = null,
        public ?float $minPrice = null,
        public ?float $maxPrice = null,
        public ?string $sortBy = null,
        public string $sortDirection = 'ASC',
        public ?int $limit = null,
        public int $offset = 0
    ) {}
}
```

### Étape 3 : Utilisation du QueryBuilder

```php
<?php
// Dans un contrôleur ou service

public function searchBooks(): array
{
    $query = (new BookQueryBuilder())
        ->withTitle('Clean')
        ->withPriceRange(10.0, 50.0)
        ->onlyAvailable()
        ->sortBy('title', 'ASC')
        ->limit(10)
        ->build();

    return $this->findBooksHandler->handle($query);
}
```

### Étape 4 : Test du QueryBuilder

```php
<?php
// tests/Unit/Application/Query/Builder/BookQueryBuilderTest.php

namespace App\Tests\Unit\Application\Query\Builder;

use App\Application\Query\Builder\BookQueryBuilder;
use PHPUnit\Framework\TestCase;

class BookQueryBuilderTest extends TestCase
{
    public function testBuildSimpleQuery(): void
    {
        $query = (new BookQueryBuilder())
            ->withTitle('Clean Code')
            ->build();

        $this->assertSame('Clean Code', $query->titleSearch);
        $this->assertNull($query->authorSearch);
    }

    public function testBuildComplexQuery(): void
    {
        $query = (new BookQueryBuilder())
            ->withTitle('Clean')
            ->withAuthor('Martin')
            ->onlyAvailable()
            ->withPriceRange(20.0, 40.0)
            ->sortBy('price', 'DESC')
            ->limit(5)
            ->offset(10)
            ->build();

        $this->assertSame('Clean', $query->titleSearch);
        $this->assertSame('Martin', $query->authorSearch);
        $this->assertTrue($query->availableOnly);
        $this->assertSame(20.0, $query->minPrice);
        $this->assertSame(40.0, $query->maxPrice);
        $this->assertSame('price', $query->sortBy);
        $this->assertSame('DESC', $query->sortDirection);
        $this->assertSame(5, $query->limit);
        $this->assertSame(10, $query->offset);
    }

    public function testFluentInterface(): void
    {
        $builder = new BookQueryBuilder();

        $result = $builder->withTitle('Test');

        $this->assertSame($builder, $result);
    }
}
```

## 🎯 Mini-Projet : Gestion de Réservations

Créez un système de réservations avec :

1. **Commands** :
   - `ReserveBookCommand`
   - `CancelReservationCommand`

2. **Queries avec QueryBuilder** :
   - `ReservationQueryBuilder` pour recherches complexes
   - `FindMemberReservationsQuery`
   - `FindBookReservationsQuery`

3. **Tests** complets pour chaque handler et builder

**Temps estimé** : 90 minutes

## 🚀 Prochaine Étape

Une fois ce guide maîtrisé, passez à **[05-infrastructure-basics.md](05-infrastructure-basics.md)** pour apprendre :
- La persistance avec Doctrine
- La configuration des services
- L'injection de dépendances

---

**Excellent !** 🎉 Vous savez maintenant orchestrer vos cas d'usage avec Commands et Queries !
