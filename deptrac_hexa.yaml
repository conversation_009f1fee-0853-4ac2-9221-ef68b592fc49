parameters:
    paths:
        - ./src/BookStore
        - ./src/Shared

    layers:
        - name: Domain
          collectors:
              - type: directory
                regex: .+/Domain/.*

        - name: Application
          collectors:
              - type: directory
                regex: .+/Application/.*

        - name: Infrastructure
          collectors:
              - type: directory
                regex: .+/Infrastructure/.*

        - name: Vendors
          collectors:
              - { type: className, regex: ^ApiPlatform\\ }
              - { type: className, regex: ^Symfony\\(?!(Component\\Uid\\)) }
              - { type: className, regex: ^Doctrine\\(?!(ORM\\Mapping)) }
              - { type: className, regex: ^Webmozart\\(?!Assert\\Assert) }

        - name: Attributes
          collectors:
              - { type: className, regex: ^Doctrine\\ORM\\Mapping }

        - name: Helpers
          collectors:
              - { type: className, regex: ^Symfony\\Component\\Uid\\ }
              - { type: className, regex: ^Webmozart\\Assert\\Assert }

    ruleset:
        Domain: 
            - Helpers
            - Attributes

        Application: 
            - Domain
            - Helpers
            - Attributes

        Infrastructure: 
            - Domain
            - Application
            - Vendors
            - Helpers
            - Attributes
